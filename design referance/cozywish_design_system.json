{"meta": {"name": "CozyWish Design System", "description": "Professional design system for CozyWish spa and wellness marketplace - Bootstrap 5 implementation", "version": "2.0.0", "bootstrap": "5.3", "lastUpdated": "2025-06-23", "author": "CozyWish Design Team", "usage": "This design system provides comprehensive styling guidelines for AI-assisted development using Bootstrap 5 classes only"}, "brandIdentity": {"name": "CozyWish", "tagline": "Find & Book Local Spa and Massage Services", "personality": ["warm", "relaxing", "professional", "trustworthy", "premium"], "targetAudience": "Adults seeking wellness and relaxation services", "brandValues": ["wellness", "quality", "accessibility", "trust", "comfort"]}, "tokens": {"colors": {"brand": {"primary": "#2F160F", "primaryLight": "#4a2a1f", "primaryDark": "#1a0d08", "accent": "#fae1d7", "accentLight": "#fef7f0", "accentDark": "#f1d4c4"}, "primary": {"50": "#fef7f0", "100": "#fdeee0", "200": "#fad9c0", "300": "#f6be95", "400": "#f19968", "500": "#ed7544", "600": "#de5a2c", "700": "#b84622", "800": "#933a22", "900": "#76321f", "950": "#40180e"}, "secondary": {"50": "#f9f7f4", "100": "#f1ebe2", "200": "#e3d5c4", "300": "#d1b89e", "400": "#bc9876", "500": "#ad7f5a", "600": "#a0704e", "700": "#855a42", "800": "#6c4a39", "900": "#583d30", "950": "#2f1f18"}, "neutral": {"50": "#fafafa", "100": "#f5f5f5", "200": "#e5e5e5", "300": "#d4d4d4", "400": "#a3a3a3", "500": "#737373", "600": "#525252", "700": "#404040", "800": "#262626", "900": "#171717", "950": "#0a0a0a"}, "semantic": {"success": "#059669", "warning": "#d97706", "error": "#dc2626", "info": "#0284c7"}, "gradients": {"hero": "radial-gradient(ellipse at center, #fae1d7 40%, #fef7f0 70%, #ffffff 100%)", "heroAlt": "radial-gradient(ellipse at center, #fef7f0 0%, #fdeee0 40%, #ffffff 100%)", "card": "linear-gradient(135deg, #ffffff 0%, #fae1d7 100%)", "cardSubtle": "linear-gradient(135deg, #ffffff 0%, #fef7f0 100%)", "button": "linear-gradient(135deg, #ed7544 0%, #de5a2c 100%)", "brandButton": "linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%)", "accent": "linear-gradient(135deg, #fae1d7 0%, #f1d4c4 100%)"}}, "typography": {"fontFamilies": {"primary": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif", "heading": "'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif", "display": "'Playfair Display', Georgia, 'Times New Roman', serif"}, "fontSizes": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem", "4xl": "2.25rem", "5xl": "3rem", "6xl": "3.75rem", "7xl": "4.5rem"}, "fontWeights": {"light": "300", "normal": "400", "medium": "500", "semibold": "600", "bold": "700", "extrabold": "800"}, "lineHeights": {"tight": "1.25", "snug": "1.375", "normal": "1.5", "relaxed": "1.625", "loose": "2"}}, "spacing": {"0": "0", "px": "1px", "0.5": "0.125rem", "1": "0.25rem", "1.5": "0.375rem", "2": "0.5rem", "2.5": "0.625rem", "3": "0.75rem", "3.5": "0.875rem", "4": "1rem", "5": "1.25rem", "6": "1.5rem", "7": "1.75rem", "8": "2rem", "9": "2.25rem", "10": "2.5rem", "11": "2.75rem", "12": "3rem", "14": "3.5rem", "16": "4rem", "20": "5rem", "24": "6rem", "28": "7rem", "32": "8rem"}, "borderRadius": {"none": "0", "sm": "0.125rem", "base": "0.25rem", "md": "0.375rem", "lg": "0.5rem", "xl": "0.75rem", "2xl": "1rem", "3xl": "1.5rem", "full": "9999px"}, "shadows": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "base": "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)", "2xl": "0 25px 50px -12px rgba(0, 0, 0, 0.25)", "inner": "inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)"}}, "components": {"buttons": {"primary": {"classes": "btn btn-primary", "customStyles": "background: linear-gradient(135deg, #ed7544 0%, #de5a2c 100%); border: none; border-radius: 0.5rem; font-weight: 600; padding: 0.75rem 1.5rem; transition: all 0.2s ease;", "hoverStyles": "transform: translateY(-1px); box-shadow: 0 4px 12px rgba(237, 117, 68, 0.3);", "usage": "Primary actions, CTAs, form submissions"}, "brand": {"classes": "btn btn-primary", "customStyles": "background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%); border: none; border-radius: 0.5rem; font-weight: 600; padding: 0.75rem 1.5rem; transition: all 0.2s ease; color: white;", "hoverStyles": "transform: translateY(-1px); box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);", "usage": "Brand-specific actions, hero CTAs, important submissions"}, "secondary": {"classes": "btn btn-outline-primary", "customStyles": "border: 2px solid #ed7544; color: #ed7544; border-radius: 0.5rem; font-weight: 600; padding: 0.75rem 1.5rem; background: white;", "hoverStyles": "background: #ed7544; color: white; transform: translateY(-1px);", "usage": "Secondary actions, alternative options"}, "brandOutline": {"classes": "btn btn-outline-primary", "customStyles": "border: 2px solid #2F160F; color: #2F160F; border-radius: 0.5rem; font-weight: 600; padding: 0.75rem 1.5rem; background: white;", "hoverStyles": "background: #2F160F; color: white; transform: translateY(-1px);", "usage": "Brand secondary actions, subtle brand presence"}, "ghost": {"classes": "btn btn-link", "customStyles": "color: #ed7544; text-decoration: none; font-weight: 500; padding: 0.5rem 1rem;", "hoverStyles": "color: #de5a2c; text-decoration: underline;", "usage": "Tertiary actions, navigation links"}, "social": {"classes": "btn btn-outline-secondary", "customStyles": "border: 1px solid #e5e5e5; border-radius: 50%; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center;", "hoverStyles": "border-color: #ed7544; color: #ed7544;", "usage": "Social login buttons"}, "sizes": {"sm": "btn-sm px-3 py-2", "md": "px-4 py-2", "lg": "btn-lg px-5 py-3", "xl": "px-6 py-4"}}, "forms": {"input": {"classes": "form-control", "customStyles": "border: 2px solid #e5e5e5; border-radius: 0.5rem; padding: 0.75rem 1rem; font-size: 1rem; transition: all 0.2s ease;", "focusStyles": "border-color: #ed7544; box-shadow: 0 0 0 0.2rem rgba(237, 117, 68, 0.1);", "errorStyles": "border-color: #dc2626; box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);"}, "select": {"classes": "form-select", "customStyles": "border: 2px solid #e5e5e5; border-radius: 0.5rem; padding: 0.75rem 1rem; font-size: 1rem;", "focusStyles": "border-color: #ed7544; box-shadow: 0 0 0 0.2rem rgba(237, 117, 68, 0.1);"}, "textarea": {"classes": "form-control", "customStyles": "border: 2px solid #e5e5e5; border-radius: 0.5rem; padding: 0.75rem 1rem; font-size: 1rem; resize: vertical;", "focusStyles": "border-color: #ed7544; box-shadow: 0 0 0 0.2rem rgba(237, 117, 68, 0.1);"}, "label": {"classes": "form-label", "customStyles": "font-weight: 600; color: #404040; margin-bottom: 0.5rem;"}, "helpText": {"classes": "form-text", "customStyles": "color: #737373; font-size: 0.875rem;"}}, "cards": {"default": {"classes": "card", "customStyles": "border: 1px solid #e5e5e5; border-radius: 1rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); background: white; overflow: hidden;", "hoverStyles": "transform: translateY(-2px); box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);"}, "featured": {"classes": "card", "customStyles": "border: 2px solid #ed7544; border-radius: 1rem; box-shadow: 0 10px 15px -3px rgba(237, 117, 68, 0.1); background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);"}, "brand": {"classes": "card", "customStyles": "border: 2px solid #2F160F; border-radius: 1rem; box-shadow: 0 10px 15px -3px rgba(47, 22, 15, 0.1); background: linear-gradient(135deg, #ffffff 0%, #fae1d7 100%);"}, "accent": {"classes": "card", "customStyles": "border: 1px solid #fae1d7; border-radius: 1rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05); background: #fae1d7; overflow: hidden;"}, "service": {"classes": "card h-100", "customStyles": "border: none; border-radius: 1rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); overflow: hidden; transition: all 0.3s ease;", "hoverStyles": "transform: translateY(-4px); box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);"}}, "navigation": {"navbar": {"classes": "navbar navbar-expand-lg", "lightVariant": "bg-white shadow-sm", "transparentVariant": "bg-transparent", "brandClasses": "navbar-brand fw-bold", "brandStyles": "font-family: '<PERSON><PERSON><PERSON>', sans-serif; font-size: 1.5rem; color: #2f1f18;"}, "navLink": {"classes": "nav-link", "customStyles": "font-weight: 500; color: #404040; padding: 0.5rem 1rem; transition: color 0.2s ease;", "hoverStyles": "color: #ed7544;", "activeStyles": "color: #ed7544; font-weight: 600;"}}, "alerts": {"success": {"classes": "alert alert-success", "customStyles": "background: #f0fdf4; border: 1px solid #bbf7d0; color: #166534; border-radius: 0.5rem;"}, "warning": {"classes": "alert alert-warning", "customStyles": "background: #fffbeb; border: 1px solid #fed7aa; color: #92400e; border-radius: 0.5rem;"}, "error": {"classes": "alert alert-danger", "customStyles": "background: #fef2f2; border: 1px solid #fecaca; color: #991b1b; border-radius: 0.5rem;"}, "info": {"classes": "alert alert-info", "customStyles": "background: #eff6ff; border: 1px solid #bfdbfe; color: #1e40af; border-radius: 0.5rem;"}}, "badges": {"primary": {"classes": "badge", "customStyles": "background: #ed7544; color: white; font-weight: 500; padding: 0.25rem 0.75rem; border-radius: 9999px;"}, "secondary": {"classes": "badge", "customStyles": "background: #f5f5f5; color: #404040; font-weight: 500; padding: 0.25rem 0.75rem; border-radius: 9999px;"}, "success": {"classes": "badge", "customStyles": "background: #059669; color: white; font-weight: 500; padding: 0.25rem 0.75rem; border-radius: 9999px;"}}, "modals": {"default": {"classes": "modal fade", "dialogClasses": "modal-dialog modal-dialog-centered", "contentClasses": "modal-content", "customStyles": "border: none; border-radius: 1rem; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);"}, "large": {"classes": "modal fade", "dialogClasses": "modal-dialog modal-lg modal-dialog-centered", "contentClasses": "modal-content"}}}, "layouts": {"hero": {"containerClasses": "container-fluid", "backgroundStyle": "background: radial-gradient(ellipse at center, #fef7f0 0%, #fdeee0 40%, #ffffff 100%); min-height: 100vh;", "contentClasses": "d-flex align-items-center justify-content-center text-center py-5"}, "section": {"containerClasses": "container", "spacingClasses": "py-5 my-4", "titleClasses": "h2 text-center mb-5", "titleStyles": "font-family: '<PERSON><PERSON>s', sans-serif; font-weight: 700; color: #2f1f18;"}, "grid": {"containerClasses": "container", "rowClasses": "row g-4", "colClasses": "col-lg-4 col-md-6 col-sm-12"}}, "utilities": {"textColors": {"brand": "color: #2F160F;", "brandLight": "color: #4a2a1f;", "primary": "color: #2f1f18;", "secondary": "color: #583d30;", "accent": "color: #ed7544;", "muted": "color: #737373;", "light": "color: #a3a3a3;"}, "backgroundColors": {"brand": "background-color: #2F160F;", "brandAccent": "background-color: #fae1d7;", "brandAccentLight": "background-color: #fef7f0;", "primary": "background-color: #ed7544;", "light": "background-color: #fef7f0;", "white": "background-color: #ffffff;", "gradient": "background: radial-gradient(ellipse at center, #fae1d7 40%, #fef7f0 70%, #ffffff 100%);", "gradientAlt": "background: radial-gradient(ellipse at center, #fef7f0 0%, #fdeee0 40%, #ffffff 100%);"}, "spacing": {"sectionPadding": "py-5", "cardPadding": "p-4", "buttonPadding": "px-4 py-2", "inputPadding": "px-3 py-2"}, "borders": {"light": "border: 1px solid #e5e5e5;", "brand": "border: 2px solid #2F160F;", "brandLight": "border: 1px solid #2F160F;", "accent": "border: 1px solid #fae1d7;", "accentThick": "border: 2px solid #fae1d7;", "primary": "border: 2px solid #ed7544;", "rounded": "border-radius: 0.5rem;", "roundedLg": "border-radius: 1rem;", "pill": "border-radius: 9999px;"}}, "breakpoints": {"xs": "0px", "sm": "576px", "md": "768px", "lg": "992px", "xl": "1200px", "xxl": "1400px"}, "accessibility": {"focusStyles": "outline: 2px solid #ed7544; outline-offset: 2px;", "skipLink": "position: absolute; left: -9999px; top: auto; width: 1px; height: 1px; overflow: hidden;", "skipLinkFocus": "position: static; width: auto; height: auto; overflow: visible; background: #ed7544; color: white; padding: 0.5rem 1rem; text-decoration: none;", "colorContrast": {"primaryOnWhite": "4.5:1", "secondaryOnWhite": "4.5:1", "whiteOnPrimary": "4.5:1"}}}