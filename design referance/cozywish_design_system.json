{"meta": {"name": "OneTooOne Design System", "description": "Visual tokens & component conventions for Django/Bootstrap 5 templates", "version": "1.0.0", "bootstrap": "5.3"}, "tokens": {"colors": {"brandPrimary": "#4e2813", "brandPrimaryHover": "#3d1f09", "brandText": "#2F160F", "accent": "#f68b4e", "bgGradientStart": "#fff8f5", "bgGradientMid": "#fae1d7", "bgGradientEnd": "#ffffff", "borderLight": "rgba(0,0,0,0.10)", "textMuted": "#666666"}, "fonts": {"primary": "'Roboto', sans-serif", "display": "'Yeseva One', serif"}, "fontSizes": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.25rem", "xl": "1.5rem", "display": "4rem"}, "spacing": {"0": "0", "1": "0.25rem", "2": "0.5rem", "3": "0.75rem", "4": "1rem", "5": "1.5rem", "6": "2rem", "7": "3rem"}, "radii": {"sm": "0.375rem", "md": "0.5rem", "pill": "100px"}, "shadows": {"elev1": "0 4px 12px rgba(0,0,0,0.10)"}}, "components": {"button": {"baseClasses": "btn", "variants": {"brand": "btn-brand", "outlineBrand": "btn-outline-brand", "social": "btn social-btn"}}, "input": {"baseClasses": "form-control", "radius": "md", "placeholderColor": "textMuted"}, "card": {"baseClasses": "card", "padding": "p-4", "radius": "md", "shadow": "elev1"}, "pageGradient": {"class": "bg-radial", "definition": "radial-gradient(circle at center, var(--color-bg-gradient-mid) 40%, var(--color-bg-gradient-end) 100%)"}}, "utilities": {"textBrand": "text-brand", "linkAccent": "link-accent"}}