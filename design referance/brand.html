<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>OneTooOne - Component Gallery</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">

  <!-- Bootstrap 5 -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Bootstrap Icons (for demo social buttons, nav icons, etc.) -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Yeseva+One&display=swap" rel="stylesheet">

  <!-- 🎨  Design-system variables + 2 helpers + btn variants -->
  <style>
    :root{
      --color-brand-primary:#4e2813;
      --color-brand-primary-hover:#3d1f09;
      --color-brand-text:#2F160F;
      --color-accent:#f68b4e;
      --color-bg-gradient-mid:#fae1d7;
      --color-bg-gradient-end:#ffffff;
      --shadow-elev1:0 4px 12px rgba(0,0,0,.1);
      --radius-sm:0.375rem;
      --radius-md:0.5rem;
      --radius-pill:100px;
      --font-display:'Yeseva One',serif;
    }

    body.bg-radial{
      min-height:100vh;
      background:radial-gradient(circle at center,
                 var(--color-bg-gradient-mid) 40%,
                 var(--color-bg-gradient-end) 100%);
    }

    .text-brand{color:var(--color-brand-text)!important;}
    .link-accent{color:var(--color-accent);text-decoration:none;}
    .link-accent:hover{text-decoration:underline;}

    .btn-brand{
      background:var(--color-brand-primary);
      color:#fff;border:none;border-radius:var(--radius-pill);
    }
    .btn-brand:hover{background:var(--color-brand-primary-hover);color:#fff;}

    .btn-outline-brand{
      border:1px solid var(--color-brand-primary);
      color:var(--color-brand-primary);
      border-radius:var(--radius-pill);
    }
    .btn-outline-brand:hover{background:var(--color-brand-primary);color:#fff;}

    /* card radius/shadow helper */
    .card-elev{border-radius:var(--radius-md);box-shadow:var(--shadow-elev1);}
  </style>
</head>

<body class="bg-radial">

<!-- **** NAVBAR **** -->
<nav class="navbar navbar-expand-lg bg-white shadow-sm">
  <div class="container">
    <a class="navbar-brand fw-bold text-brand" style="font-family:var(--font-display);" href="#">OneTooOne</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNav">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="mainNav">
      <ul class="navbar-nav ms-auto">
        <li class="nav-item"><a class="nav-link active" href="#">Home</a></li>
        <li class="nav-item"><a class="nav-link" href="#forms">Forms</a></li>
        <li class="nav-item"><a class="nav-link" href="#lists">Components</a></li>
        <li class="nav-item"><a class="nav-link" href="#footer">Contact</a></li>
      </ul>
      <button class="btn btn-brand btn-sm ms-lg-3">Sign up</button>
    </div>
  </div>
</nav>

<!-- **** HERO **** -->
<header class="py-7 text-center">
  <div class="container">
    <h1 class="display-4 fw-bold text-brand" style="font-family:var(--font-display);">Your Design System in Action</h1>
    <p class="lead mb-4">All components below inherit the same tokens, so you can drop them into any Django template and stay perfectly on-brand.</p>
    <button class="btn btn-brand btn-lg me-2">Primary&nbsp;CTA</button>
    <button class="btn btn-outline-brand btn-lg">Secondary CTA</button>
  </div>
</header>

<!-- **** BUTTONS & BADGES **** -->
<section class="py-5" id="lists">
  <div class="container">
    <h2 class="h4 fw-bold text-brand mb-4">Buttons & Badges</h2>
    <div class="d-flex flex-wrap gap-2">
      <button class="btn btn-brand">Brand</button>
      <button class="btn btn-outline-brand">Outline</button>
      <button class="btn btn-secondary">Secondary</button>
      <button class="btn btn-light">Light</button>
      <button class="btn btn-link link-accent">Link Accent</button>
      <span class="badge bg-secondary me-1">Tag</span>
      <span class="badge bg-brand text-light" style="background:var(--color-brand-primary);">Brand Badge</span>
    </div>
  </div>
</section>

<!-- **** FORMS **** -->
<section class="py-5 bg-white" id="forms">
  <div class="container">
    <h2 class="h4 fw-bold text-brand mb-3">Form Controls</h2>
    <form class="row g-3">
      <div class="col-md-6">
        <label class="form-label">Name</label>
        <input class="form-control" placeholder="Jane Doe">
      </div>
      <div class="col-md-6">
        <label class="form-label">Email</label>
        <input type="email" class="form-control" placeholder="<EMAIL>">
      </div>
      <div class="col-12">
        <label class="form-label">Message</label>
        <textarea class="form-control" rows="3" placeholder="Tell us anything…"></textarea>
      </div>

      <div class="col-12 col-md-4">
        <label class="form-label">Select plan</label>
        <select class="form-select">
          <option>Free</option><option>Pro</option><option>Enterprise</option>
        </select>
      </div>
      <div class="col-6 col-md-4 pt-4">
        <div class="form-check">
          <input class="form-check-input" type="checkbox" id="tos">
          <label class="form-check-label" for="tos">I accept terms</label>
        </div>
      </div>
      <div class="col-6 col-md-4 pt-4">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="news">
          <label class="form-check-label" for="news">Subscribe</label>
        </div>
      </div>

      <div class="col-12">
        <button type="submit" class="btn btn-brand w-100">Submit</button>
      </div>
    </form>
  </div>
</section>

<!-- **** CARD, TABLE, TABS **** -->
<section class="py-5">
  <div class="container">
    <h2 class="h4 fw-bold text-brand mb-4">Content Blocks</h2>

    <div class="row g-4">
      <!-- Card -->
      <div class="col-md-4">
        <div class="card card-elev h-100">
          <img src="https://picsum.photos/600/400?random=1" class="card-img-top" alt="">
          <div class="card-body">
            <h5 class="card-title text-brand">Card Title</h5>
            <p class="card-text">Quick example card using system shadow/radius.</p>
            <a href="#" class="btn btn-outline-brand">Go somewhere</a>
          </div>
        </div>
      </div>

      <!-- Table -->
      <div class="col-md-8">
        <ul class="nav nav-tabs mb-3" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#tabTable" type="button" role="tab">Table</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#tabAlert" type="button" role="tab">Alerts</button>
          </li>
        </ul>

        <div class="tab-content">
          <div class="tab-pane fade show active" id="tabTable" role="tabpanel">
            <div class="table-responsive">
              <table class="table align-middle">
                <thead class="table-light">
                  <tr><th>#</th><th>Name</th><th>Status</th><th class="text-end">Actions</th></tr>
                </thead>
                <tbody>
                  <tr><td>1</td><td>Alpha</td><td><span class="badge bg-success">Active</span></td><td class="text-end"><button class="btn btn-sm btn-outline-brand">View</button></td></tr>
                  <tr><td>2</td><td>Beta</td><td><span class="badge bg-warning text-dark">Pending</span></td><td class="text-end"><button class="btn btn-sm btn-outline-brand">View</button></td></tr>
                  <tr><td>3</td><td>Gamma</td><td><span class="badge bg-secondary">Closed</span></td><td class="text-end"><button class="btn btn-sm btn-outline-brand">View</button></td></tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="tab-pane fade" id="tabAlert" role="tabpanel">
            <div class="alert alert-success d-flex align-items-center" role="alert">
              <i class="bi bi-check-circle-fill me-2"></i>Success message
            </div>
            <div class="alert alert-warning d-flex align-items-center" role="alert">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>Warning message
            </div>
            <div class="alert alert-danger d-flex align-items-center" role="alert">
              <i class="bi bi-x-octagon-fill me-2"></i>Error message
            </div>
          </div>
        </div>
      </div>
    </div><!-- /row -->
  </div>
</section>

<!-- **** PAGINATION & BREADCRUMB **** -->
<section class="py-5 bg-white">
  <div class="container">
    <h2 class="h4 fw-bold text-brand mb-3">Navigation patterns</h2>

    <nav aria-label="breadcrumb">
      <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="#" class="link-accent">Home</a></li>
        <li class="breadcrumb-item"><a href="#">Library</a></li>
        <li class="breadcrumb-item active" aria-current="page">Data</li>
      </ol>
    </nav>

    <nav aria-label="Page navigation example">
      <ul class="pagination mb-0">
        <li class="page-item disabled"><span class="page-link">Prev</span></li>
        <li class="page-item active"><span class="page-link">1</span></li>
        <li class="page-item"><a class="page-link" href="#">2</a></li>
        <li class="page-item"><a class="page-link" href="#">3</a></li>
        <li class="page-item"><a class="page-link" href="#">Next</a></li>
      </ul>
    </nav>
  </div>
</section>

<!-- **** MODAL TRIGGER **** -->
<section class="py-5">
  <div class="container text-center">
    <button class="btn btn-brand" data-bs-toggle="modal" data-bs-target="#demoModal">
      Launch modal
    </button>
  </div>
</section>

<!-- Modal -->
<div class="modal fade" id="demoModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content card-elev">
      <div class="modal-header">
        <h5 class="modal-title text-brand">Modal title</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <p>This modal still uses the same border-radius and shadow token.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-brand" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-brand">Save changes</button>
      </div>
    </div>
  </div>
</div>

<!-- **** FOOTER **** -->
<footer class="py-4 border-top" id="footer">
  <div class="container text-center">
    <p class="mb-2"><strong class="text-brand">OneTooOne</strong> © 2025</p>
    <a class="link-accent me-3" href="#">Privacy</a>
    <a class="link-accent" href="#">Terms</a>
  </div>
</footer>

<!-- Bootstrap JS (Modal, Tabs, etc.) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
