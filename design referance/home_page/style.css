/************************************************************
 * 1. GLOBAL / BASE STYLES
 ************************************************************/

body {
  font-family: 'Roboto', sans-serif; /* Sets the global font family */
  margin: 0;                        /* Removes default browser margins */
  min-height: 100vh;                /* Ensures the body covers full viewport height */
  padding-top: 50px;                /* Extra spacing at the top to ensure fixed headers don't overlap content */
}





/************************************************************
 * 2. RADIAL BACKGROUND GRADIENT & ANIMATION
 ************************************************************/
 .radial-gradient {
  width: 100%;
  min-height: 100vh;
  background: radial-gradient(ellipse at center, 
              #fae1d7 40%, 
              rgba(255, 255, 255, 0.5) 70%, 
              #ffffff 100%);
  background-size: cover;
  animation: radialMove 5s step-start infinite;
}

/* Subtle movement animation */
@keyframes radialMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}





/************************************************************
 * 3. NAVBAR STYLES
 ************************************************************/

/* 
  Main navbar container.
  We override Bootstrap's default background with transparent (or white if needed).
*/
.navbar {
  background: transparent !important; /* Overriding default Bootstrap color */
  padding: 1.5rem 2rem;              /* Spacing for top/bottom and sides */
}

/* 
  Brand styling: "Logo" or site name
*/
.navbar-brand {
  font-weight: 700;    /* Bold font */
  font-size: 1.75rem;  /* Larger text size for brand name */
  color: #2F160F;      /* Dark brown text color */
}

/* 
  Container for right-aligned buttons and dropdowns 
*/
.nav-buttons .btn {
  border-radius: 100px;                   /* Rounded pill shape */
  padding: 0.625rem 1.25rem;             /* Vertical & horizontal padding */
  background: white;                      /* Button background color */
  font-size: 0.9375rem;                   /* Adjusted font size */
  font-weight: 500;                       /* Medium font weight */
  border: 1px solid rgba(0,0,0,0.1);      /* Subtle border */
}

/* 
  In case the navbar is fixed (Bootstrap's .fixed-top class), 
  add a white background, so it's not transparent when scrolled.
*/
.fixed-top {
  background-color: #ffffff;
  padding-top: 10px;
  padding-bottom: 10px;
}

/* 
  Navbar can get a "scrolled" class from JS to change background 
  when user scrolls a bit.
*/
.navbar.scrolled {
  background-color: rgb(255, 255, 255) !important;
}

/* 
  Specific button used for "For business"
*/
.for-business-btn {
  border: 1px solid rgba(0,0,0,0.1) !important;
}

/* 
  Dropdown menu button 
*/
.menu-btn {
  border: 1px solid rgba(0,0,0,0.1) !important;
}

/* 
  Overall dropdown menu: removing default borders, 
  applying box-shadow & rounding corners
*/
.dropdown-menu {
  padding: 0.5rem 0;
  border-radius: 1rem;
  border: none;
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
  min-width: 240px;
}

/* 
  Individual dropdown items 
*/
.dropdown-item {
  padding: 0.75rem 1.25rem;
  color: #333;
  font-size: 0.9375rem;
}

/* 
  Headers (like "Top categories") inside dropdown 
*/
.dropdown-header {
  padding: 0.75rem 1.25rem;
  font-size: 0.875rem;
  color: #666;
}

/* 
  Divider lines inside dropdown 
*/
.dropdown-divider {
  margin: 0.5rem 0;
}




/************************************************************
 * 4. HERO SECTION
 ************************************************************/

.hero-title {
  font-family: 'Yeseva One', serif; /* Apply the new font */
  font-size: 64px; /* Adjust to match the look */
  font-weight: 700; /* Keep it bold */
  line-height: 1.2; /* Maintain spacing */
  text-transform: capitalize; /* Ensure a nice capitalized effect */
  margin-bottom: 48px;
  color: #2F160F; /* Dark brown color for heading */
}







/************************************************************
 * 5. SEARCH CONTAINER (IN HERO)
 ************************************************************/
/*
  Container holding the search fields (categories & location) 
  plus the search button.
*/
.search-container {
  background: white;
  border-radius: 100px;                 /* Pill shape around the entire search bar */
  padding: 8px;                         /* Internal padding for the entire container */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 100%;
  max-width: 800px;                     /* Limits the maximum width on larger screens */
  margin: 0 auto;                       /* Centers the container */
}

/* 
  The wrapper for each input inside the search container 
*/
.search-group {
  position: relative; /* Allows positioning of the icon in the input */
  width: 100%;
}

/* 
  The text input styles for categories & location 
*/
.search-input {
  border: none;                    /* Removes default border */
  padding: 16px 16px 16px 48px;    /* Spacing around text and extra space for icon */
  background: transparent;         /* Transparent so the container's background shows */
  width: 100%;                     /* Full width inside the container */
  font-size: 1rem;                 /* Base font size for inputs */
  color: #333;                     /* Dark text color */
}

/* 
  Removes the focus outline and shadow for a cleaner look
*/
.search-input:focus {
  outline: none;
  box-shadow: none;
}

/* 
  Placeholder text color 
*/
.search-input::placeholder {
  color: #666;
}

/* 
  The left icon inside each search input (e.g., search or location icon)
*/
.input-icon-left {
  position: absolute;         /* Positioned relative to parent container */
  left: 16px;                 /* Spacing from left side */
  top: 50%;                   /* Vertically center it within input */
  transform: translateY(-50%);
  color: #666;
  font-size: 1.1rem;
}

/* 
  Vertical separator line between Category and Location inputs 
*/
.search-divider {
  width: 1px;
  height: 32px;
  background-color: #e0e0e0;
  margin: 0 8px;
}

/* 
  "Search" button on the right side 
*/
.search-btn {
  background: #2F160F;        /* Dark background color */
  color: white;               /* White text */
  border: none;
  border-radius: 100px;       /* Pill shaped button */
  padding: 16px 32px;
  font-weight: 500;
  font-size: 1rem;
  transition: background-color 0.2s;
  white-space: nowrap;        /* Prevent text from wrapping */
}

.search-btn:hover {
  background: #1a0d09;        /* Slightly darker color on hover */
}





/************************************************************
 * 6. BOOKING COUNT (HERO SECTION)
 ************************************************************/
/* 
  Text showing the total bookings made today 
*/
.booking-count {
  font-size: 25px;
  margin-top: 2rem;
  color: #2F160F;
}

/* 
  Emphasize the number in bold 
*/
.booking-count strong {
  font-weight: 600;
}


/************************************************************
 * 7. CARD SECTIONS (TOP PICKS, TRENDING, EXCLUSIVE DEALS, ETC.)
 ************************************************************/
/* 
  General styles for each service card 
  used in multiple sections (Top Picks, Trending, etc.)
*/
.service-card {
  width: 317px;                       /* Fixed width for consistency */
  border: none;
  border-radius: 10px;                /* Rounds card corners */
  overflow: hidden;                   /* Ensures child elements don't overflow */
  transition: transform 0.2s;         /* Slight scale effect on hover */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Soft shadow for card depth */
  margin: 0 auto;                     /* Center the card horizontally */
  background: white;
}

.service-card:hover {
  transform: translateY(-5px); /* Move the card up slightly on hover */
}

/* 
  Images within each card 
*/
.card-img-top {
  width: 317px;
  height: 177px;
  object-fit: cover; /* Ensures the image is cropped proportionally */
}

/* 
  Inner card body spacing 
*/
.card-body {
  padding: 1rem;
}

/* 
  Card title (e.g., "Glow Beauty Salon")
*/
.card-title {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2F160F;
}

/* 
  Rating section inside the card (stars + number of reviews)
*/
.rating {
  margin-bottom: 0.5rem;
}

/* 
  Star rating number 
*/
.rating-score {
  font-weight: 600;
  color: #2F160F;
}

/* 
  The total number of reviews 
*/
.review-count {
  color: #666;
  margin-left: 4px;
  font-size: 0.9rem;
}

/* 
  Location text (e.g., "Al Jazirah, Al Qatif")
*/
.location {
  margin-bottom: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

/* 
  Business type label (e.g. "Beauty Salon", "Barbershop")
*/
.business-type {
  display: inline-block;
  padding: 4px 12px;
  background-color: #f8f8f8;
  border-radius: 20px;
  font-size: 0.85rem;
  color: #333;
}


