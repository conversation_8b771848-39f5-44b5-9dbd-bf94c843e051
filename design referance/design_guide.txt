# CozyWish Design System Guide
## Professional Design Implementation for AI-Assisted Development

### VERSION: 2.0.0
### LAST UPDATED: 2025-06-23
### FRAMEWORK: Bootstrap 5.3+

---

## OVERVIEW

This guide provides comprehensive instructions for implementing the CozyWish design system using Bootstrap 5 classes only. It is specifically designed for AI tools (Cursor, Augment, etc.) to ensure consistent, professional styling across all pages.

**Brand Identity:**
- Name: CozyWish
- Tagline: "Find & Book Local Spa and Massage Services"
- Personality: Warm, relaxing, professional, trustworthy, premium
- Target: Adults seeking wellness and relaxation services

---

## CORE DESIGN PRINCIPLES

1. **Warmth & Comfort**: Use warm oranges and browns to evoke relaxation
2. **Professional Trust**: Clean typography and consistent spacing
3. **Accessibility First**: High contrast ratios and semantic HTML
4. **Mobile Responsive**: Mobile-first approach with Bootstrap grid
5. **Bootstrap 5 Only**: No custom CSS - use Bootstrap classes exclusively

---

## COLOR SYSTEM

### Brand Colors (Core Identity)
- **Brand Primary**: #2F160F (Dark brown - main brand color for text, headings, and primary elements)
- **Brand Primary Light**: #4a2a1f (Lighter brown for secondary text and hover states)
- **Brand Primary Dark**: #1a0d08 (Darker brown for emphasis and contrast)
- **Brand Accent**: #fae1d7 (Light cream/beige - backgrounds, cards, accent elements)
- **Brand Accent Light**: #fef7f0 (Very light cream for subtle backgrounds)
- **Brand Accent Dark**: #f1d4c4 (Darker cream for borders and subtle contrast)

### Primary Colors (Orange Palette)
- **Primary 50**: #fef7f0 (Very light backgrounds)
- **Primary 100**: #fdeee0 (Light backgrounds, hero sections)
- **Primary 500**: #ed7544 (Main orange color, buttons, links)
- **Primary 600**: #de5a2c (Hover states, active elements)

### Secondary Colors (Supporting Browns)
- **Secondary 950**: #2f1f18 (Dark text, headings - same as Brand Primary)
- **Secondary 900**: #583d30 (Secondary text)
- **Secondary 200**: #e3d5c4 (Light borders, dividers)

### Neutral Colors (Text & Backgrounds)
- **Neutral 50**: #fafafa (Light backgrounds)
- **Neutral 200**: #e5e5e5 (Borders, dividers)
- **Neutral 500**: #737373 (Muted text)
- **Neutral 700**: #404040 (Body text)
- **Neutral 900**: #171717 (Dark text)

### Semantic Colors
- **Success**: #059669 (Green for success states)
- **Warning**: #d97706 (Orange for warnings)
- **Error**: #dc2626 (Red for errors)
- **Info**: #0284c7 (Blue for information)

### Brand Color Usage Guidelines

#### When to Use Brand Primary (#2F160F):
- Main headings and hero titles
- Primary navigation brand name
- Important call-to-action buttons
- Key text that needs emphasis
- Brand-specific elements

#### When to Use Brand Accent (#fae1d7):
- Background sections for warmth
- Card backgrounds for featured content
- Subtle highlighting areas
- Accent borders and dividers
- Special offer or promotion backgrounds

#### Color Hierarchy:
1. **Brand Primary** - Most important elements
2. **Primary Orange** - Secondary important elements
3. **Brand Accent** - Background and supporting elements
4. **Neutral Colors** - Body text and general UI

---

## TYPOGRAPHY

### Font Families
1. **Primary**: 'Inter' - Body text, forms, UI elements
2. **Heading**: 'Poppins' - Headings, navigation, buttons
3. **Display**: 'Playfair Display' - Hero titles, special headings

### Implementation
```html
<!-- Include Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- Apply via inline styles -->
<h1 style="font-family: 'Poppins', sans-serif;">Heading</h1>
<p style="font-family: 'Inter', sans-serif;">Body text</p>
<h1 style="font-family: 'Playfair Display', serif;">Display title</h1>
```

### Hierarchy
- **Display**: Use for hero titles, major page headers
- **H1-H6**: Use Poppins, fw-bold or fw-semibold
- **Body**: Use Inter, fw-normal (400) or fw-medium (500)
- **Captions**: Use Inter, fw-normal, text-muted

---

## COMPONENT GUIDELINES

### Buttons

#### Brand Button (Primary brand actions)
```html
<button class="btn btn-primary" style="background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%); border: none; border-radius: 0.5rem; font-weight: 600; font-family: 'Poppins', sans-serif; color: white;">
  Brand Action
</button>
```

#### Brand Outline Button (Secondary brand actions)
```html
<button class="btn btn-outline-primary" style="border: 2px solid #2F160F; color: #2F160F; border-radius: 0.5rem; font-weight: 600; font-family: 'Poppins', sans-serif; background: white;">
  Brand Outline
</button>
```

#### Primary Button (Main CTAs)
```html
<button class="btn btn-primary" style="background: linear-gradient(135deg, #ed7544 0%, #de5a2c 100%); border: none; border-radius: 0.5rem; font-weight: 600; font-family: 'Poppins', sans-serif;">
  Primary Action
</button>
```

#### Secondary Button (Alternative actions)
```html
<button class="btn btn-outline-primary" style="border: 2px solid #ed7544; color: #ed7544; border-radius: 0.5rem; font-weight: 600; font-family: 'Poppins', sans-serif;">
  Secondary Action
</button>
```

#### Ghost Button (Tertiary actions)
```html
<button class="btn btn-link" style="color: #ed7544; text-decoration: none; font-weight: 500; font-family: 'Poppins', sans-serif;">
  Ghost Action
</button>
```

### Cards

#### Standard Card
```html
<div class="card" style="border: 1px solid #e5e5e5; border-radius: 1rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); overflow: hidden;">
  <div class="card-body p-4">
    <!-- Content -->
  </div>
</div>
```

#### Brand Card
```html
<div class="card" style="border: 2px solid #2F160F; border-radius: 1rem; background: linear-gradient(135deg, #ffffff 0%, #fae1d7 100%); box-shadow: 0 10px 15px -3px rgba(47, 22, 15, 0.1);">
  <div class="card-body p-4">
    <!-- Content -->
  </div>
</div>
```

#### Accent Card
```html
<div class="card" style="border: 1px solid #fae1d7; border-radius: 1rem; background: #fae1d7; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05); overflow: hidden;">
  <div class="card-body p-4">
    <!-- Content -->
  </div>
</div>
```

#### Featured Card
```html
<div class="card" style="border: 2px solid #ed7544; border-radius: 1rem; background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%); box-shadow: 0 10px 15px -3px rgba(237, 117, 68, 0.1);">
  <div class="card-body p-4">
    <!-- Content -->
  </div>
</div>
```

### Forms

#### Input Fields
```html
<input class="form-control" style="border: 2px solid #e5e5e5; border-radius: 0.5rem; padding: 0.75rem 1rem; font-family: 'Inter', sans-serif;" placeholder="Enter text">
```

#### Focus States (Add via JavaScript or CSS)
```html
<input class="form-control" style="border: 2px solid #ed7544; box-shadow: 0 0 0 0.2rem rgba(237, 117, 68, 0.1);">
```

#### Labels
```html
<label class="form-label" style="font-weight: 600; color: #404040; font-family: 'Poppins', sans-serif;">Label Text</label>
```

### Navigation

#### Navbar
```html
<nav class="navbar navbar-expand-lg" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);">
  <div class="container">
    <a class="navbar-brand" style="font-family: 'Poppins', sans-serif; font-size: 1.5rem; font-weight: 700; color: #2f1f18;">CozyWish</a>
    <!-- Navigation items -->
  </div>
</nav>
```

#### Nav Links
```html
<a class="nav-link" style="font-weight: 500; color: #404040; font-family: 'Poppins', sans-serif;">Link Text</a>
```

---

## LAYOUT PATTERNS

### Hero Section
```html
<section style="background: radial-gradient(ellipse at center, #fae1d7 40%, #fef7f0 70%, #ffffff 100%); min-height: 100vh;" class="d-flex align-items-center">
  <div class="container text-center">
    <h1 style="font-family: 'Playfair Display', serif; font-size: 3rem; font-weight: 700; color: #2F160F;">Hero Title</h1>
    <p class="lead" style="color: #737373; font-family: 'Inter', sans-serif;">Subtitle text</p>
    <button class="btn btn-primary" style="background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%); border: none; border-radius: 0.5rem; font-weight: 600; font-family: 'Poppins', sans-serif; color: white;">Get Started</button>
  </div>
</section>
```

### Hero Section (Alternative)
```html
<section style="background: radial-gradient(ellipse at center, #fef7f0 0%, #fdeee0 40%, #ffffff 100%); min-height: 100vh;" class="d-flex align-items-center">
  <div class="container text-center">
    <h1 style="font-family: 'Playfair Display', serif; font-size: 3rem; font-weight: 700; color: #2F160F;">Hero Title</h1>
    <p class="lead" style="color: #737373; font-family: 'Inter', sans-serif;">Subtitle text</p>
  </div>
</section>
```

### Content Section
```html
<section class="py-5">
  <div class="container">
    <h2 class="text-center mb-5" style="font-family: 'Poppins', sans-serif; font-weight: 700; color: #2f1f18;">Section Title</h2>
    <!-- Content -->
  </div>
</section>
```

### Grid Layout
```html
<div class="row g-4">
  <div class="col-lg-4 col-md-6">
    <!-- Card or content -->
  </div>
  <!-- Repeat columns -->
</div>
```

---

## UTILITY CLASSES & STYLES

### Text Colors
- Brand text: `style="color: #2F160F;"`
- Brand light text: `style="color: #4a2a1f;"`
- Primary text: `style="color: #2f1f18;"`
- Secondary text: `style="color: #583d30;"`
- Muted text: `style="color: #737373;"`
- Accent text: `style="color: #ed7544;"`

### Background Colors
- Brand background: `style="background-color: #2F160F;"`
- Brand accent background: `style="background-color: #fae1d7;"`
- Brand accent light: `style="background-color: #fef7f0;"`
- Light background: `style="background-color: #fef7f0;"`
- White background: `style="background-color: #ffffff;"`
- Brand gradient: `style="background: radial-gradient(ellipse at center, #fae1d7 40%, #fef7f0 70%, #ffffff 100%);"`
- Alternative gradient: `style="background: radial-gradient(ellipse at center, #fef7f0 0%, #fdeee0 40%, #ffffff 100%);"`

### Shadows
- Small: `style="box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);"`
- Medium: `style="box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);"`
- Large: `style="box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);"`

### Border Radius
- Small: `style="border-radius: 0.5rem;"`
- Large: `style="border-radius: 1rem;"`
- Full: `style="border-radius: 9999px;"`

---

## RESPONSIVE DESIGN

### Breakpoints (Bootstrap 5 standard)
- xs: <576px
- sm: ≥576px
- md: ≥768px
- lg: ≥992px
- xl: ≥1200px
- xxl: ≥1400px

### Mobile-First Approach
Always design for mobile first, then enhance for larger screens:
```html
<div class="col-12 col-md-6 col-lg-4">
  <!-- Content adapts: full width on mobile, half on tablet, third on desktop -->
</div>
```

---

## ACCESSIBILITY REQUIREMENTS

### Color Contrast
- Ensure 4.5:1 contrast ratio minimum
- Primary color (#ed7544) on white background: ✓ Passes
- Dark text (#2f1f18) on light backgrounds: ✓ Passes

### Semantic HTML
```html
<main role="main">
  <section aria-labelledby="section-title">
    <h2 id="section-title">Section Title</h2>
    <!-- Content -->
  </section>
</main>
```

### Focus States
All interactive elements must have visible focus states:
```html
<button style="outline: 2px solid #ed7544; outline-offset: 2px;" onfocus="this.style.outline='2px solid #ed7544'">Button</button>
```

---

## IMPLEMENTATION CHECKLIST

### For Every Page:
- [ ] Include Google Fonts (Inter, Poppins, Playfair Display)
- [ ] Use Bootstrap 5.3+ CDN
- [ ] Apply hero gradient background if applicable
- [ ] Use consistent spacing (py-5 for sections)
- [ ] Implement proper heading hierarchy
- [ ] Add responsive classes (col-*, d-*, etc.)
- [ ] Include proper semantic HTML
- [ ] Test on mobile devices

### For Components:
- [ ] Use specified color values
- [ ] Apply correct font families
- [ ] Include hover states where applicable
- [ ] Ensure proper contrast ratios
- [ ] Add appropriate shadows and border radius
- [ ] Test keyboard navigation
- [ ] Validate HTML semantics

---

## COMMON PATTERNS

### Service Card
```html
<div class="col-lg-4 col-md-6 mb-4">
  <div class="card h-100" style="border: 1px solid #e5e5e5; border-radius: 1rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); overflow: hidden; transition: transform 0.3s ease;">
    <img src="image.jpg" class="card-img-top" style="height: 200px; object-fit: cover;">
    <div class="card-body p-4">
      <h5 class="card-title" style="font-family: 'Poppins', sans-serif; font-weight: 600; color: #2f1f18;">Service Name</h5>
      <p class="card-text" style="color: #737373; font-family: 'Inter', sans-serif;">Service description</p>
      <div class="d-flex justify-content-between align-items-center">
        <span style="background: #ed7544; color: white; padding: 0.25rem 0.75rem; border-radius: 9999px; font-weight: 500;">$89</span>
        <button class="btn btn-outline-primary" style="border: 2px solid #ed7544; color: #ed7544; border-radius: 0.5rem; font-weight: 600;">Book Now</button>
      </div>
    </div>
  </div>
</div>
```

### Search Bar
```html
<div class="input-group" style="background: white; border-radius: 50px; padding: 8px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);">
  <span class="input-group-text" style="background: transparent; border: none;">
    <i class="fas fa-search" style="color: #737373;"></i>
  </span>
  <input type="text" class="form-control" style="border: none; background: transparent; font-family: 'Inter', sans-serif;" placeholder="Search services...">
  <button class="btn" style="background: #ed7544; color: white; border: none; border-radius: 50px; padding: 16px 32px; font-weight: 500;">Search</button>
</div>
```

---

## TROUBLESHOOTING

### Common Issues:
1. **Fonts not loading**: Ensure Google Fonts link is in <head>
2. **Colors not matching**: Use exact hex values provided
3. **Responsive issues**: Always use Bootstrap grid classes
4. **Accessibility failures**: Check contrast ratios and semantic HTML
5. **Inconsistent spacing**: Use Bootstrap spacing classes (p-*, m-*, py-*, etc.)

### Testing:
- Test on mobile, tablet, and desktop
- Validate HTML and check accessibility
- Verify color contrast ratios
- Test keyboard navigation
- Check loading performance

---

## RESOURCES

- Bootstrap 5 Documentation: https://getbootstrap.com/docs/5.3/
- Google Fonts: https://fonts.google.com/
- Color Contrast Checker: https://webaim.org/resources/contrastchecker/
- HTML Validator: https://validator.w3.org/

---

**Remember**: This design system prioritizes consistency, accessibility, and professional appearance. When in doubt, refer to the brand.html showcase file for visual examples of all components in action.

---

## AI IMPLEMENTATION INSTRUCTIONS

### For AI Tools (Cursor, Augment, etc.)

#### CRITICAL RULES:
1. **NEVER use custom CSS files** - Only Bootstrap 5 classes + inline styles
2. **ALWAYS include Google Fonts** in every HTML file
3. **ALWAYS use exact color values** from this guide
4. **ALWAYS apply font-family inline styles** for typography
5. **ALWAYS use semantic HTML** with proper ARIA labels

#### Step-by-Step Implementation Process:

**Step 1: Setup**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Title - CozyWish</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
```

**Step 2: Body Structure**
```html
<body style="font-family: 'Inter', sans-serif; color: #404040;">
    <!-- Navigation -->
    <!-- Main Content -->
    <!-- Footer -->

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
```

**Step 3: Apply Design System**
- Use the component patterns from this guide
- Apply inline styles for colors and fonts
- Use Bootstrap classes for layout and spacing

#### MANDATORY INLINE STYLES:

**Typography:**
```html
<!-- Brand headings -->
<h1 style="font-family: 'Poppins', sans-serif; font-weight: 700; color: #2F160F;">
<h2 style="font-family: 'Poppins', sans-serif; font-weight: 600; color: #2F160F;">

<!-- Regular headings -->
<h1 style="font-family: 'Poppins', sans-serif; font-weight: 700; color: #2f1f18;">
<h2 style="font-family: 'Poppins', sans-serif; font-weight: 600; color: #2f1f18;">

<!-- Display titles -->
<h1 style="font-family: 'Playfair Display', serif; font-weight: 700; color: #2F160F;">

<!-- Body text -->
<p style="font-family: 'Inter', sans-serif; color: #404040;">

<!-- Muted text -->
<p style="font-family: 'Inter', sans-serif; color: #737373;">
```

**Buttons:**
```html
<!-- Brand Primary -->
<button class="btn btn-primary" style="background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%); border: none; border-radius: 0.5rem; font-weight: 600; font-family: 'Poppins', sans-serif; color: white;">

<!-- Brand Outline -->
<button class="btn btn-outline-primary" style="border: 2px solid #2F160F; color: #2F160F; border-radius: 0.5rem; font-weight: 600; font-family: 'Poppins', sans-serif; background: white;">

<!-- Primary -->
<button class="btn btn-primary" style="background: linear-gradient(135deg, #ed7544 0%, #de5a2c 100%); border: none; border-radius: 0.5rem; font-weight: 600; font-family: 'Poppins', sans-serif;">

<!-- Secondary -->
<button class="btn btn-outline-primary" style="border: 2px solid #ed7544; color: #ed7544; border-radius: 0.5rem; font-weight: 600; font-family: 'Poppins', sans-serif;">
```

**Cards:**
```html
<!-- Standard Card -->
<div class="card" style="border: 1px solid #e5e5e5; border-radius: 1rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">

<!-- Brand Card -->
<div class="card" style="border: 2px solid #2F160F; border-radius: 1rem; background: linear-gradient(135deg, #ffffff 0%, #fae1d7 100%); box-shadow: 0 10px 15px -3px rgba(47, 22, 15, 0.1);">

<!-- Accent Card -->
<div class="card" style="border: 1px solid #fae1d7; border-radius: 1rem; background: #fae1d7; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);">
```

**Forms:**
```html
<input class="form-control" style="border: 2px solid #e5e5e5; border-radius: 0.5rem; padding: 0.75rem 1rem; font-family: 'Inter', sans-serif;">

<label class="form-label" style="font-weight: 600; color: #404040; font-family: 'Poppins', sans-serif;">
```

#### QUALITY ASSURANCE CHECKLIST:

Before completing any page, verify:
- [ ] Google Fonts are loaded and applied
- [ ] All colors match the specified hex values
- [ ] Bootstrap 5 classes are used for layout
- [ ] Inline styles are applied for design system elements
- [ ] Responsive classes are included (col-*, d-*, etc.)
- [ ] Semantic HTML is used (main, section, article, etc.)
- [ ] ARIA labels are included where needed
- [ ] Focus states are accessible
- [ ] Mobile responsiveness is tested

#### COMMON MISTAKES TO AVOID:

❌ **DON'T:**
- Create separate CSS files
- Use generic Bootstrap colors without customization
- Forget to include Google Fonts
- Use inconsistent font families
- Skip responsive classes
- Use non-semantic HTML

✅ **DO:**
- Use inline styles for design system elements
- Include all three font families (Inter, Poppins, Playfair Display)
- Apply exact color values from this guide
- Use Bootstrap grid system consistently
- Include proper semantic HTML structure
- Test on multiple screen sizes

#### DEBUGGING GUIDE:

**If fonts don't look right:**
1. Check Google Fonts link in <head>
2. Verify font-family inline styles are applied
3. Ensure font weights are available (300, 400, 500, 600, 700, 800)

**If colors don't match:**
1. Use exact hex values from this guide
2. Apply inline styles, don't rely on Bootstrap defaults
3. Check for CSS specificity issues

**If layout breaks:**
1. Verify Bootstrap 5 CDN is loaded
2. Use proper Bootstrap grid classes
3. Test responsive behavior with browser dev tools

**If accessibility fails:**
1. Check color contrast ratios
2. Add proper ARIA labels
3. Ensure keyboard navigation works
4. Use semantic HTML elements

---

## DJANGO TEMPLATE INTEGRATION

### Template Structure:
```html
{% extends 'base.html' %}
{% load static %}

{% block title %}Page Title - CozyWish{% endblock %}

{% block extra_css %}
<!-- Page-specific styles if needed -->
{% endblock %}

{% block content %}
<!-- Apply design system patterns here -->
<section class="py-5" style="background: radial-gradient(ellipse at center, #fef7f0 0%, #fdeee0 40%, #ffffff 100%);">
    <div class="container">
        <h1 style="font-family: 'Poppins', sans-serif; font-weight: 700; color: #2f1f18;">{{ page_title }}</h1>
        <!-- Content -->
    </div>
</section>
{% endblock %}
```

### Form Integration:
```html
<form method="post">
    {% csrf_token %}
    <div class="mb-3">
        <label class="form-label" style="font-weight: 600; color: #404040; font-family: 'Poppins', sans-serif;">
            {{ form.field.label }}
        </label>
        {{ form.field|add_class:"form-control"|attr:"style:border: 2px solid #e5e5e5; border-radius: 0.5rem; padding: 0.75rem 1rem; font-family: 'Inter', sans-serif;" }}
    </div>
    <button type="submit" class="btn btn-primary" style="background: linear-gradient(135deg, #ed7544 0%, #de5a2c 100%); border: none; border-radius: 0.5rem; font-weight: 600; font-family: 'Poppins', sans-serif;">
        Submit
    </button>
</form>
```

---

## FINAL IMPLEMENTATION NOTES

### Performance Optimization:
- Use Google Fonts with `display=swap` parameter
- Optimize images with proper sizing and compression
- Minimize inline styles where possible while maintaining design consistency

### Browser Compatibility:
- Tested on Chrome, Firefox, Safari, Edge
- Supports IE11+ (with Bootstrap 5 limitations)
- Mobile browsers: iOS Safari, Chrome Mobile, Samsung Internet

### Maintenance:
- Update Bootstrap CDN links as new versions are released
- Monitor Google Fonts for any changes
- Test design system updates across all pages
- Document any customizations or exceptions

**This design system is production-ready and optimized for AI-assisted development. Follow these guidelines precisely for consistent, professional results.**
