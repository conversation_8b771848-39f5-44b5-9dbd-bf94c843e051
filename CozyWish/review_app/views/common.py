# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.db.models import Avg, Count, Q
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from booking_cart_app.models import Booking
from ..models import Review

User = get_user_model()


# --- Helper Functions ---

def is_admin(user):
    """Check if user is an admin."""
    return user.is_authenticated and user.is_staff


def is_customer(user):
    """Check if user is a customer (not service provider)."""
    return user.is_authenticated and user.role == User.CUSTOMER


def is_service_provider(user):
    """Check if user is a service provider."""
    return user.is_authenticated and user.role == User.SERVICE_PROVIDER


def has_completed_booking(user, venue):
    """Check if user has a completed booking for the venue."""
    if not is_customer(user):
        return False
    return Booking.objects.filter(
        customer=user,
        venue=venue,
        status=Booking.COMPLETED,
    ).exists()


def get_venue_review_stats(venue):
    """Calculate comprehensive review statistics for a venue."""
    approved_reviews = Review.objects.filter(venue=venue, is_approved=True)
    stats = approved_reviews.aggregate(
        average_rating=Avg('rating'),
        total_reviews=Count('id'),
        five_star=Count('id', filter=Q(rating=5)),
        four_star=Count('id', filter=Q(rating=4)),
        three_star=Count('id', filter=Q(rating=3)),
        two_star=Count('id', filter=Q(rating=2)),
        one_star=Count('id', filter=Q(rating=1)),
    )
    total = stats['total_reviews'] or 1
    stats.update({
        'five_star_percent': round((stats['five_star'] / total) * 100, 1),
        'four_star_percent': round((stats['four_star'] / total) * 100, 1),
        'three_star_percent': round((stats['three_star'] / total) * 100, 1),
        'two_star_percent': round((stats['two_star'] / total) * 100, 1),
        'one_star_percent': round((stats['one_star'] / total) * 100, 1),
    })
    return stats
