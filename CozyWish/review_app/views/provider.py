# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Count, Q
from django.shortcuts import get_object_or_404, redirect, render
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views import View

from django.conf import settings

# --- Local App Imports ---
from utils.logging_utils import log_error, log_security_event, log_user_activity
from ..forms import ReviewResponseForm
from ..logging_utils import (
    log_review_response,
    log_review_response_update,
    log_unauthorized_review_access,
    performance_monitor,
)
from ..models import Review, ReviewResponse
from ..utils import get_provider_venue
from .common import get_venue_review_stats, is_service_provider


# --- Helper Functions ---


# --- Provider Views ---


@login_required
@performance_monitor('provider_respond_to_review')
def provider_respond_to_review_view(request, review_slug):
    """Allow service providers to respond to reviews of their venue."""
    try:
        review = get_object_or_404(Review, slug=review_slug, is_approved=True)
        if not is_service_provider(request.user):
            log_security_event(
                app_name='review_app',
                event_type='unauthorized_response_attempt',
                user_email=request.user.email,
                user_id=request.user.id,
                request=request,
                details={'review_slug': review_slug, 'user_role': request.user.role},
                severity='WARNING',
            )
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('Only service providers can respond to reviews.'))
        venue = get_provider_venue(request.user)
        if venue is None or venue != review.venue:
            messages.error(request, _('You can only respond to reviews of your own venue.'))
            return redirect('review_app:venue_reviews', venue_id=review.venue.id)
        existing_response = ReviewResponse.objects.filter(review=review).first()
        if existing_response:
            messages.info(request, _('You have already responded to this review. You can edit your response.'))
            return redirect('review_app:provider_edit_response', response_id=existing_response.id)
        if request.method == 'POST':
            form = ReviewResponseForm(request.POST)
            if form.is_valid():
                response = form.save(commit=False)
                response.review = review
                response.provider = request.user
                response.save()
                log_review_response(
                    user=request.user,
                    review=review,
                    response=response,
                    request=request,
                    additional_details={
                        'response_method': 'web_form',
                        'is_first_response': True,
                    },
                )
                messages.success(request, _('Your response has been posted successfully!'))
                return redirect('review_app:provider_venue_reviews')
        else:
            form = ReviewResponseForm()
        context = {
            'form': form,
            'review': review,
            'venue': review.venue,
            'page_title': _('Respond to Review'),
        }
        return render(request, 'review_app/provider_respond_to_review.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='response_submission_error',
            error_message='Failed to submit response',
            user=request.user,
            request=request,
            exception=e,
            details={'review_slug': review_slug},
        )
        messages.error(request, _('Unable to submit response. Please try again.'))
        return redirect('review_app:provider_venue_reviews')


@login_required
@performance_monitor('provider_edit_response')
def provider_edit_response_view(request, response_id):
    """Allow service providers to edit their existing response to a review."""
    try:
        response = get_object_or_404(ReviewResponse, id=response_id)
        review = response.review
        if not is_service_provider(request.user):
            log_unauthorized_review_access(
                user_email=request.user.email,
                attempted_action='edit_response',
                review_id=review.id,
                request=request,
                additional_details={'response_id': response_id, 'user_role': request.user.role},
            )
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('Only service providers can edit responses.'))
        venue = get_provider_venue(request.user)
        if venue is None or venue != review.venue:
            messages.error(request, _('You can only edit responses to reviews of your own venue.'))
            return redirect('review_app:venue_reviews', venue_id=review.venue.id)
        if request.method == 'POST':
            form = ReviewResponseForm(request.POST, instance=response)
            if form.is_valid():
                old_response_text = response.response_text
                updated_response = form.save()
                log_review_response_update(
                    user=request.user,
                    review=review,
                    response=updated_response,
                    request=request,
                    additional_details={
                        'response_method': 'web_form',
                        'content_changed': old_response_text != updated_response.response_text,
                        'old_response_length': len(old_response_text),
                    },
                )
                messages.success(request, _('Your response has been updated successfully!'))
                return redirect('review_app:provider_venue_reviews')
        else:
            form = ReviewResponseForm(instance=response)
        context = {
            'form': form,
            'response': response,
            'review': review,
            'venue': review.venue,
            'page_title': _('Edit Response'),
        }
        return render(request, 'review_app/provider_edit_response.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='response_edit_error',
            error_message='Failed to edit response',
            user=request.user,
            request=request,
            exception=e,
            details={'response_id': response_id},
        )
        messages.error(request, _('Unable to edit response. Please try again.'))
        return redirect('review_app:provider_venue_reviews')


class ProviderVenueReviewsView(View):
    """Display all reviews for the provider's venue with management capabilities."""

    @method_decorator(login_required)
    @method_decorator(performance_monitor('provider_venue_reviews'))
    def get(self, request):
        try:
            if not is_service_provider(request.user):
                from django.http import HttpResponseForbidden
                return HttpResponseForbidden(_('Access denied. Only service providers can view this page.'))
            venue = get_provider_venue(request.user)
            if venue is None:
                messages.error(request, _('You need to create a venue first.'))
                return redirect('dashboard_app:provider_dashboard')
            reviews = (
                Review.objects.filter(venue=venue)
                .select_related('customer', 'response')
                .prefetch_related('flags')
                .order_by('-created_at')
            )
            status_filter = request.GET.get('status', 'all')
            if status_filter == 'approved':
                reviews = reviews.filter(is_approved=True)
            elif status_filter == 'flagged':
                reviews = reviews.filter(is_flagged=True)
            elif status_filter == 'pending':
                reviews = reviews.filter(is_approved=False)
            paginator = Paginator(reviews, 15)
            page_obj = paginator.get_page(request.GET.get('page'))
            review_stats = get_venue_review_stats(venue)
            additional_stats = Review.objects.filter(venue=venue).aggregate(
                flagged_count=Count('id', filter=Q(is_flagged=True)),
                pending_count=Count('id', filter=Q(is_approved=False)),
                responded_count=Count('id', filter=Q(response__isnull=False)),
                unresponded_count=Count('id', filter=Q(response__isnull=True, is_approved=True)),
            )
            review_stats.update(additional_stats)
            log_user_activity(
                app_name='review_app',
                activity_type='provider_reviews_viewed',
                user=request.user,
                request=request,
                details={
                    'venue_id': venue.id,
                    'venue_name': venue.venue_name,
                    'total_reviews': review_stats['total_reviews'],
                    'status_filter': status_filter,
                    'unresponded_count': review_stats['unresponded_count'],
                },
                target_object=f'venue_{venue.id}_reviews_management',
            )
            context = {
                'venue': venue,
                'reviews': page_obj,
                'review_stats': review_stats,
                'status_filter': status_filter,
                'page_title': _('Manage Reviews'),
            }
            return render(request, 'review_app/provider_venue_reviews.html', context)
        except Exception as e:
            log_error(
                app_name='review_app',
                error_type='provider_reviews_view_error',
                error_message='Failed to load provider reviews',
                user=request.user,
                request=request,
                exception=e,
            )
            messages.error(request, _('Unable to load reviews. Please try again.'))
            return redirect('dashboard_app:provider_dashboard')


@login_required
@performance_monitor('provider_review_summary')
def provider_review_summary_view(request):
    """Display detailed review statistics and summary for the provider."""
    try:
        if not is_service_provider(request.user):
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('Access denied. Only service providers can view this page.'))
        venue = get_provider_venue(request.user)
        if venue is None:
            messages.error(request, _('You need to create a venue first.'))
            return redirect('dashboard_app:provider_dashboard')
        cache_key = f"provider_summary_{request.user.id}"
        cached = cache.get(cache_key)
        rating_distribution = None
        if cached:
            review_stats = cached['stats']
            recent_ids = cached['recent']
            recent_reviews = (
                Review.objects.filter(id__in=recent_ids)
                .select_related('customer', 'response')
                .prefetch_related('flags')
            )
            is_new_venue = cached['new']
            rating_distribution = cached.get('distribution')
        else:
            review_stats = get_venue_review_stats(venue)
            additional_stats = Review.objects.filter(venue=venue).aggregate(
                total_all_reviews=Count('id'),
                flagged_count=Count('id', filter=Q(is_flagged=True)),
                pending_count=Count('id', filter=Q(is_approved=False)),
                responded_count=Count('id', filter=Q(response__isnull=False)),
                unresponded_count=Count('id', filter=Q(response__isnull=True, is_approved=True)),
            )
            review_stats.update(additional_stats)
            if review_stats['total_reviews'] > 0:
                review_stats['response_rate'] = round(
                    (review_stats['responded_count'] / review_stats['total_reviews']) * 100, 1
                )
            else:
                review_stats['response_rate'] = 0
            recent_reviews = list(
                Review.objects.filter(venue=venue, is_approved=True)
                .select_related('customer', 'response')
                .prefetch_related('flags')
                .order_by('-created_at')[:10]
            )
            is_new_venue = review_stats['total_reviews'] < 5
            rating_distribution = {
                5: {'count': review_stats['five_star'], 'percentage': review_stats['five_star_percent']},
                4: {'count': review_stats['four_star'], 'percentage': review_stats['four_star_percent']},
                3: {'count': review_stats['three_star'], 'percentage': review_stats['three_star_percent']},
                2: {'count': review_stats['two_star'], 'percentage': review_stats['two_star_percent']},
                1: {'count': review_stats['one_star'], 'percentage': review_stats['one_star_percent']},
            }
            cache.set(
                cache_key,
                {
                    'stats': review_stats,
                    'recent': [r.id for r in recent_reviews],
                    'new': is_new_venue,
                    'distribution': rating_distribution,
                },
                300,
            )
        recent_reviews = (
            Review.objects.filter(id__in=[r.id for r in recent_reviews])
            .select_related('customer', 'response')
            .prefetch_related('flags')
        )
        log_user_activity(
            app_name='review_app',
            activity_type='provider_review_summary_viewed',
            user=request.user,
            request=request,
            details={
                'venue_id': venue.id,
                'venue_name': venue.venue_name,
                'total_reviews': review_stats['total_reviews'],
                'average_rating': float(review_stats['average_rating'] or 0),
                'response_rate': review_stats['response_rate'],
                'is_new_venue': is_new_venue,
            },
            target_object=f'venue_{venue.id}_review_summary',
        )
        context = {
            'venue': venue,
            'review_stats': review_stats,
            'overall_stats': review_stats,
            'rating_distribution': rating_distribution,
            'recent_reviews': recent_reviews,
            'is_new_venue': is_new_venue,
            'page_title': _('Review Summary'),
            # Add individual context variables for backward compatibility with tests
            'average_rating': review_stats.get('average_rating', 0),
            'total_reviews': review_stats.get('total_reviews', 0),
        }
        return render(request, 'review_app/provider_review_summary.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='provider_summary_error',
            error_message='Failed to load provider review summary',
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(request, _('Unable to load review summary. Please try again.'))
        return redirect('dashboard_app:provider_dashboard')


@login_required
@performance_monitor('provider_review_detail')
def provider_review_detail_view(request, review_id):
    """Display detailed view of a review for providers (used by notifications)."""
    try:
        review = get_object_or_404(Review, id=review_id)

        # Check if user has permission to view this review
        if not is_service_provider(request.user):
            messages.error(request, _('Only service providers can view this page.'))
            return redirect('review_app:venue_reviews', venue_id=review.venue.id)

        venue = get_provider_venue(request.user)
        if venue is None or venue != review.venue:
            messages.error(request, _('You can only view reviews of your own venue.'))
            return redirect('review_app:provider_venue_reviews')

        log_user_activity(
            app_name='review_app',
            activity_type='provider_review_detail_viewed',
            user=request.user,
            request=request,
            details={
                'review_id': review.id,
                'venue_id': review.venue.id,
                'venue_name': review.venue.venue_name,
                'review_rating': review.rating,
                'has_response': hasattr(review, 'response'),
            },
            target_object=f'review_{review.id}',
        )

        context = {
            'review': review,
            'venue': review.venue,
            'page_title': _('Review Details'),
        }
        return render(request, 'review_app/provider_review_detail.html', context)

    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='provider_review_detail_error',
            error_message='Failed to load provider review detail',
            user=request.user,
            request=request,
            exception=e,
            details={'review_id': review_id},
        )
        messages.error(request, _('Unable to load review details. Please try again.'))
        return redirect('review_app:provider_venue_reviews')
