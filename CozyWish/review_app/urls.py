# """URL configuration for review_app."""

# --- Third-Party Imports ---
from django.urls import path

# --- Local App Imports ---
from . import views

app_name = 'review_app'

urlpatterns = [
    # ===== CUSTOMER URLS =====

    # Review viewing and submission
    path('venue/<int:venue_id>/reviews/', views.venue_reviews_view, name='venue_reviews'),
    path('venue/<int:venue_id>/submit/', views.submit_review_view, name='submit_review'),
    path('review/<slug:review_slug>/edit/', views.edit_review_view, name='edit_review'),
    path('review/<slug:review_slug>/flag/', views.flag_review_view, name='flag_review'),
    path('review/<slug:review_slug>/vote-helpfulness/', views.vote_review_helpfulness_view, name='vote_review_helpfulness'),

    # Review detail views for notifications
    path('review/<int:review_id>/customer/', views.customer_review_detail_view, name='customer_review_detail'),

    # Customer review history
    path('customer/history/', views.customer_review_history_view, name='customer_review_history'),

    # ===== PROVIDER URLS =====

    # Provider review management
    path('provider/reviews/', views.ProviderVenueReviewsView.as_view(), name='provider_venue_reviews'),
    path('provider/summary/', views.provider_review_summary_view, name='provider_review_summary'),

    # Provider review detail view for notifications
    path('review/<int:review_id>/provider/', views.provider_review_detail_view, name='provider_review_detail'),

    # Provider responses
    path('provider/respond/<slug:review_slug>/', views.provider_respond_to_review_view, name='provider_respond_to_review'),
    path('provider/response/<int:response_id>/edit/', views.provider_edit_response_view, name='provider_edit_response'),

    # ===== ADMIN URLS =====

    # Admin review moderation
    path('admin/moderation/', views.admin_review_moderation_view, name='admin_review_moderation'),
    path('admin/review/<slug:review_slug>/moderate/', views.admin_moderate_review_view, name='admin_moderate_review'),

    # Admin flag management
    path('admin/flags/', views.admin_manage_flags_view, name='admin_manage_flags'),
    path('admin/flag/<int:flag_id>/resolve/', views.admin_flag_resolution_view, name='admin_flag_resolution'),
]
