# Generated by Django 5.2.3 on 2025-06-21 16:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('review_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReviewHelpfulness',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_helpful', models.BooleanField(help_text='True if user found review helpful, False if not helpful')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the vote was cast')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the vote was last updated')),
                ('review', models.ForeignKey(help_text='Review being voted on', on_delete=django.db.models.deletion.CASCADE, related_name='helpfulness_votes', to='review_app.review')),
                ('user', models.ForeignKey(help_text='User who voted on helpfulness', on_delete=django.db.models.deletion.CASCADE, related_name='review_helpfulness_votes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Review Helpfulness Vote',
                'verbose_name_plural': 'Review Helpfulness Votes',
                'indexes': [models.Index(fields=['review', 'is_helpful'], name='review_app__review__3cbfed_idx'), models.Index(fields=['user'], name='review_app__user_id_19077a_idx')],
                'unique_together': {('review', 'user')},
            },
        ),
    ]
