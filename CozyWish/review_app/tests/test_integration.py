"""
Integration tests for review_app Django application.

This module contains comprehensive integration tests that verify the complete review workflows
including customer review submission, provider responses, admin moderation, and cross-app
integration with venues_app, booking_cart_app, and accounts_app following the same patterns as other apps.
"""

# Standard library imports
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

# Django imports
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.messages import get_messages

# Local imports
from review_app.models import Review, ReviewResponse, ReviewFlag
from venues_app.models import Category, Venue, Service
from booking_cart_app.models import Booking, BookingItem
from accounts_app.models import ServiceProviderProfile, CustomerProfile

User = get_user_model()


class ReviewIntegrationTest(TestCase):
    """Integration tests for complete review workflows in review_app."""

    def setUp(self):
        """Set up test data for integration tests."""
        self.client = Client()

        # Create test users with proper roles
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            role=User.SERVICE_PROVIDER
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Integration Test Spa',
            business_phone_number='+**********',
            contact_person_name='John Doe',
            business_address='123 Integration Ave',
            city='Integration City',
            state='NY',
            zip_code='10001'
        )

        self.admin = User.objects.create_user(
            email='<EMAIL>',
            password='AdminPass123!',
            role=User.ADMIN,
            is_staff=True,
            is_superuser=True
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name='Spa & Wellness',
            category_description='Relaxation and wellness services'
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Integration Test Spa',
            short_description='A luxury spa for integration testing',
            state='New York',
            county='New York County',
            city='New York',
            street_number='123',
            street_name='Integration Ave',
            operating_hours='9AM-6PM',
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        self.venue.categories.add(self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Integration Test Massage',
            short_description='A relaxing massage for testing',
            price_min=Decimal('80.00'),
            price_max=Decimal('120.00'),
            duration_minutes=60,
            is_active=True
        )

        # Create a completed booking to allow review submission
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status=Booking.COMPLETED,
            total_price=Decimal('100.00'),
            notes='Integration test booking'
        )

        self.booking_item = BookingItem.objects.create(
            booking=self.booking,
            service=self.service,
            service_title=self.service.service_title,
            service_price=Decimal('100.00'),
            quantity=1,
            scheduled_date=timezone.now().date() + timedelta(days=1),
            scheduled_time=timezone.now().time(),
            duration_minutes=60
        )

    def test_complete_customer_review_workflow(self):
        """Test the complete customer review workflow from submission to editing."""
        # Step 1: Customer logs in
        login_success = self.client.login(email='<EMAIL>', password='CustomerPass123!')
        self.assertTrue(login_success)

        # Step 2: Customer submits a review
        submit_review_url = reverse('review_app:submit_review', args=[self.venue.id])
        review_data = {
            'rating': 4,
            'written_review': 'Great experience! The staff was very friendly and professional.'
        }
        response = self.client.post(submit_review_url, review_data)

        # Verify review was created
        self.assertTrue(Review.objects.filter(
            venue=self.venue,
            customer=self.customer,
            rating=4,
            written_review='Great experience! The staff was very friendly and professional.'
        ).exists())

        review = Review.objects.get(venue=self.venue, customer=self.customer)
        self.assertTrue(review.is_approved)
        self.assertFalse(review.is_flagged)

        # Step 3: Customer edits the review
        edit_review_url = reverse('review_app:edit_review', args=[review.slug])
        updated_data = {
            'rating': 5,
            'written_review': 'Updated review: Excellent service and atmosphere!'
        }
        response = self.client.post(edit_review_url, updated_data)

        # Verify review was updated
        review.refresh_from_db()
        self.assertEqual(review.rating, 5)
        self.assertEqual(review.written_review, 'Updated review: Excellent service and atmosphere!')

        # Step 4: Customer views their review history
        history_url = reverse('review_app:customer_review_history')
        response = self.client.get(history_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Updated review: Excellent service and atmosphere!')

    def test_provider_response_workflow(self):
        """Test provider responding to customer reviews."""
        # Create a review first
        review = Review.objects.create(
            venue=self.venue,
            customer=self.customer,
            rating=4,
            written_review='Good service overall.'
        )

        # Step 1: Provider logs in
        login_success = self.client.login(email='<EMAIL>', password='ProviderPass123!')
        self.assertTrue(login_success)

        # Step 2: Provider views their reviews
        provider_reviews_url = reverse('review_app:provider_venue_reviews')
        response = self.client.get(provider_reviews_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Good service overall.')

        # Step 3: Provider responds to the review
        respond_url = reverse('review_app:provider_respond_to_review', args=[review.slug])
        response_data = {
            'response_text': 'Thank you for your feedback! We appreciate your business.'
        }
        response = self.client.post(respond_url, response_data)

        # Verify response was created
        self.assertTrue(ReviewResponse.objects.filter(
            review=review,
            response_text='Thank you for your feedback! We appreciate your business.'
        ).exists())

        review_response = ReviewResponse.objects.get(review=review)
        self.assertEqual(review_response.provider, self.provider)

        # Step 4: Provider views review summary
        summary_url = reverse('review_app:provider_review_summary')
        response = self.client.get(summary_url)
        self.assertEqual(response.status_code, 200)

    def test_review_flagging_workflow(self):
        """Test the complete review flagging and moderation workflow."""
        # Create a review
        review = Review.objects.create(
            venue=self.venue,
            customer=self.customer,
            rating=2,
            written_review='This place was terrible and overpriced.'
        )

        # Create another customer to flag the review
        flagger = User.objects.create_user(
            email='<EMAIL>',
            password='FlaggerPass123!',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(user=flagger)

        # Step 1: Customer flags the review
        self.client.login(email='<EMAIL>', password='FlaggerPass123!')
        flag_url = reverse('review_app:flag_review', args=[review.slug])
        flag_data = {
            'reason': 'offensive',
            'reason_text': 'This review contains inappropriate language.'
        }
        response = self.client.post(flag_url, flag_data)

        # Verify flag was created
        self.assertTrue(ReviewFlag.objects.filter(
            review=review,
            flagged_by=flagger,
            reason='offensive'
        ).exists())

        flag = ReviewFlag.objects.get(review=review, flagged_by=flagger)
        self.assertEqual(flag.status, ReviewFlag.PENDING)

        # Verify review is flagged
        review.refresh_from_db()
        self.assertTrue(review.is_flagged)

        # Step 2: Admin reviews the flag
        self.client.logout()
        self.client.login(email='<EMAIL>', password='AdminPass123!')

        # Admin views flagged reviews
        flagged_reviews_url = reverse('review_app:admin_manage_flags')
        response = self.client.get(flagged_reviews_url)
        self.assertEqual(response.status_code, 200)

        # Step 3: Admin resolves the flag (review is legitimate)
        resolve_flag_url = reverse('review_app:admin_flag_resolution', args=[flag.id])
        response = self.client.post(resolve_flag_url)

        # Verify flag was rejected
        flag.refresh_from_db()
        self.assertEqual(flag.status, ReviewFlag.RESOLVED)

        # Verify review is no longer flagged
        review.refresh_from_db()
        self.assertFalse(review.is_flagged)
        self.assertTrue(review.is_approved)

    def test_admin_moderation_workflow(self):
        """Test admin review moderation and management workflow."""
        # Create a review with inappropriate content
        review = Review.objects.create(
            venue=self.venue,
            customer=self.customer,
            rating=1,
            written_review='This place is absolutely horrible and the staff are incompetent!'
        )

        # Create a flag for the review
        flagger = User.objects.create_user(
            email='<EMAIL>',
            password='FlaggerPass123!',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(user=flagger)

        flag = ReviewFlag.objects.create(
            review=review,
            flagged_by=flagger,
            reason='inappropriate',
            reason_text='Inappropriate language and unfair criticism'
        )

        # Step 1: Admin logs in
        login_success = self.client.login(email='<EMAIL>', password='AdminPass123!')
        self.assertTrue(login_success)

        # Step 2: Admin views all reviews
        admin_reviews_url = reverse('review_app:admin_review_moderation')
        response = self.client.get(admin_reviews_url)
        self.assertEqual(response.status_code, 200)

        # Step 3: Admin resolves the flag and moderates the review
        resolve_flag_url = reverse('review_app:admin_flag_resolution', args=[flag.id])
        response = self.client.post(resolve_flag_url)

        # Verify flag was approved
        flag.refresh_from_db()
        self.assertEqual(flag.status, ReviewFlag.RESOLVED)

        # Step 4: Admin edits the review content
        moderate_review_url = reverse('review_app:admin_moderate_review', args=[review.slug])
        moderation_data = {
            'rating': 2,
            'written_review': 'The service could be improved.',
            'is_approved': True,
            'is_flagged': False
        }
        response = self.client.post(moderate_review_url, moderation_data)

        # Verify review was moderated
        review.refresh_from_db()
        self.assertEqual(review.rating, 2)
        self.assertEqual(review.written_review, 'The service could be improved.')
        self.assertTrue(review.is_approved)
        self.assertFalse(review.is_flagged)

    def test_cross_app_integration_workflow(self):
        """Test integration between review_app and other apps."""
        # Step 1: Customer books a service (booking_cart_app integration)
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        # Verify booking exists and is completed
        self.assertEqual(self.booking.status, Booking.COMPLETED)
        self.assertEqual(self.booking.customer, self.customer)
        self.assertEqual(self.booking.venue, self.venue)

        # Step 2: Customer submits review after completed booking
        submit_review_url = reverse('review_app:submit_review', args=[self.venue.id])
        review_data = {
            'rating': 5,
            'written_review': 'Amazing experience! Will definitely come back.'
        }
        response = self.client.post(submit_review_url, review_data)

        # Verify review was created and linked to venue
        review = Review.objects.get(venue=self.venue, customer=self.customer)
        self.assertEqual(review.rating, 5)
        self.assertEqual(review.venue, self.venue)

        # Step 3: Verify venue rating is updated (venues_app integration)
        venue_reviews_url = reverse('review_app:venue_reviews', args=[self.venue.id])
        response = self.client.get(venue_reviews_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Amazing experience!')

        # Step 4: Provider views reviews through their profile (accounts_app integration)
        self.client.logout()
        self.client.login(email='<EMAIL>', password='ProviderPass123!')

        provider_reviews_url = reverse('review_app:provider_venue_reviews')
        response = self.client.get(provider_reviews_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Amazing experience!')

        # Verify provider can only see reviews for their venues
        reviews_in_context = response.context.get('reviews', [])
        for review in reviews_in_context:
            self.assertEqual(review.venue.service_provider, self.provider_profile)

    def test_security_and_permissions_workflow(self):
        """Test security controls and permission-based access."""
        # Create a review
        review = Review.objects.create(
            venue=self.venue,
            customer=self.customer,
            rating=4,
            written_review='Good service.'
        )

        # Create another customer and provider
        other_customer = User.objects.create_user(
            email='<EMAIL>',
            password='OtherPass123!',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(user=other_customer)

        other_provider = User.objects.create_user(
            email='<EMAIL>',
            password='OtherProviderPass123!',
            role=User.SERVICE_PROVIDER
        )
        other_provider_profile = ServiceProviderProfile.objects.create(
            user=other_provider,
            business_name='Other Test Spa',
            business_phone_number='+**********',
            contact_person_name='Other Owner',
            business_address='789 Other St',
            city='Integration City',
            state='NY',
            zip_code='10001'
        )

        # Test 1: Customer cannot edit another customer's review
        self.client.login(email='<EMAIL>', password='OtherPass123!')
        edit_review_url = reverse('review_app:edit_review', args=[review.slug])
        response = self.client.post(edit_review_url, {
            'rating': 1,
            'written_review': 'Trying to edit someone else review'
        })
        self.assertEqual(response.status_code, 403)  # Forbidden

        # Verify original review unchanged
        review.refresh_from_db()
        self.assertEqual(review.rating, 4)
        self.assertEqual(review.written_review, 'Good service.')

        # Test 2: Provider cannot respond to reviews for other venues
        self.client.logout()
        self.client.login(email='<EMAIL>', password='OtherProviderPass123!')
        respond_url = reverse('review_app:provider_respond_to_review', args=[review.slug])
        response = self.client.post(respond_url, {
            'response_text': 'Unauthorized response attempt'
        })
        self.assertEqual(response.status_code, 403)  # Forbidden

        # Verify no response was created
        self.assertFalse(ReviewResponse.objects.filter(review=review).exists())

        # Test 3: Unauthenticated users cannot submit reviews
        self.client.logout()
        submit_review_url = reverse('review_app:submit_review', args=[self.venue.id])
        response = self.client.post(submit_review_url, {
            'rating': 3,
            'written_review': 'Unauthorized review attempt'
        })
        self.assertEqual(response.status_code, 302)  # Redirect to login

        # Test 4: Only admin can access admin moderation views
        self.client.login(email='<EMAIL>', password='CustomerPass123!')
        admin_reviews_url = reverse('review_app:admin_review_moderation')
        response = self.client.get(admin_reviews_url)
        self.assertEqual(response.status_code, 403)  # Forbidden


class ReviewEdgeCasesIntegrationTest(TestCase):
    """Integration tests for edge cases and error handling in review_app."""

    def setUp(self):
        """Set up test data for edge case testing."""
        self.client = Client()

        # Create test users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            role=User.SERVICE_PROVIDER
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Edge Case Test Spa',
            business_phone_number='+**********',
            contact_person_name='John Doe',
            business_address='456 Edge St',
            city='Edge City',
            state='NY',
            zip_code='10001'
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name='Spa & Wellness',
            category_description='Relaxation and wellness services'
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Edge Case Test Spa',
            short_description='A spa for edge case testing',
            state='California',
            county='Los Angeles County',
            city='Los Angeles',
            street_number='456',
            street_name='Edge Case Blvd',
            operating_hours='10AM-8PM',
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        self.venue.categories.add(self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Edge Case Massage',
            short_description='A massage for edge case testing',
            price_min=Decimal('60.00'),
            price_max=Decimal('100.00'),
            duration_minutes=45,
            is_active=True
        )

    def test_review_without_completed_booking(self):
        """Test that customers cannot review venues without completed bookings."""
        # Customer tries to submit review without a completed booking
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        submit_review_url = reverse('review_app:submit_review', args=[self.venue.id])
        review_data = {
            'rating': 4,
            'written_review': 'Trying to review without booking.'
        }
        response = self.client.post(submit_review_url, review_data)

        # Should be redirected with error message
        self.assertEqual(response.status_code, 302)

        # Verify no review was created
        self.assertFalse(Review.objects.filter(
            venue=self.venue,
            customer=self.customer
        ).exists())

    def test_duplicate_review_prevention(self):
        """Test that customers cannot submit multiple reviews for the same venue."""
        # Create a completed booking
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status=Booking.COMPLETED,
            total_price=Decimal('80.00')
        )

        # Create first review
        review = Review.objects.create(
            venue=self.venue,
            customer=self.customer,
            rating=4,
            written_review='First review for this venue.'
        )

        # Customer tries to submit another review
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        submit_review_url = reverse('review_app:submit_review', args=[self.venue.id])
        review_data = {
            'rating': 5,
            'written_review': 'Trying to submit duplicate review.'
        }
        response = self.client.post(submit_review_url, review_data)

        # Should be redirected to edit existing review
        self.assertEqual(response.status_code, 302)

        # Verify only one review exists
        reviews = Review.objects.filter(venue=self.venue, customer=self.customer)
        self.assertEqual(reviews.count(), 1)
        self.assertEqual(reviews.first().written_review, 'First review for this venue.')

    def test_review_for_nonexistent_venue(self):
        """Test handling of review submission for non-existent venue."""
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        # Try to submit review for non-existent venue
        submit_review_url = reverse('review_app:submit_review', args=[99999])
        review_data = {
            'rating': 4,
            'written_review': 'Review for non-existent venue.'
        }
        response = self.client.post(submit_review_url, review_data)

        # Should return 404
        self.assertEqual(response.status_code, 404)

    def test_flag_own_review_prevention(self):
        """Test that customers cannot flag their own reviews."""
        # Create a completed booking and review
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status=Booking.COMPLETED,
            total_price=Decimal('75.00')
        )

        review = Review.objects.create(
            venue=self.venue,
            customer=self.customer,
            rating=3,
            written_review='My own review that I want to flag.'
        )

        # Customer tries to flag their own review
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        flag_url = reverse('review_app:flag_review', args=[review.slug])
        flag_data = {
            'reason': 'other',
            'reason_text': 'Trying to flag my own review.'
        }
        response = self.client.post(flag_url, flag_data)

        # Should be forbidden or redirected with error
        self.assertIn(response.status_code, [403, 302])

        # Verify no flag was created
        self.assertFalse(ReviewFlag.objects.filter(
            review=review,
            flagged_by=self.customer
        ).exists())

    def test_invalid_rating_values(self):
        """Test handling of invalid rating values in review submission."""
        # Create a completed booking
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status=Booking.COMPLETED,
            total_price=Decimal('90.00')
        )

        self.client.login(email='<EMAIL>', password='CustomerPass123!')
        submit_review_url = reverse('review_app:submit_review', args=[self.venue.id])

        # Test rating below minimum (0)
        response = self.client.post(submit_review_url, {
            'rating': 0,
            'written_review': 'Invalid rating test.'
        })
        self.assertFalse(Review.objects.filter(venue=self.venue, customer=self.customer).exists())

        # Test rating above maximum (6)
        response = self.client.post(submit_review_url, {
            'rating': 6,
            'written_review': 'Invalid rating test.'
        })
        self.assertFalse(Review.objects.filter(venue=self.venue, customer=self.customer).exists())

        # Test non-numeric rating
        response = self.client.post(submit_review_url, {
            'rating': 'invalid',
            'written_review': 'Invalid rating test.'
        })
        self.assertFalse(Review.objects.filter(venue=self.venue, customer=self.customer).exists())


class ReviewPerformanceIntegrationTest(TestCase):
    """Integration tests for performance and logging in review_app."""

    def setUp(self):
        """Set up test data for performance testing."""
        self.client = Client()

        # Create test users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='ProviderPass123!',
            role=User.SERVICE_PROVIDER
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Performance Test Spa',
            business_phone_number='+**********',
            contact_person_name='John Doe',
            business_address='789 Performance Dr',
            city='Performance City',
            state='NY',
            zip_code='10001'
        )

        # Create category and venue
        self.category = Category.objects.create(
            category_name='Spa & Wellness',
            category_description='Relaxation and wellness services'
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Performance Test Spa',
            short_description='A spa for performance testing',
            state='Texas',
            county='Harris County',
            city='Houston',
            street_number='789',
            street_name='Performance Dr',
            operating_hours='8AM-10PM',
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        self.venue.categories.add(self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Performance Test Massage',
            short_description='A massage for performance testing',
            price_min=Decimal('70.00'),
            price_max=Decimal('110.00'),
            duration_minutes=50,
            is_active=True
        )

        # Create completed booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            status=Booking.COMPLETED,
            total_price=Decimal('85.00')
        )

    @patch('review_app.logging_utils.log_review_creation')
    def test_review_creation_logging(self, mock_log_creation):
        """Test that review creation is properly logged."""
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        submit_review_url = reverse('review_app:submit_review', args=[self.venue.id])
        review_data = {
            'rating': 5,
            'written_review': 'Excellent service with proper logging!'
        }
        response = self.client.post(submit_review_url, review_data)

        # Verify review was created
        review = Review.objects.get(venue=self.venue, customer=self.customer)
        self.assertEqual(review.rating, 5)

        # Verify logging was called
        mock_log_creation.assert_called_once()

    @patch('review_app.logging_utils.log_review_response')
    def test_provider_response_logging(self, mock_log_response):
        """Test that provider responses are properly logged."""
        # Create a review first
        review = Review.objects.create(
            venue=self.venue,
            customer=self.customer,
            rating=4,
            written_review='Good service for logging test.'
        )

        self.client.login(email='<EMAIL>', password='ProviderPass123!')

        respond_url = reverse('review_app:provider_respond_to_review', args=[review.id])
        response_data = {
            'response_text': 'Thank you for your feedback with logging!'
        }
        response = self.client.post(respond_url, response_data)

        # Verify response was created
        review_response = ReviewResponse.objects.get(review=review)
        self.assertEqual(review_response.response_text, 'Thank you for your feedback with logging!')

        # Verify logging was called
        mock_log_response.assert_called_once()

    @patch('review_app.logging_utils.log_review_flag')
    def test_review_flagging_logging(self, mock_log_flag):
        """Test that review flagging is properly logged."""
        # Create a review
        review = Review.objects.create(
            venue=self.venue,
            customer=self.customer,
            rating=2,
            written_review='Poor service for flagging test.'
        )

        # Create another customer to flag the review
        flagger = User.objects.create_user(
            email='<EMAIL>',
            password='FlaggerPass123!',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(user=flagger)

        self.client.login(email='<EMAIL>', password='FlaggerPass123!')

        flag_url = reverse('review_app:flag_review', args=[review.slug])
        flag_data = {
            'reason': 'inappropriate',
            'reason_text': 'Inappropriate content with logging test.'
        }
        response = self.client.post(flag_url, flag_data)

        # Verify flag was created
        flag = ReviewFlag.objects.get(review=review, flagged_by=flagger)
        self.assertEqual(flag.reason, 'Inappropriate content with logging test.')

        # Verify logging was called
        mock_log_flag.assert_called_once()

    def test_bulk_review_operations_performance(self):
        """Test performance with multiple reviews and operations."""
        # Create multiple customers and reviews
        customers = []
        reviews = []

        for i in range(10):
            customer = User.objects.create_user(
                email=f'customer{i}@example.com',
                password='CustomerPass123!',
                role=User.CUSTOMER
            )
            CustomerProfile.objects.create(user=customer)
            customers.append(customer)

            # Create completed booking for each customer
            booking = Booking.objects.create(
                customer=customer,
                venue=self.venue,
                status=Booking.COMPLETED,
                total_price=Decimal('80.00')
            )

            # Create review
            review = Review.objects.create(
                venue=self.venue,
                customer=customer,
                rating=(i % 5) + 1,  # Ratings from 1-5
                written_review=f'Review number {i+1} for performance testing.'
            )
            reviews.append(review)

        # Test venue reviews view performance with multiple reviews
        venue_reviews_url = reverse('review_app:venue_reviews', args=[self.venue.id])
        response = self.client.get(venue_reviews_url)
        self.assertEqual(response.status_code, 200)

        # Verify all reviews are displayed
        for review in reviews:
            self.assertContains(response, f'Review number {review.customer.id}')

        # Test provider reviews view performance
        self.client.login(email='<EMAIL>', password='ProviderPass123!')
        provider_reviews_url = reverse('review_app:provider_venue_reviews')
        response = self.client.get(provider_reviews_url)
        self.assertEqual(response.status_code, 200)

        # Verify provider can see all reviews for their venue
        reviews_in_context = response.context.get('reviews', [])
        self.assertEqual(len(reviews_in_context), 10)

    def test_cross_app_data_consistency(self):
        """Test data consistency across apps during review operations."""
        # Create review
        self.client.login(email='<EMAIL>', password='CustomerPass123!')

        submit_review_url = reverse('review_app:submit_review', args=[self.venue.id])
        review_data = {
            'rating': 5,
            'written_review': 'Consistency test review.'
        }
        response = self.client.post(submit_review_url, review_data)

        # Verify review exists in review_app
        review = Review.objects.get(venue=self.venue, customer=self.customer)
        self.assertEqual(review.rating, 5)

        # Verify venue still exists in venues_app
        venue_from_venues_app = Venue.objects.get(id=self.venue.id)
        self.assertEqual(venue_from_venues_app.venue_name, 'Performance Test Spa')

        # Verify booking still exists in booking_cart_app
        booking_from_booking_app = Booking.objects.get(id=self.booking.id)
        self.assertEqual(booking_from_booking_app.customer, self.customer)

        # Verify user profiles still exist in accounts_app
        customer_profile = CustomerProfile.objects.get(user=self.customer)
        provider_profile = ServiceProviderProfile.objects.get(user=self.provider)
        self.assertEqual(customer_profile.user, self.customer)
        self.assertEqual(provider_profile.user, self.provider)
