"""
Unit tests for the review_app logging system.

This module tests the logging utilities and ensures proper logging
functionality across the review_app.
"""

import logging
import tempfile
import os
from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.utils import timezone

from review_app.logging_utils import (
    log_review_creation, log_review_response, log_review_response_update,
    log_review_flag, log_review_flag_resolution, log_review_moderation,
    log_review_error, log_unauthorized_review_access, performance_monitor
)
from review_app.models import Review, ReviewResponse, ReviewFlag
from venues_app.models import Venue, Category
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


class ReviewLoggingTest(TestCase):
    """Test the review_app logging system."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        
        # Create test users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )
        
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )
        
        self.admin = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.ADMIN,
            is_staff=True
        )
        
        # Create provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Test Spa',
            business_phone_number='+**********',
            contact_person_name='John Doe',
            business_address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )
        
        # Create test category and venue
        self.category = Category.objects.create(
            name='Spa Services',
            description='Relaxing spa treatments'
        )
        
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            category=self.category,
            description='A relaxing spa venue',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            zip_code='12345',
            phone_number='+**********'
        )
        
        # Create test review
        self.review = Review.objects.create(
            customer=self.customer,
            venue=self.venue,
            rating=4,
            written_review='Great experience! The staff was very friendly and professional.'
        )

    @patch('review_app.logging_utils.log_user_activity')
    def test_log_review_creation(self, mock_log_activity):
        """Test logging review creation."""
        request = self.factory.post('/reviews/submit/')
        request.user = self.customer
        
        log_review_creation(
            user=self.customer,
            review=self.review,
            request=request,
            additional_details={'submission_method': 'web_form'}
        )
        
        # Verify log_user_activity was called with correct parameters
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        
        self.assertEqual(call_args[1]['app_name'], 'review_app')
        self.assertEqual(call_args[1]['activity_type'], 'review_creation')
        self.assertEqual(call_args[1]['user'], self.customer)
        self.assertEqual(call_args[1]['request'], request)
        
        # Check details
        details = call_args[1]['details']
        self.assertEqual(details['review_id'], self.review.id)
        self.assertEqual(details['venue_id'], self.venue.id)
        self.assertEqual(details['venue_name'], self.venue.venue_name)
        self.assertEqual(details['rating'], self.review.rating)
        self.assertEqual(details['submission_method'], 'web_form')

    @patch('review_app.logging_utils.log_user_activity')
    def test_review_creation_includes_length(self, mock_log_activity):
        request = self.factory.post('/reviews/submit/')
        request.user = self.customer

        log_review_creation(user=self.customer, review=self.review, request=request)

        details = mock_log_activity.call_args[1]['details']
        self.assertEqual(details['review_length'], len(self.review.written_review))

    @patch('review_app.logging_utils.log_user_activity')
    def test_log_review_response(self, mock_log_activity):
        """Test logging review response."""
        response = ReviewResponse.objects.create(
            review=self.review,
            provider=self.provider,
            response_text='Thank you for your review!'
        )
        
        request = self.factory.post('/reviews/respond/')
        request.user = self.provider
        
        log_review_response(
            user=self.provider,
            review=self.review,
            response=response,
            request=request
        )
        
        # Verify log_user_activity was called (signals may also call it)
        self.assertTrue(mock_log_activity.called)

        # Check that at least one call has the expected parameters
        found_expected_call = False
        for call in mock_log_activity.call_args_list:
            if (call[1].get('app_name') == 'review_app' and
                call[1].get('activity_type') == 'review_response' and
                call[1].get('user') == self.provider and
                call[1].get('request') == request):

                details = call[1]['details']
                if (details.get('review_id') == self.review.id and
                    details.get('response_id') == response.id and
                    details.get('venue_id') == self.venue.id):
                    found_expected_call = True
                    break

        self.assertTrue(found_expected_call, "Expected log_user_activity call not found")

    @patch('review_app.logging_utils.log_user_activity')
    def test_log_review_response_update(self, mock_log_activity):
        """Test logging review response update."""
        response = ReviewResponse.objects.create(
            review=self.review,
            provider=self.provider,
            response_text='Original response'
        )
        
        request = self.factory.post('/reviews/edit-response/')
        request.user = self.provider
        
        log_review_response_update(
            user=self.provider,
            review=self.review,
            response=response,
            request=request,
            additional_details={'old_text': 'Original response'}
        )
        
        # Verify log_user_activity was called (signals may also call it)
        self.assertTrue(mock_log_activity.called)

        # Check that at least one call has the expected parameters
        found_expected_call = False
        for call in mock_log_activity.call_args_list:
            if (call[1].get('activity_type') == 'review_response_updated' and
                call[1].get('user') == self.provider and
                call[1].get('request') == request):

                details = call[1]['details']
                if details.get('old_text') == 'Original response':
                    found_expected_call = True
                    break

        self.assertTrue(found_expected_call, "Expected log_user_activity call not found")

    @patch('review_app.logging_utils.log_security_event')
    def test_log_review_flag(self, mock_log_security):
        """Test logging review flag."""
        flag = ReviewFlag.objects.create(
            review=self.review,
            flagged_by=self.customer,
            reason='Inappropriate content'
        )
        
        request = self.factory.post('/reviews/flag/')
        request.user = self.customer
        
        log_review_flag(
            user=self.customer,
            review=self.review,
            flag=flag,
            request=request
        )
        
        # Verify log_security_event was called (signals may also call it)
        self.assertTrue(mock_log_security.called)

        # Check that at least one call has the expected parameters
        found_expected_call = False
        for call in mock_log_security.call_args_list:
            if (call[1].get('app_name') == 'review_app' and
                call[1].get('event_type') == 'review_flagged' and
                call[1].get('user_email') == self.customer.email and
                call[1].get('request') == request):

                details = call[1]['details']
                if (details.get('review_id') == self.review.id and
                    details.get('flag_id') == flag.id and
                    details.get('flag_reason') == flag.reason):
                    found_expected_call = True
                    break

        self.assertTrue(found_expected_call, "Expected log_security_event call not found")

    @patch('review_app.logging_utils.log_audit_event')
    def test_log_review_flag_resolution(self, mock_log_audit):
        """Test logging review flag resolution."""
        flag = ReviewFlag.objects.create(
            review=self.review,
            flagged_by=self.customer,
            reason='Inappropriate content'
        )
        
        request = self.factory.post('/admin/resolve-flag/')
        request.user = self.admin
        
        log_review_flag_resolution(
            admin_user=self.admin,
            flag=flag,
            action='resolved',
            request=request
        )
        
        # Verify log_audit_event was called
        mock_log_audit.assert_called_once()
        call_args = mock_log_audit.call_args
        
        self.assertEqual(call_args[1]['app_name'], 'review_app')
        self.assertEqual(call_args[1]['action'], 'flag_resolution')
        self.assertEqual(call_args[1]['admin_user'], self.admin)
        
        # Check details
        details = call_args[1]['details']
        self.assertEqual(details['flag_id'], flag.id)
        self.assertEqual(details['resolution_action'], 'resolved')

    @patch('review_app.logging_utils.log_audit_event')
    def test_log_review_moderation(self, mock_log_audit):
        """Test logging review moderation."""
        request = self.factory.post('/admin/moderate-review/')
        request.user = self.admin
        
        log_review_moderation(
            admin_user=self.admin,
            review=self.review,
            action='content_edited',
            request=request,
            additional_details={'changes': 'Updated inappropriate language'}
        )
        
        # Verify log_audit_event was called
        mock_log_audit.assert_called_once()
        call_args = mock_log_audit.call_args
        
        self.assertEqual(call_args[1]['action'], 'review_moderation')
        
        # Check details
        details = call_args[1]['details']
        self.assertEqual(details['review_id'], self.review.id)
        self.assertEqual(details['moderation_action'], 'content_edited')
        self.assertEqual(details['changes'], 'Updated inappropriate language')

    @patch('review_app.logging_utils.log_error')
    def test_log_review_error(self, mock_log_error):
        """Test logging review errors."""
        request = self.factory.post('/reviews/submit/')
        request.user = self.customer
        
        test_exception = ValueError("Invalid rating value")
        
        log_review_error(
            error_type='validation_error',
            error_message='Invalid rating provided',
            user=self.customer,
            request=request,
            exception=test_exception,
            review_context={'review_id': self.review.id, 'venue_id': self.venue.id}
        )
        
        # Verify log_error was called
        mock_log_error.assert_called_once()
        call_args = mock_log_error.call_args
        
        self.assertEqual(call_args[1]['app_name'], 'review_app')
        self.assertEqual(call_args[1]['error_type'], 'validation_error')
        self.assertEqual(call_args[1]['error_message'], 'Invalid rating provided')
        self.assertEqual(call_args[1]['user'], self.customer)
        self.assertEqual(call_args[1]['exception'], test_exception)
        
        # Check details
        details = call_args[1]['details']
        self.assertEqual(details['error_category'], 'review_operation')
        self.assertEqual(details['review_id'], self.review.id)

    @patch('review_app.logging_utils.log_security_event')
    def test_log_unauthorized_review_access(self, mock_log_security):
        """Test logging unauthorized review access."""
        request = self.factory.get('/reviews/edit/123/')
        request.user = self.customer
        
        log_unauthorized_review_access(
            user_email=self.customer.email,
            attempted_action='edit_review',
            review_id=self.review.id,
            request=request,
            additional_details={'reason': 'User not review owner'}
        )
        
        # Verify log_security_event was called
        mock_log_security.assert_called_once()
        call_args = mock_log_security.call_args
        
        self.assertEqual(call_args[1]['app_name'], 'review_app')
        self.assertEqual(call_args[1]['event_type'], 'unauthorized_access')
        self.assertEqual(call_args[1]['user_email'], self.customer.email)
        
        # Check details
        details = call_args[1]['details']
        self.assertEqual(details['review_id'], self.review.id)
        self.assertEqual(details['attempted_action'], 'edit_review')
        self.assertEqual(details['reason'], 'User not review owner')

    @patch('review_app.logging_utils.log_performance')
    def test_performance_monitor_decorator(self, mock_log_performance):
        """Test the performance monitor decorator."""
        @performance_monitor('test_operation')
        def test_function(request):
            return "test_result"
        
        request = self.factory.get('/test/')
        request.user = self.customer
        
        result = test_function(request)
        
        # Verify function executed correctly
        self.assertEqual(result, "test_result")
        
        # Verify performance was logged
        mock_log_performance.assert_called_once()
        call_args = mock_log_performance.call_args
        
        self.assertEqual(call_args[1]['app_name'], 'review_app')
        self.assertEqual(call_args[1]['operation'], 'test_operation')
        self.assertIsInstance(call_args[1]['duration'], float)
        self.assertEqual(call_args[1]['user'], self.customer)

    def test_performance_monitor_without_user(self):
        """Test performance monitor with no authenticated user."""
        @performance_monitor('test_operation')
        def test_function():
            return "test_result"
        
        with patch('review_app.logging_utils.log_performance') as mock_log_performance:
            result = test_function()
            
            # Verify function executed correctly
            self.assertEqual(result, "test_result")
            
            # Verify performance was logged with no user
            mock_log_performance.assert_called_once()
            call_args = mock_log_performance.call_args
            self.assertIsNone(call_args[1]['user'])

    @patch('review_app.logging_utils.get_app_logger')
    def test_logging_configuration(self, mock_get_logger):
        """Test that logging is properly configured."""
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger
        
        # Test that logger is requested with correct app name
        from review_app.logging_utils import log_review_creation
        
        # This should trigger logger creation
        log_review_creation(self.customer, self.review)
        
        # Verify logger was requested (indirectly through log_user_activity)
        # This test ensures the logging infrastructure is properly set up

    def test_logging_with_none_values(self):
        """Test logging functions handle None values gracefully."""
        with patch('review_app.logging_utils.log_user_activity') as mock_log:
            # Test with None request
            log_review_creation(
                user=self.customer,
                review=self.review,
                request=None
            )
            
            # Should not raise an exception
            mock_log.assert_called_once()
            call_args = mock_log.call_args
            self.assertIsNone(call_args[1]['request'])

    def test_logging_with_additional_details(self):
        """Test that additional details are properly included in logs."""
        with patch('review_app.logging_utils.log_user_activity') as mock_log:
            additional_details = {
                'custom_field': 'custom_value',
                'numeric_field': 123,
                'boolean_field': True
            }
            
            log_review_creation(
                user=self.customer,
                review=self.review,
                additional_details=additional_details
            )
            
            # Verify additional details are included
            call_args = mock_log.call_args
            details = call_args[1]['details']
            
            self.assertEqual(details['custom_field'], 'custom_value')
            self.assertEqual(details['numeric_field'], 123)
            self.assertEqual(details['boolean_field'], True)

    def test_error_logging_with_exception(self):
        """Test error logging includes exception details."""
        with patch('review_app.logging_utils.log_error') as mock_log_error:
            test_exception = Exception("Test exception message")
            
            log_review_error(
                error_type='test_error',
                error_message='Test error occurred',
                exception=test_exception
            )
            
            # Verify exception was passed to log_error
            call_args = mock_log_error.call_args
            self.assertEqual(call_args[1]['exception'], test_exception)

    def test_logging_timestamp_inclusion(self):
        """Test that timestamps are included in log details."""
        with patch('review_app.logging_utils.log_user_activity') as mock_log:
            log_review_creation(
                user=self.customer,
                review=self.review
            )
            
            # Verify timestamp is included
            call_args = mock_log.call_args
            details = call_args[1]['details']
            self.assertIn('timestamp', details)
            
            # Verify timestamp format (ISO format)
            timestamp = details['timestamp']
            self.assertIsInstance(timestamp, str)
            # Basic check that it looks like an ISO timestamp
            self.assertIn('T', timestamp)

    def test_logging_user_context(self):
        """Test that user context is properly captured in logs."""
        with patch('review_app.logging_utils.log_user_activity') as mock_log:
            log_review_creation(
                user=self.customer,
                review=self.review
            )
            
            # Verify user is passed correctly
            call_args = mock_log.call_args
            self.assertEqual(call_args[1]['user'], self.customer)
            
            # Verify app_name is correct
            self.assertEqual(call_args[1]['app_name'], 'review_app')
