# --- Third-Party Imports ---
import bleach
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import Review


class ReviewForm(forms.ModelForm):
    """Form for customers to create reviews for venues."""

    class Meta:
        model = Review
        fields = ['rating', 'written_review']
        widgets = {
            'rating': forms.Select(
                choices=Review.RATING_CHOICES,
                attrs={
                    'class': 'form-select',
                    'required': True
                }
            ),
            'written_review': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': _('Share your experience with this venue...'),
                'maxlength': 1000,
                'required': True
            })
        }
        labels = {
            'rating': _('Your Rating'),
            'written_review': _('Your Review')
        }
        help_texts = {
            'rating': _('Rate your overall experience from 1 to 5 stars'),
            'written_review': _('Tell others about your experience (maximum 1000 characters)')
        }

    def __init__(self, *args, **kwargs):
        self.customer = kwargs.pop('customer', None)
        self.venue = kwargs.pop('venue', None)
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes and validation
        for field in self.fields.values():
            if hasattr(field.widget, 'attrs'):
                field.widget.attrs.update({'class': field.widget.attrs.get('class', '') + ' form-control'})

    def clean_written_review(self):
        written_review = self.cleaned_data.get('written_review')
        if not written_review or len(written_review.strip()) < 10:
            raise ValidationError(_('Review must be at least 10 characters long.'))
        inappropriate_words = ['spam', 'fake', 'scam']
        review_lower = written_review.lower()
        for word in inappropriate_words:
            if word in review_lower:
                raise ValidationError(_('Please keep your review professional and appropriate.'))
        cleaned = bleach.clean(written_review, tags=[], strip=True)
        return cleaned.strip()

    def clean_rating(self):
        rating = self.cleaned_data.get('rating')
        if not rating or rating < 1 or rating > 5:
            raise ValidationError(_('Please select a rating between 1 and 5 stars.'))
        return rating

    def save(self, commit=True):
        review = super().save(commit=False)
        if self.customer:
            review.customer = self.customer
        if self.venue:
            review.venue = self.venue
        if commit:
            review.save()
        return review


class ReviewEditForm(forms.ModelForm):
    """Form for customers to edit their existing reviews."""

    class Meta:
        model = Review
        fields = ['rating', 'written_review']
        widgets = {
            'rating': forms.Select(
                choices=Review.RATING_CHOICES,
                attrs={'class': 'form-select', 'required': True}
            ),
            'written_review': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'maxlength': 1000,
                'required': True
            })
        }
        labels = {
            'rating': _('Your Rating'),
            'written_review': _('Your Review')
        }

    def clean_written_review(self):
        written_review = self.cleaned_data.get('written_review')
        if not written_review or len(written_review.strip()) < 10:
            raise ValidationError(_('Review must be at least 10 characters long.'))
        cleaned = bleach.clean(written_review, tags=[], strip=True)
        return cleaned.strip()

    def clean_rating(self):
        rating = self.cleaned_data.get('rating')
        if not rating or rating < 1 or rating > 5:
            raise ValidationError(_('Please select a rating between 1 and 5 stars.'))
        return rating
