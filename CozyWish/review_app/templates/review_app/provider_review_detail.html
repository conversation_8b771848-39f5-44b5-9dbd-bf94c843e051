{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}{{ page_title }} - {{ review.venue.venue_name }}{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2>{{ page_title }}</h2>
                    <p class="mb-0">Review details for {{ review.venue.venue_name }}</p>
                </div>
                <div class="card-body">
                    <div class="review-detail">
                        <div class="review-header d-flex justify-content-between mb-3">
                            <div>
                                <h5>Customer Review</h5>
                                <div>
                                    <strong>{{ review.customer.first_name|default:"Anonymous" }}</strong>
                                    <span class="rating">{{ review.rating }}/5 stars</span>
                                    {% if review.is_flagged %}
                                        <span class="badge badge-warning">Flagged</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="text-right">
                                <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                                <div class="review-actions mt-1">
                                    {% if not review.response %}
                                        <a href="{% url 'review_app:provider_respond_to_review' review.slug %}" class="btn btn-sm btn-primary">Respond</a>
                                    {% else %}
                                        <a href="{% url 'review_app:provider_edit_response' review.response.id %}" class="btn btn-sm btn-outline-primary">Edit Response</a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="review-content">
                            <h6>Customer's Review:</h6>
                            <p class="review-text">{{ review.written_review }}</p>
                        </div>
                        
                        {% if review.response %}
                            <div class="provider-response mt-4 p-3 bg-light">
                                <h6>Your Response:</h6>
                                <p>{{ review.response.response_text }}</p>
                                <small class="text-muted">Responded on {{ review.response.created_at|date:"M d, Y" }}</small>
                                {% if review.response.updated_at != review.response.created_at %}
                                    <small class="text-muted"> (Last updated: {{ review.response.updated_at|date:"M d, Y" }})</small>
                                {% endif %}
                            </div>
                        {% endif %}
                        
                        {% if review.is_flagged %}
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-flag"></i> This review has been flagged and is under moderation.
                            </div>
                        {% endif %}
                        
                        {% if not review.is_approved %}
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-clock"></i> This review is pending approval.
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mt-4">
                        <a href="{% url 'review_app:provider_venue_reviews' %}" class="btn btn-secondary">Back to All Reviews</a>
                        <a href="{% url 'review_app:provider_review_summary' %}" class="btn btn-outline-primary">View Summary</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
