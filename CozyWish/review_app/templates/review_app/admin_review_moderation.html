{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}Review Moderation{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>Review Moderation</h1>
            <p class="lead">Manage and moderate customer reviews</p>
            
            {% if stats %}
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ stats.total_reviews }}</h3>
                                <p>Total Reviews</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ stats.pending_reviews }}</h3>
                                <p>Pending Reviews</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ stats.flagged_reviews }}</h3>
                                <p>Flagged Reviews</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3>{{ stats.approved_reviews }}</h3>
                                <p>Approved Reviews</p>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
            
            <div class="card">
                <div class="card-header">
                    <h5>Reviews Requiring Attention</h5>
                </div>
                <div class="card-body">
                    {% if reviews %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Venue</th>
                                        <th>Rating</th>
                                        <th>Review</th>
                                        <th>Status</th>
                                        <th>Flags</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for review in reviews %}
                                        <tr>
                                            <td>{{ review.customer.first_name|default:"Anonymous" }}</td>
                                            <td>{{ review.venue.venue_name }}</td>
                                            <td>{{ review.rating }}/5</td>
                                            <td>{{ review.written_review|truncatewords:10 }}</td>
                                            <td>
                                                {% if review.is_approved %}
                                                    <span class="badge badge-success">Approved</span>
                                                {% else %}
                                                    <span class="badge badge-warning">Pending</span>
                                                {% endif %}
                                                {% if review.is_flagged %}
                                                    <span class="badge badge-danger">Flagged</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if review.pending_count > 0 %}
                                                    <span class="badge badge-warning">{{ review.pending_count }}</span>
                                                {% else %}
                                                    <span class="text-muted">0</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ review.created_at|date:"M d, Y" }}</td>
                                            <td>
                                                <a href="{% url 'review_app:admin_moderate_review' review.slug %}" class="btn btn-sm btn-primary">Moderate</a>
                                                {% if review.pending_count > 0 %}
                                                    <a href="{% url 'review_app:admin_manage_flags' %}?review={{ review.id }}" class="btn btn-sm btn-warning">View Flags</a>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        {% if reviews.has_other_pages %}
                            <nav aria-label="Reviews pagination">
                                <ul class="pagination">
                                    {% if reviews.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ reviews.previous_page_number }}">Previous</a>
                                        </li>
                                    {% endif %}
                                    
                                    <li class="page-item active">
                                        <span class="page-link">{{ reviews.number }} of {{ reviews.paginator.num_pages }}</span>
                                    </li>
                                    
                                    {% if reviews.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ reviews.next_page_number }}">Next</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <h5>No reviews requiring attention</h5>
                            <p>All reviews are currently approved and no flags are pending.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
