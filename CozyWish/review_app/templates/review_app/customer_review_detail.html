{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}{{ page_title }} - {{ review.venue.venue_name }}{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2>{{ page_title }}</h2>
                    <p class="mb-0">Your review for {{ review.venue.venue_name }}</p>
                </div>
                <div class="card-body">
                    <div class="review-detail">
                        <div class="review-header d-flex justify-content-between mb-3">
                            <div>
                                <h5>{{ review.venue.venue_name }}</h5>
                                <span class="rating">{{ review.rating }}/5 stars</span>
                            </div>
                            <div class="text-right">
                                <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                                <div class="review-actions mt-1">
                                    <a href="{% url 'review_app:edit_review' review.slug %}" class="btn btn-sm btn-outline-primary">Edit Review</a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="review-content">
                            <h6>Your Review:</h6>
                            <p class="review-text">{{ review.written_review }}</p>
                        </div>
                        
                        {% if review.response %}
                            <div class="provider-response mt-4 p-3 bg-light">
                                <h6>Response from {{ review.venue.venue_name }}:</h6>
                                <p>{{ review.response.response_text }}</p>
                                <small class="text-muted">Responded on {{ review.response.created_at|date:"M d, Y" }}</small>
                            </div>
                        {% endif %}
                        
                        {% if review.is_flagged %}
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-flag"></i> This review has been flagged and is under moderation.
                            </div>
                        {% endif %}
                        
                        {% if not review.is_approved %}
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-clock"></i> This review is pending approval.
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mt-4">
                        <a href="{% url 'review_app:customer_review_history' %}" class="btn btn-secondary">Back to My Reviews</a>
                        <a href="{% url 'review_app:venue_reviews' review.venue.id %}" class="btn btn-outline-primary">View All Venue Reviews</a>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5>{{ venue.venue_name }}</h5>
                        <p>{{ venue.short_description }}</p>
                        <p><strong>Location:</strong> {{ venue.city }}, {{ venue.state }}</p>
                        <a href="{% url 'venues_app:venue_detail' venue.slug %}" class="btn btn-outline-primary">View Venue Details</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
