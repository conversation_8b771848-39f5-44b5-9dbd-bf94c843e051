{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}Flag Review - {{ venue.venue_name }}{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2>{{ page_title }}</h2>
                    <p class="mb-0">Report inappropriate content</p>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <strong>Review to be flagged:</strong>
                        <div class="mt-2 p-3 bg-light">
                            <div class="d-flex justify-content-between">
                                <span><strong>Rating:</strong> {{ review.rating }}/5 stars</span>
                                <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                            </div>
                            <p class="mt-2">{{ review.written_review }}</p>
                        </div>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.reason.id_for_label }}" class="form-label">Reason for flagging *</label>
                            {{ form.reason }}
                            {% if form.reason.errors %}
                                <div class="text-danger">
                                    {% for error in form.reason.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Please provide a clear reason for flagging this review. Our moderation team will review your report.
                            </small>
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div class="alert alert-warning">
                            <strong>Please note:</strong> False or malicious reports may result in account restrictions. 
                            Only flag reviews that violate our community guidelines.
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'review_app:venue_reviews' venue.id %}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-warning">Flag Review</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
