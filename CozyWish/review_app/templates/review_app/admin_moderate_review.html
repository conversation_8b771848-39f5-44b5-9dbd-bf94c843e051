{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}Moderate Review{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h2>Moderate Review</h2>
                    <p class="mb-0">Review moderation for {{ review.venue.venue_name }}</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="review-details">
                                <h5>Review Details</h5>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between mb-2">
                                            <div>
                                                <strong>Customer:</strong> {{ review.customer.first_name|default:"Anonymous" }} ({{ review.customer.email }})
                                            </div>
                                            <div>
                                                <strong>Rating:</strong> {{ review.rating }}/5 stars
                                            </div>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Venue:</strong> {{ review.venue.venue_name }}
                                        </div>
                                        <div class="mb-2">
                                            <strong>Date:</strong> {{ review.created_at|date:"M d, Y H:i" }}
                                        </div>
                                        <div class="mb-3">
                                            <strong>Review Text:</strong>
                                            <p class="mt-2">{{ review.written_review }}</p>
                                        </div>
                                        
                                        {% if review.response %}
                                            <div class="provider-response">
                                                <strong>Provider Response:</strong>
                                                <p class="mt-2">{{ review.response.response_text }}</p>
                                                <small class="text-muted">Responded on {{ review.response.created_at|date:"M d, Y" }}</small>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                {% if review.flags.all %}
                                    <div class="mt-4">
                                        <h6>Flags</h6>
                                        {% for flag in review.flags.all %}
                                            <div class="card border-warning mb-2">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between">
                                                        <div>
                                                            <strong>Flagged by:</strong> {{ flag.flagged_by.email }}
                                                        </div>
                                                        <div>
                                                            <span class="badge badge-{{ flag.status|lower }}">{{ flag.get_status_display }}</span>
                                                        </div>
                                                    </div>
                                                    <div class="mt-2">
                                                        <strong>Reason:</strong> {{ flag.reason }}
                                                    </div>
                                                    <small class="text-muted">{{ flag.created_at|date:"M d, Y H:i" }}</small>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="moderation-actions">
                                <h5>Moderation Actions</h5>
                                
                                <form method="post">
                                    {% csrf_token %}
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Action</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="action" value="approve" id="approve">
                                            <label class="form-check-label" for="approve">
                                                Approve Review
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="action" value="reject" id="reject">
                                            <label class="form-check-label" for="reject">
                                                Reject Review
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="action" value="flag" id="flag">
                                            <label class="form-check-label" for="flag">
                                                Flag for Further Review
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="admin_notes" class="form-label">Admin Notes</label>
                                        <textarea class="form-control" name="admin_notes" id="admin_notes" rows="4" placeholder="Add notes about your moderation decision..."></textarea>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">Submit Decision</button>
                                        <a href="{% url 'review_app:admin_review_moderation' %}" class="btn btn-secondary">Cancel</a>
                                    </div>
                                </form>
                                
                                <div class="mt-4">
                                    <h6>Current Status</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>Approved:</strong> {{ review.is_approved|yesno:"Yes,No" }}</li>
                                        <li><strong>Flagged:</strong> {{ review.is_flagged|yesno:"Yes,No" }}</li>
                                        <li><strong>Flags Count:</strong> {{ review.flags.count }}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
