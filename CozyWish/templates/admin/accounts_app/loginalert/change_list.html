{% extends "admin/change_list.html" %}
{% load i18n admin_urls static admin_list %}

{% block content_title %}
    <h1>Login Alerts</h1>
    {% if show_alert_stats %}
        <div class="alert-stats" style="margin: 20px 0;">
            <div class="alert alert-warning" style="background-color: white; border: 2px solid black; padding: 15px; border-radius: 5px; color: black;">
                <h3 style="margin-top: 0; color: black;">Alert Statistics</h3>
                <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                    <div>
                        <strong style="color: black;">Total Alerts:</strong>
                        <span style="color: black;">{{ total_alerts }}</span>
                    </div>
                    <div>
                        <strong style="color: black;">Unresolved:</strong>
                        <span style="color: black; font-weight: {% if unresolved_alerts > 0 %}bold{% else %}normal{% endif %};">
                            {{ unresolved_alerts }}
                        </span>
                    </div>
                    <div>
                        <strong style="color: black;">High Priority:</strong>
                        <span style="color: black; font-weight: {% if high_priority_alerts > 0 %}bold{% else %}normal{% endif %};">
                            {{ high_priority_alerts }}
                        </span>
                    </div>
                </div>

                {% if recent_high_priority %}
                    <div style="margin-top: 15px;">
                        <strong style="color: black;">Recent High Priority Alerts:</strong>
                        <ul style="margin: 5px 0 0 20px;">
                            {% for alert in recent_high_priority %}
                                <li style="color: black; font-weight: bold;">
                                    <strong>{{ alert.get_alert_type_display }}</strong> - {{ alert.ip_address }}
                                    ({{ alert.created_at|timesince }} ago)
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <div style="margin-top: 15px;">
                    <a href="{% url 'admin:accounts_app_loginalert_changelist' %}?is_resolved__exact=0" class="button" style="background-color: black; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; border: 2px solid black;">
                        View Unresolved Alerts
                    </a>
                    <a href="{% url 'admin:accounts_app_loginalert_changelist' %}?severity__in=high,critical" class="button" style="background-color: white; color: black; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin-left: 10px; border: 2px solid black;">
                        View High Priority
                    </a>
                    <a href="{% url 'admin:accounts_app_loginhistory_changelist' %}" class="button" style="background-color: white; color: black; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin-left: 10px; border: 2px solid black;">
                        View Login History
                    </a>
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .alert-stats .alert {
            margin-bottom: 20px;
        }
        .alert-stats .button {
            display: inline-block;
            margin-right: 10px;
        }
        .alert-stats .button:hover {
            opacity: 0.8;
        }
    </style>
{% endblock %}
