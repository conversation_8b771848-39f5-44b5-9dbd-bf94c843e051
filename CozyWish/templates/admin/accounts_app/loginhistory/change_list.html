{% extends "admin/change_list.html" %}
{% load i18n admin_urls static admin_list %}

{% block content_title %}
    <h1>Login History</h1>
    {% if show_security_alerts %}
        <div class="security-alerts" style="margin: 20px 0;">
            <div class="alert alert-info" style="background-color: white; border: 2px solid black; padding: 15px; border-radius: 5px; color: black;">
                <h3 style="margin-top: 0; color: black;">Security Overview</h3>
                <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                    <div>
                        <strong style="color: black;">Failed Logins (Last Hour):</strong>
                        <span style="color: black; font-weight: {% if failed_last_hour > 10 %}bold{% elif failed_last_hour > 5 %}600{% else %}normal{% endif %};">
                            {{ failed_last_hour }}
                        </span>
                    </div>
                    <div>
                        <strong style="color: black;">Failed Logins (Last 24h):</strong>
                        <span style="color: black; font-weight: {% if failed_last_day > 50 %}bold{% elif failed_last_day > 20 %}600{% else %}normal{% endif %};">
                            {{ failed_last_day }}
                        </span>
                    </div>
                </div>

                {% if suspicious_ips %}
                    <div style="margin-top: 15px;">
                        <strong style="color: black;">Suspicious IPs (3+ failed attempts in last hour):</strong>
                        <ul style="margin: 5px 0 0 20px;">
                            {% for ip in suspicious_ips %}
                                <li style="color: black; font-weight: bold;">{{ ip.ip_address }} - {{ ip.attempt_count }} attempts</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <div style="margin-top: 15px;">
                    <a href="{% url 'admin:accounts_app_loginalert_changelist' %}" class="button" style="background-color: black; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; border: 2px solid black;">
                        View Login Alerts
                    </a>
                    <a href="{% url 'admin:accounts_app_loginhistory_changelist' %}?is_successful__exact=0" class="button" style="background-color: white; color: black; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin-left: 10px; border: 2px solid black;">
                        View Failed Logins Only
                    </a>
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .security-alerts .alert {
            margin-bottom: 20px;
        }
        .security-alerts .button {
            display: inline-block;
            margin-right: 10px;
        }
        .security-alerts .button:hover {
            opacity: 0.8;
        }
    </style>
{% endblock %}
