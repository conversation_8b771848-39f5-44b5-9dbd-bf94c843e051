{% comment %}
Navigation Buttons Component
Reusable navigation buttons and dropdown menu for both hero and standard layouts
{% endcomment %}

<div class="nav-buttons ms-auto d-flex align-items-center">
    <!-- Shopping Cart Button (Customer Only) -->
    {% if user.is_authenticated and user.is_customer %}
        <a href="{% url 'booking_cart_app:cart_view' %}"
           class="btn btn-light me-3 position-relative"
           title="View Cart">
            <i class="fas fa-shopping-cart"></i>
            {% if cart_count > 0 %}
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    {{ cart_count }}
                    <span class="visually-hidden">items in cart</span>
                </span>
            {% endif %}
        </a>
    {% endif %}

    <!-- Notifications -->
    {% if user.is_authenticated %}
        {% include 'notifications_app/notification_dropdown.html' %}
    {% endif %}

    <!-- Provider Dashboard or Business Link -->
    {% if user.is_service_provider %}
        <a href="{% url 'dashboard_app:provider_dashboard' %}"
           class="btn btn-light me-3 {% if request.resolver_match.url_name == 'provider_dashboard' %}active{% endif %}"
           title="Provider Dashboard">
            Provider Dashboard
        </a>
    {% elif not user.is_authenticated or not user.is_customer %}
        <a href="{% url 'accounts_app:for_business' %}"
           class="btn btn-light me-3 for-business-btn {% if request.resolver_match.url_name == 'for_business' %}active{% endif %}"
           title="Business Registration">
            For Business
        </a>
    {% endif %}

    <!-- Main Menu Dropdown -->
    <div class="dropdown">
        <button class="btn btn-light menu-btn dropdown-toggle" 
                type="button" 
                data-bs-toggle="dropdown" 
                aria-expanded="false"
                title="Main Menu">
            Menu
        </button>
        
        <ul class="dropdown-menu dropdown-menu-end">
            {% if user.is_authenticated %}
                <!-- User Profile Section -->
                {% if user.is_service_provider %}
                    <li>
                        <a class="dropdown-item d-flex align-items-center" 
                           href="{% url 'accounts_app:service_provider_profile' %}">
                            <i class="fas fa-user me-3"></i>
                            My Profile
                        </a>
                    </li>
                {% else %}
                    <li>
                        <a class="dropdown-item d-flex align-items-center" 
                           href="{% url 'accounts_app:customer_profile' %}">
                            <i class="fas fa-user me-3"></i>
                            My Profile
                        </a>
                    </li>
                {% endif %}

                <!-- Customer-Specific Menu Items -->
                {% if user.is_customer %}
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'dashboard_app:customer_dashboard' %}">
                            <i class="fas fa-tachometer-alt me-3"></i>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'dashboard_app:customer_booking_status' %}">
                            <i class="fas fa-calendar-check me-3"></i>
                            My Bookings
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'booking_cart_app:cart_view' %}">
                            <i class="fas fa-shopping-cart me-3"></i>
                            My Cart
                            {% if cart_count > 0 %}
                                <span class="badge bg-danger ms-auto">{{ cart_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'notifications_app:notification_list' %}">
                            <i class="fas fa-bell me-3"></i>
                            Notifications
                            {% if unread_notifications_count > 0 %}
                                <span class="badge bg-danger ms-auto">{{ unread_notifications_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'discount_app:featured_discounts' %}">
                            <i class="fas fa-tags me-3"></i>
                            Featured Discounts
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'discount_app:search_discounts' %}">
                            <i class="fas fa-search me-3"></i>
                            Search Discounts
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'payments_app:payment_history' %}">
                            <i class="fas fa-credit-card me-3"></i>
                            Payment History
                        </a>
                    </li>
                {% endif %}

                <!-- Service Provider-Specific Menu Items -->
                {% if user.is_service_provider %}
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'booking_cart_app:provider_booking_list' %}">
                            <i class="fas fa-calendar-check me-3"></i>
                            Bookings
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'notifications_app:notification_list' %}">
                            <i class="fas fa-bell me-3"></i>
                            Notifications
                            {% if unread_notifications_count > 0 %}
                                <span class="badge bg-danger ms-auto">{{ unread_notifications_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'payments_app:provider_payment_history' %}">
                            <i class="fas fa-credit-card me-3"></i>
                            Payment History
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center"
                           href="{% url 'accounts_app:service_provider_profile' %}">
                            <i class="fas fa-users me-3"></i>
                            My Staff
                        </a>
                    </li>
                {% endif %}

                <!-- Admin-Specific Menu Items -->
                {% if user.is_staff %}
                    <li>
                        <a class="dropdown-item d-flex align-items-center" 
                           href="{% url 'booking_cart_app:admin_booking_list' %}">
                            <i class="fas fa-calendar-check me-3"></i>
                            Bookings Management
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center" 
                           href="{% url 'dashboard_app:admin_booking_analytics' %}">
                            <i class="fas fa-chart-bar me-3"></i>
                            Booking Analytics
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center" 
                           href="{% url 'admin_app:pending_providers' %}">
                            <i class="fas fa-check-circle me-3"></i>
                            Provider Approvals
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center" 
                           href="{% url 'admin_app:user_list' %}">
                            <i class="fas fa-users-cog me-3"></i>
                            User Management
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center" 
                           href="{% url 'admin:index' %}">
                            <i class="fas fa-cog me-3"></i>
                            Django Admin
                        </a>
                    </li>
                {% endif %}

                <li><hr class="dropdown-divider"></li>

                <!-- Logout -->
                <li>
                    <a class="dropdown-item d-flex align-items-center" 
                       href="{% url 'accounts_app:logout' %}">
                        <i class="fas fa-sign-out-alt me-3"></i>
                        Log out
                    </a>
                </li>
            {% else %}
                <!-- Guest User Menu Items -->
                <li>
                    <a class="dropdown-item d-flex align-items-center" 
                       href="{% url 'accounts_app:customer_login' %}">
                        <i class="fas fa-sign-in-alt me-3"></i>
                        Log in
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item" 
                       href="{% url 'accounts_app:customer_signup' %}">
                        Sign Up
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
            {% endif %}

            <!-- Business Link for Non-Customers -->
            {% if not user.is_customer %}
                <li>
                    <a class="dropdown-item" 
                       href="{% url 'accounts_app:for_business' %}">
                        For businesses
                    </a>
                </li>
            {% endif %}
        </ul>
    </div>
</div>
