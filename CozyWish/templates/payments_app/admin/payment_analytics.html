{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Payment Analytics" %} - CozyWish Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Payment Analytics Dashboard" %}</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card" style="background-color: black; color: white;">
                                <div class="card-body">
                                    <h5 style="color: white;">{% trans "Total Payments" %}</h5>
                                    <h3 style="color: white;">{{ total_payments|default:0 }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card" style="background-color: black; color: white;">
                                <div class="card-body">
                                    <h5 style="color: white;">{% trans "Successful Payments" %}</h5>
                                    <h3 style="color: white;">{{ successful_payments|default:0 }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card" style="background-color: black; color: white;">
                                <div class="card-body">
                                    <h5 style="color: white;">{% trans "Failed Payments" %}</h5>
                                    <h3 style="color: white;">{{ failed_payments|default:0 }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card" style="background-color: white; color: black; border: 2px solid black;">
                                <div class="card-body">
                                    <h5 style="color: black;">{% trans "Total Revenue" %}</h5>
                                    <h3 style="color: black;">${{ total_revenue|default:0 }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5>{% trans "Refund Rate" %}</h5>
                                    <h3>{{ refund_stats.refund_rate|floatformat:1|default:0 }}%</h3>
                                    <p style="color: black; opacity: 0.7;">{% trans "Percentage of payments that resulted in refunds" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5>{% trans "Total Refunds" %}</h5>
                                    <h3>{{ refund_stats.total_refunds|default:0 }}</h3>
                                    <p style="color: black; opacity: 0.7;">{% trans "Total number of refund requests" %}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h4>{% trans "Payment Analytics" %}</h4>
                            <p>{% trans "Detailed payment analytics and reporting will be displayed here." %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
