{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Refund Management" %} - CozyWish Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Refund Request Management" %}</h3>
                </div>
                <div class="card-body">
                    {% if refund_requests %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Request ID" %}</th>
                                    <th>{% trans "Customer" %}</th>
                                    <th>{% trans "Payment ID" %}</th>
                                    <th>{% trans "Amount Requested" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for refund in refund_requests %}
                                <tr>
                                    <td>{{ refund.refund_request_id|truncatechars:8 }}...</td>
                                    <td>{{ refund.payment.customer.get_full_name|default:refund.payment.customer.email }}</td>
                                    <td>{{ refund.payment.payment_id|truncatechars:8 }}...</td>
                                    <td>${{ refund.requested_amount }}</td>
                                    <td>
                                        <span class="badge badge-{% if refund.request_status == 'approved' %}success{% elif refund.request_status == 'declined' %}danger{% else %}warning{% endif %}">
                                            {{ refund.get_request_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ refund.created_at|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <a href="{% url 'payments_app:admin_refund_detail' refund_request_id=refund.refund_request_id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> {% trans "View" %}
                                        </a>
                                        {% if refund.request_status == 'pending' %}
                                        <a href="{% url 'payments_app:admin_refund_approve' refund_id=refund.refund_request_id %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-check"></i> {% trans "Approve" %}
                                        </a>
                                        <a href="{% url 'payments_app:admin_refund_decline' refund_id=refund.refund_request_id %}" class="btn btn-sm btn-danger">
                                            <i class="fas fa-times"></i> {% trans "Decline" %}
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    {% if is_paginated %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">&laquo; {% trans "First" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %} &raquo;</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <p>{% trans "No refund requests found." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
