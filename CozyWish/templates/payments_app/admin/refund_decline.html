{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Decline Refund Request" %} - CozyWish Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Decline Refund Request" %}</h3>
                    <div class="card-tools">
                        <a href="{% url 'payments_app:admin_refund_detail' refund_request_id=refund_request.refund_request_id %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Detail" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if refund_request %}
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{% trans "Refund Request Information" %}</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>{% trans "Request ID" %}</th>
                                    <td>{{ refund_request.refund_request_id }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Customer" %}</th>
                                    <td>{{ refund_request.payment.customer.get_full_name|default:refund_request.payment.customer.email }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Payment ID" %}</th>
                                    <td>{{ refund_request.payment.payment_id }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Amount Requested" %}</th>
                                    <td>${{ refund_request.requested_amount }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Reason" %}</th>
                                    <td>{{ refund_request.reason_description }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "Request Date" %}</th>
                                    <td>{{ refund_request.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>{% trans "Decline Form" %}</h5>
                            <form method="post">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="admin_notes" class="form-label">{% trans "Reason for Decline" %}</label>
                                    <textarea class="form-control" id="admin_notes" name="admin_notes" rows="4" 
                                              placeholder="{% trans 'Please provide a reason for declining this refund request...' %}" required></textarea>
                                </div>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-times"></i> {% trans "Decline Refund" %}
                                    </button>
                                    <a href="{% url 'payments_app:admin_refund_approve' refund_id=refund_request.refund_request_id %}"
                                       class="btn btn-success">
                                        <i class="fas fa-check"></i> {% trans "Approve Instead" %}
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                    {% else %}
                    <p>{% trans "Refund request not found." %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
