{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Payments App - Black & White Design */
        /* Matching homepage and accounts_app design with clean typography and spacing */

        /* CSS Variables for fonts */
        :root {
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Payments wrapper - clean white background */
        .payments-wrapper {
            background-color: white;
            min-height: 100vh;
            padding: 2rem 0;
            font-family: var(--font-primary);
        }

        /* Typography */
        .payments-wrapper h1, .payments-wrapper h2, .payments-wrapper h3,
        .payments-wrapper h4, .payments-wrapper h5, .payments-wrapper h6 {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            margin-bottom: 1rem;
        }

        .payments-wrapper p, .payments-wrapper span, .payments-wrapper div {
            color: black;
        }

        /* Cards - clean white with black border */
        .payments-wrapper .card {
            background: white;
            border: 2px solid black;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .payments-wrapper .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        /* Buttons - black and white theme */
        .payments-wrapper .btn {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid black;
            transition: all 0.3s ease;
        }

        .payments-wrapper .btn-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .payments-wrapper .btn-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .payments-wrapper .btn-outline-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .payments-wrapper .btn-outline-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .payments-wrapper .btn-success {
            background-color: white;
            color: black;
            border-color: black;
        }

        .payments-wrapper .btn-success:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .payments-wrapper .btn-danger {
            background-color: white;
            color: black;
            border-color: black;
        }

        .payments-wrapper .btn-danger:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .payments-wrapper .btn-warning {
            background-color: white;
            color: black;
            border-color: black;
        }

        .payments-wrapper .btn-warning:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Form elements */
        .payments-wrapper .form-control, .payments-wrapper .form-select {
            font-family: var(--font-primary);
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            background-color: white;
            color: black;
        }

        .payments-wrapper .form-control:focus, .payments-wrapper .form-select:focus {
            border-color: black;
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
            background-color: white;
            color: black;
        }

        /* Labels */
        .payments-wrapper .form-label {
            font-family: var(--font-primary);
            font-weight: 500;
            color: black;
            margin-bottom: 0.5rem;
        }

        /* Tables */
        .payments-wrapper .table {
            color: black;
            font-family: var(--font-primary);
        }

        .payments-wrapper .table th {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            border-bottom: 2px solid black;
            background-color: white;
        }

        .payments-wrapper .table td {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* Badges */
        .payments-wrapper .badge {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }

        .payments-wrapper .badge.bg-success {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .payments-wrapper .badge.bg-primary {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .payments-wrapper .badge.bg-warning {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .payments-wrapper .badge.bg-danger {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .payments-wrapper .badge.bg-info {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        /* Pagination */
        .payments-wrapper .pagination .page-link {
            color: black;
            border: 2px solid black;
            background-color: white;
            font-family: var(--font-primary);
            font-weight: 500;
        }

        .payments-wrapper .pagination .page-link:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .payments-wrapper .pagination .page-item.active .page-link {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Text colors */
        .payments-wrapper .text-muted {
            color: rgba(0, 0, 0, 0.6) !important;
        }

        .payments-wrapper .text-primary {
            color: black !important;
        }

        .payments-wrapper .text-success {
            color: black !important;
        }

        .payments-wrapper .text-danger {
            color: black !important;
        }

        .payments-wrapper .text-warning {
            color: black !important;
        }

        .payments-wrapper .text-info {
            color: black !important;
        }

        /* Payment specific styling */
        .payment-amount {
            font-family: var(--font-heading);
            font-weight: 700;
            font-size: 1.5rem;
            color: black;
        }

        .payment-status {
            font-family: var(--font-primary);
            font-weight: 600;
        }

        /* Breadcrumb styling */
        .payments-wrapper .breadcrumb {
            background-color: white;
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
        }

        .payments-wrapper .breadcrumb-item a {
            color: black;
            text-decoration: none;
            font-weight: 500;
        }

        .payments-wrapper .breadcrumb-item a:hover {
            color: rgba(0, 0, 0, 0.7);
        }

        .payments-wrapper .breadcrumb-item.active {
            color: rgba(0, 0, 0, 0.6);
        }

        /* Card header styling */
        .payments-wrapper .card-header {
            background-color: white !important;
            color: black;
            border-bottom: 2px solid black;
            font-family: var(--font-heading);
            font-weight: 600;
        }

        .payments-wrapper .card-header.bg-light {
            background-color: white !important;
            color: black;
            border-bottom: 2px solid black;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .payments-wrapper {
                padding: 1rem 0;
            }

            .payments-wrapper .card {
                margin-bottom: 1.5rem;
            }
        }
    </style>
    {% block payments_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="payments-wrapper">
    <div class="container py-4">
        {% block payments_content %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% block payments_extra_js %}{% endblock %}
{% endblock %}

