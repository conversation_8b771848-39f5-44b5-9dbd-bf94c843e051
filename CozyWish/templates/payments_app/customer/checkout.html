{% extends 'payments_app/base_payments.html' %}
{% load crispy_forms_tags %}

{% block title %}Checkout - CozyWish{% endblock %}

{% block payments_extra_css %}
<style>
    /* Checkout specific styles - black & white theme */
    .section-title {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .section-subtitle {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.7);
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Price breakdown styling */
    .price-breakdown {
        font-family: var(--font-primary);
    }

    .price-breakdown .total {
        font-family: var(--font-heading);
        font-weight: 700;
        font-size: 1.25rem;
        color: black;
    }

    /* Security notice styling */
    .security-notice {
        text-align: center;
        padding: 2rem;
    }

    .security-notice i {
        color: black;
        margin-bottom: 1rem;
    }

    .security-notice h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    /* Alert styling */
    .payments-wrapper .alert {
        border: 2px solid black;
        border-radius: 0.75rem;
        background-color: white;
        color: black;
    }

    .payments-wrapper .alert-info {
        border-color: black;
    }

    .payments-wrapper .alert-heading {
        color: black;
        font-family: var(--font-heading);
        font-weight: 600;
    }

    /* Form check styling */
    .payments-wrapper .form-check-input {
        border: 2px solid black;
        background-color: white;
    }

    .payments-wrapper .form-check-input:checked {
        background-color: black;
        border-color: black;
    }

    .payments-wrapper .form-check-label {
        color: black;
        font-family: var(--font-primary);
        font-weight: 500;
    }
</style>
{% endblock %}

{% block payments_content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'venues_app:home' %}">Home</a></li>
                <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:booking_list' %}">Bookings</a></li>
                <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:booking_detail' booking_slug=booking.slug %}">Booking Details</a></li>
                <li class="breadcrumb-item active" aria-current="page">Checkout</li>
            </ol>
        </nav>

        <div class="page-header mb-4">
            <h1 class="section-title">Secure Checkout</h1>
            <p class="section-subtitle">Complete your payment to confirm your booking</p>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Payment Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-credit-card me-2"></i>Payment Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Stripe Placeholder Notice -->
                        <div class="alert alert-info mb-4">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-info-circle fa-2x"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading">Stripe Checkout Integration Placeholder</h6>
                                    <p class="mb-0">This is a placeholder for Stripe Checkout integration. In production, this would redirect to Stripe's secure checkout process.</p>
                                </div>
                            </div>
                        </div>
                            
                            <form method="post">
                                {% csrf_token %}
                                
                                <!-- Payment Method -->
                                <div class="mb-4">
                                    <h6 class="mb-3">Payment Method</h6>
                                    {{ form.payment_method|as_crispy_field }}
                                </div>
                                
                                <!-- Billing Information -->
                                <div class="mb-4">
                                    <h6 class="mb-3">Billing Information</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            {{ form.billing_name|as_crispy_field }}
                                        </div>
                                        <div class="col-md-6">
                                            {{ form.billing_email|as_crispy_field }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            {{ form.billing_phone|as_crispy_field }}
                                        </div>
                                        <div class="col-md-6">
                                            {{ form.billing_address|as_crispy_field }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            {{ form.billing_city|as_crispy_field }}
                                        </div>
                                        <div class="col-md-4">
                                            {{ form.billing_state|as_crispy_field }}
                                        </div>
                                        <div class="col-md-4">
                                            {{ form.billing_zip_code|as_crispy_field }}
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Payment Options -->
                                <div class="mb-4">
                                    <div class="form-check">
                                        {{ form.save_payment_method }}
                                        <label class="form-check-label" for="{{ form.save_payment_method.id_for_label }}">
                                            {{ form.save_payment_method.label }}
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- Terms and Conditions -->
                                <div class="mb-4">
                                    <div class="form-check">
                                        {{ form.accept_terms }}
                                        <label class="form-check-label" for="{{ form.accept_terms.id_for_label }}">
                                            {{ form.accept_terms.label }}
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- Submit Button -->
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'booking_cart_app:booking_detail' booking_slug=booking.slug %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Booking
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-lock me-2"></i>Complete Payment - ${{ total_amount }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
            <div class="col-lg-4 order-lg-2">
                <div class="sticky-top" style="top:80px;">
                <!-- Order Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-receipt me-2"></i>Order Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6>Booking Details</h6>
                            <p class="mb-1"><strong>Booking ID:</strong> {{ booking.booking_id|truncatechars:12 }}</p>
                            <p class="mb-1"><strong>Venue:</strong> {{ booking.venue.venue_name }}</p>
                            <p class="mb-1"><strong>Date:</strong> {{ booking.booking_date|date:"M d, Y" }}</p>
                            <p class="mb-0"><strong>Status:</strong>
                                <span class="badge bg-warning">{{ booking.get_status_display }}</span>
                            </p>
                        </div>

                        <hr>

                        <!-- Price Breakdown -->
                        <div class="mb-3 price-breakdown">
                            <h6>Price Breakdown</h6>
                            <div class="d-flex justify-content-between mb-1">
                                <span>Subtotal:</span>
                                <span>${{ price_breakdown.subtotal }}</span>
                            </div>
                            {% if price_breakdown.discount > 0 %}
                            <div class="d-flex justify-content-between mb-1">
                                <span>Discount:</span>
                                <span>-${{ price_breakdown.discount }}</span>
                            </div>
                            {% endif %}
                            {% if price_breakdown.tax > 0 %}
                            <div class="d-flex justify-content-between mb-1">
                                <span>Tax:</span>
                                <span>${{ price_breakdown.tax }}</span>
                            </div>
                            {% endif %}
                            {% if price_breakdown.service_fee > 0 %}
                            <div class="d-flex justify-content-between mb-1">
                                <span>Service Fee:</span>
                                <span>${{ price_breakdown.service_fee }}</span>
                            </div>
                            {% endif %}
                            <hr>
                            <div class="d-flex justify-content-between total">
                                <span>Total:</span>
                                <span>${{ price_breakdown.total }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="card">
                    <div class="card-body security-notice">
                        <i class="fas fa-shield-alt fa-3x"></i>
                        <h6>Secure Payment</h6>
                        <p class="text-muted mb-0">Your payment information is encrypted and secure. We use industry-standard SSL encryption to protect your data.</p>
                    </div>
                </div>
                </div><!-- /sticky-top -->
            </div>
        </div>
    </div>
</div>
{% endblock %}
