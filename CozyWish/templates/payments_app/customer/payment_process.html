{% extends 'base.html' %}

{% block title %}Processing Payment - CozyWish{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            {% if payment.payment_status == 'succeeded' %}
            <!-- Success State -->
            <div class="text-center mb-5">
                <div class="mb-4">
                    <i class="fas fa-check-circle fa-5x text-success"></i>
                </div>
                <h1 class="h2 text-success">Payment Successful!</h1>
                <p class="lead text-muted">Your payment has been processed successfully.</p>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>Payment Confirmation
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h6>Payment ID</h6>
                            <p class="mb-3"><code>{{ payment.payment_id|truncatechars:20 }}</code></p>
                            
                            <h6>Amount Paid</h6>
                            <p class="mb-3 fw-bold fs-5">${{ payment.amount_paid }}</p>
                        </div>
                        <div class="col-6">
                            <h6>Payment Date</h6>
                            <p class="mb-3">{{ payment.completed_date|date:"M d, Y H:i" }}</p>
                            
                            <h6>Status</h6>
                            <p class="mb-3">
                                <span class="badge bg-success fs-6">Succeeded</span>
                            </p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h6>Booking Information</h6>
                            <p class="mb-1"><strong>Booking ID:</strong> {{ payment.booking.booking_id|truncatechars:20 }}</p>
                            <p class="mb-1"><strong>Venue:</strong> {{ payment.booking.venue.name }}</p>
                            <p class="mb-0"><strong>Status:</strong> 
                                <span class="badge bg-success">{{ payment.booking.get_status_display }}</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- What's Next -->
            <div class="alert alert-success mb-4">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="fas fa-info-circle fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="alert-heading">What's Next?</h6>
                        <ul class="mb-0">
                            <li>Your booking has been confirmed</li>
                            <li>You will receive a confirmation email shortly</li>
                            <li>The service provider will contact you if needed</li>
                            <li>You can view your booking details anytime</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            {% elif payment.payment_status == 'pending' %}
            <!-- Processing State -->
            <div class="text-center mb-5">
                <div class="mb-4">
                    <div class="spinner-border text-primary" style="width: 5rem; height: 5rem;" role="status">
                        <span class="visually-hidden">Processing...</span>
                    </div>
                </div>
                <h1 class="h2 text-primary">Processing Payment...</h1>
                <p class="lead text-muted">Please wait while we process your payment.</p>
            </div>
            
            <div class="alert alert-info mb-4">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="alert-heading">Payment in Progress</h6>
                        <p class="mb-0">Your payment is being processed. This page will automatically refresh when complete. Please do not close this window.</p>
                    </div>
                </div>
            </div>
            
            <!-- Auto-refresh for pending payments -->
            <script>
                setTimeout(function() {
                    location.reload();
                }, 5000); // Refresh every 5 seconds
            </script>
            
            {% elif payment.payment_status == 'failed' %}
            <!-- Failed State -->
            <div class="text-center mb-5">
                <div class="mb-4">
                    <i class="fas fa-times-circle fa-5x text-danger"></i>
                </div>
                <h1 class="h2 text-danger">Payment Failed</h1>
                <p class="lead text-muted">Unfortunately, your payment could not be processed.</p>
            </div>
            
            <div class="alert alert-danger mb-4">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="alert-heading">Payment Error</h6>
                        <p class="mb-0">
                            {% if payment.failure_reason %}
                                {{ payment.failure_reason }}
                            {% else %}
                                Your payment could not be processed. Please try again or contact support.
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            
            {% endif %}
            
            <!-- Action Buttons -->
            <div class="row">
                {% if payment.payment_status == 'succeeded' %}
                <div class="col-md-6 mb-3">
                    <div class="d-grid">
                        <a href="{% url 'booking_cart_app:booking_detail' booking_slug=payment.booking.slug %}" class="btn btn-primary">
                            <i class="fas fa-calendar-check me-2"></i>View Booking
                        </a>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="d-grid">
                        <a href="{% url 'payments_app:payment_receipt' payment_id=payment.payment_id %}" class="btn btn-outline-primary">
                            <i class="fas fa-download me-2"></i>Download Receipt
                        </a>
                    </div>
                </div>
                {% elif payment.payment_status == 'failed' %}
                <div class="col-md-6 mb-3">
                    <div class="d-grid">
                        <a href="{% url 'payments_app:checkout' booking_id=payment.booking.booking_id %}" class="btn btn-primary">
                            <i class="fas fa-redo me-2"></i>Try Again
                        </a>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="d-grid">
                        <a href="#" class="btn btn-outline-secondary">
                            <i class="fas fa-headset me-2"></i>Contact Support
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="d-grid">
                        <a href="{% url 'payments_app:payment_history' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-history me-2"></i>Payment History
                        </a>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="d-grid">
                        <a href="{% url 'venues_app:home' %}" class="btn btn-outline-primary">
                            <i class="fas fa-home me-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
