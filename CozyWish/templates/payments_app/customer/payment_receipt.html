{% extends 'base.html' %}

{% block title %}Payment Receipt - CozyWish{% endblock %}
{% block extra_head %}
<meta property="og:title" content="CozyWish Payment Receipt" />
<meta property="og:description" content="Receipt for booking {{ booking.booking_id }} totaling ${{ payment.amount_paid }}" />
<meta property="og:type" content="article" />
<meta property="og:url" content="{{ request.build_absolute_uri }}" />
{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .no-print { display: none !important; }
        .container { max-width: none !important; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Print Button -->
            <div class="text-end mb-3 no-print">
                <a href="{% url 'payments_app:payment_receipt' payment_id=payment.payment_id %}?format=pdf" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-file-pdf me-2"></i>Download PDF
                </a>
                <button onclick="window.print()" class="btn btn-outline-primary">
                    <i class="fas fa-print me-2"></i>Print Receipt
                </button>
            </div>
            
            <!-- Receipt -->
            <div class="card">
                <div class="card-body">
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <h2 class="mb-1">CozyWish</h2>
                        <p class="text-muted mb-0">Payment Receipt</p>
                    </div>
                    
                    <hr>
                    
                    <!-- Receipt Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Receipt Details</h6>
                            <p class="mb-1"><strong>Receipt Date:</strong> {{ payment.completed_date|date:"M d, Y H:i" }}</p>
                            <p class="mb-1"><strong>Payment ID:</strong> <code>{{ payment.payment_id }}</code></p>
                            <p class="mb-0"><strong>Status:</strong> 
                                <span class="badge bg-success">{{ payment.get_payment_status_display }}</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>Customer Information</h6>
                            <p class="mb-1"><strong>Name:</strong> 
                                {% if customer.customerprofile.first_name %}
                                    {{ customer.customerprofile.first_name }} {{ customer.customerprofile.last_name }}
                                {% else %}
                                    {{ customer.email }}
                                {% endif %}
                            </p>
                            <p class="mb-0"><strong>Email:</strong> {{ customer.email }}</p>
                        </div>
                    </div>
                    
                    <!-- Booking Information -->
                    <div class="mb-4">
                        <h6>Booking Information</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    <tr>
                                        <td><strong>Booking ID:</strong></td>
                                        <td><code>{{ booking.booking_id }}</code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Venue:</strong></td>
                                        <td>{{ booking.venue.name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Booking Date:</strong></td>
                                        <td>{{ booking.booking_date|date:"M d, Y H:i" }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td><span class="badge bg-success">{{ booking.get_status_display }}</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Services -->
                    {% if booking.items.all %}
                    <div class="mb-4">
                        <h6>Services</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>Service</th>
                                        <th>Date</th>
                                        <th>Time</th>
                                        <th>Quantity</th>
                                        <th class="text-end">Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in booking.items.all %}
                                    <tr>
                                        <td>{{ item.service_title }}</td>
                                        <td>{{ item.date|date:"M d, Y" }}</td>
                                        <td>{{ item.time_slot|time:"H:i" }}</td>
                                        <td>{{ item.quantity }}</td>
                                        <td class="text-end">${{ item.service_price }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Payment Summary -->
                    <div class="mb-4">
                        <h6>Payment Summary</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    <tr>
                                        <td><strong>Subtotal:</strong></td>
                                        <td class="text-end">${{ payment.amount_paid }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Discount:</strong></td>
                                        <td class="text-end">$0.00</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tax:</strong></td>
                                        <td class="text-end">$0.00</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Service Fee:</strong></td>
                                        <td class="text-end">$0.00</td>
                                    </tr>
                                    <tr class="table-active">
                                        <td><strong>Total Paid:</strong></td>
                                        <td class="text-end"><strong>${{ payment.amount_paid }}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Payment Method -->
                    <div class="mb-4">
                        <h6>Payment Method</h6>
                        <p class="mb-1"><strong>Method:</strong> {{ payment.get_payment_method_display }}</p>
                        <p class="mb-1"><strong>Transaction Date:</strong> {{ payment.payment_date|date:"M d, Y H:i" }}</p>
                        {% if payment.stripe_payment_intent_id %}
                        <p class="mb-0"><strong>Stripe Payment Intent:</strong> <code>{{ payment.stripe_payment_intent_id }}</code></p>
                        {% endif %}
                    </div>
                    
                    <!-- Refund Information -->
                    {% if payment.refunded_amount > 0 %}
                    <div class="mb-4">
                        <h6>Refund Information</h6>
                        <div class="alert alert-info">
                            <p class="mb-1"><strong>Refunded Amount:</strong> ${{ payment.refunded_amount }}</p>
                            <p class="mb-0"><strong>Net Amount:</strong> ${{ payment.remaining_refundable_amount }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    <hr>
                    
                    <!-- Footer -->
                    <div class="text-center">
                        <p class="text-muted mb-1">Thank you for choosing CozyWish!</p>
                        <p class="text-muted mb-0">For support, please contact <NAME_EMAIL></p>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="text-center mt-4 no-print">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <div class="d-grid">
                            <a href="{% url 'payments_app:payment_detail' payment_id=payment.payment_id %}" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Payment
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 mb-2">
                        <div class="d-grid">
                            <a href="{% url 'booking_cart_app:booking_detail' booking_slug=booking.slug %}" class="btn btn-outline-secondary">
                                <i class="fas fa-calendar-check me-2"></i>View Booking
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 mb-2">
                        <div class="d-grid">
                            <a href="{% url 'payments_app:payment_history' %}" class="btn btn-primary">
                                <i class="fas fa-history me-2"></i>Payment History
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
