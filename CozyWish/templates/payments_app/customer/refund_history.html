{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block title %}{% trans "Refund History" %} - CozyWish{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2">{% trans "Refund History" %}</h1>
                    <p class="text-muted">{% trans "View your refund request history" %}</p>
                </div>
                <div>
                    <a href="{% url 'payments_app:payment_history' %}" class="btn btn-outline-primary">
                        <i class="fas fa-credit-card me-2"></i>{% trans "Payment History" %}
                    </a>
                </div>
            </div>
            
            <!-- Refund Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">{{ refund_stats.total_requests }}</h5>
                            <p class="card-text text-muted">Total Requests</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">{{ refund_stats.pending_requests }}</h5>
                            <p class="card-text text-muted">Pending</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">{{ refund_stats.processed_requests }}</h5>
                            <p class="card-text text-muted">Processed</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">${{ refund_stats.total_refunded|floatformat:2 }}</h5>
                            <p class="card-text text-muted">Total Refunded</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>Search & Filter
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            {{ search_form.search_query|as_crispy_field }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.status|as_crispy_field }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.reason_category|as_crispy_field }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.date_from|as_crispy_field }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.date_to|as_crispy_field }}
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                            <a href="{% url 'payments_app:refund_history' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            {% if active_filters %}
            <div class="mb-3">
                {% for chip in active_filters %}
                <span class="badge bg-secondary me-2">{{ chip }}</span>
                {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3 text-end">
                <a href="?{{ request.GET.urlencode }}&format=csv" class="btn btn-sm btn-outline-primary me-2">
                    <i class="fas fa-file-csv me-1"></i>CSV
                </a>
                <a href="?{{ request.GET.urlencode }}&format=pdf" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-file-pdf me-1"></i>PDF
                </a>
            </div>

            {% include 'payments_app/includes/refund_skeleton.html' %}
            <div id="refund-results" style="display:none;">
            <!-- Refund Request List -->
            {% if refund_requests %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-undo me-2"></i>{% trans "Refund Requests" %}
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead style="background-color: black; color: white;">
                                <tr>
                                    <th style="color: white;">{% trans "Request ID" %}</th>
                                    <th style="color: white;">{% trans "Date" %}</th>
                                    <th style="color: white;">{% trans "Payment ID" %}</th>
                                    <th style="color: white;">{% trans "Reason" %}</th>
                                    <th style="color: white;">{% trans "Requested" %}</th>
                                    <th style="color: white;">{% trans "Processed" %}</th>
                                    <th style="color: white;">{% trans "Status" %}</th>
                                    <th style="color: white;">{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for refund_request in refund_requests %}
                                <tr>
                                    <td>
                                        <code>{{ refund_request.refund_request_id|truncatechars:12 }}</code>
                                    </td>
                                    <td>{{ refund_request.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <a href="{% url 'payments_app:payment_detail' payment_id=refund_request.payment.payment_id %}" class="text-decoration-none">
                                            <code>{{ refund_request.payment.payment_id|truncatechars:12 }}</code>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ refund_request.get_reason_category_display }}</span>
                                    </td>
                                    <td class="fw-bold">${{ refund_request.requested_amount }}</td>
                                    <td class="fw-bold">
                                        {% if refund_request.processed_amount > 0 %}
                                            ${{ refund_request.processed_amount }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if refund_request.request_status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                        {% elif refund_request.request_status == 'approved' %}
                                        <span class="badge bg-success">Approved</span>
                                        {% elif refund_request.request_status == 'declined' %}
                                        <span class="badge bg-danger">Declined</span>
                                        {% elif refund_request.request_status == 'processed' %}
                                        <span class="badge bg-info">Processed</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ refund_request.get_request_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#refundModal{{ refund_request.id }}"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="{% url 'payments_app:payment_detail' payment_id=refund_request.payment.payment_id %}" 
                                               class="btn btn-outline-secondary" title="View Payment">
                                                <i class="fas fa-credit-card"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Refund history pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}">Previous</a>
                    </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}">Last</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <!-- Empty State -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-undo fa-4x text-muted"></i>
                    </div>
                    <h4 class="mb-3">{% trans "No Refund Requests Yet" %}</h4>
                    <p class="text-muted mb-4">{% trans "You haven't submitted any refund requests yet." %}</p>
                    <a href="{% url 'payments_app:payment_history' %}" class="btn btn-primary">
                        <i class="fas fa-credit-card me-2"></i>{% trans "View Payment History" %}
                    </a>
                </div>
            </div>
            {% endif %}

            </div>
            <!-- Navigation -->
            <div class="d-flex justify-content-between mt-4">
                <a href="{% url 'venues_app:home' %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Home" %}
                </a>
                <a href="{% url 'payments_app:payment_history' %}" class="btn btn-primary">
                    <i class="fas fa-credit-card me-2"></i>{% trans "Payment History" %}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Refund Detail Modals -->
{% for refund_request in refund_requests %}
<div class="modal fade" id="refundModal{{ refund_request.id }}" tabindex="-1" aria-labelledby="refundModalLabel{{ refund_request.id }}" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="refundModalLabel{{ refund_request.id }}">
                    <i class="fas fa-undo me-2"></i>Refund Request Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Request Information</h6>
                        <p class="mb-1"><strong>Request ID:</strong> <code>{{ refund_request.refund_request_id|truncatechars:20 }}</code></p>
                        <p class="mb-1"><strong>Date:</strong> {{ refund_request.created_at|date:"M d, Y H:i" }}</p>
                        <p class="mb-1"><strong>Status:</strong> 
                            {% if refund_request.request_status == 'pending' %}
                            <span class="badge bg-warning">Pending</span>
                            {% elif refund_request.request_status == 'approved' %}
                            <span class="badge bg-success">Approved</span>
                            {% elif refund_request.request_status == 'declined' %}
                            <span class="badge bg-danger">Declined</span>
                            {% elif refund_request.request_status == 'processed' %}
                            <span class="badge bg-info">Processed</span>
                            {% endif %}
                        </p>
                        <p class="mb-1"><strong>Reason:</strong> {{ refund_request.get_reason_category_display }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Amount Information</h6>
                        <p class="mb-1"><strong>Requested Amount:</strong> ${{ refund_request.requested_amount }}</p>
                        {% if refund_request.processed_amount > 0 %}
                        <p class="mb-1"><strong>Processed Amount:</strong> ${{ refund_request.processed_amount }}</p>
                        {% endif %}
                        {% if refund_request.reviewed_at %}
                        <p class="mb-1"><strong>Reviewed Date:</strong> {{ refund_request.reviewed_at|date:"M d, Y H:i" }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-12">
                        <h6>Reason Description</h6>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0">{{ refund_request.reason_description }}</p>
                        </div>
                    </div>
                </div>
                
                {% if refund_request.admin_notes %}
                <hr>
                <div class="row">
                    <div class="col-12">
                        <h6>Admin Notes</h6>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0">{{ refund_request.admin_notes }}</p>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="{% url 'payments_app:payment_detail' payment_id=refund_request.payment.payment_id %}" class="btn btn-primary">
                    <i class="fas fa-credit-card me-2"></i>View Payment
                </a>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}
