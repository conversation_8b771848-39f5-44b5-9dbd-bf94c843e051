{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Booking Details - {{ booking.booking_id }}{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Booking Details</h4>
                    <small class="text-muted">Booking ID: {{ booking.booking_id }}</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Venue Information</h6>
                            <p><strong>{{ booking.venue.venue_name }}</strong></p>
                            <p>{{ booking.venue.short_description }}</p>
                            <p>
                                {{ booking.venue.street_number }} {{ booking.venue.street_name }}<br>
                                {{ booking.venue.city }}, {{ booking.venue.state }}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>Booking Status</h6>
                            <span class="badge badge-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                {{ booking.get_status_display }}
                            </span>
                            
                            <h6 class="mt-3">Booking Date</h6>
                            <p>{{ booking.booking_date|date:"F d, Y" }}</p>
                            
                            <h6>Total Price</h6>
                            <p class="h5 text-success">${{ booking.total_price }}</p>
                        </div>
                    </div>
                    
                    {% if booking.notes %}
                    <div class="mt-3">
                        <h6>Special Notes</h6>
                        <p>{{ booking.notes }}</p>
                    </div>
                    {% endif %}
                    
                    <div class="mt-4">
                        <h6>Services Booked</h6>
                        {% for item in booking.items.all %}
                        <div class="border p-3 mb-2">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>{{ item.service_title }}</h6>
                                    <p class="text-muted">{{ item.service.short_description }}</p>
                                    <p><strong>Date:</strong> {{ item.scheduled_date|date:"F d, Y" }}</p>
                                    <p><strong>Time:</strong> {{ item.scheduled_time|time:"g:i A" }}</p>
                                    <p><strong>Duration:</strong> {{ item.duration_minutes }} minutes</p>
                                </div>
                                <div class="col-md-4 text-right">
                                    <p><strong>Quantity:</strong> {{ item.quantity }}</p>
                                    <p><strong>Price:</strong> ${{ item.service_price }} each</p>
                                    <p class="h6"><strong>Total:</strong> ${{ item.service_price|floatformat:2 }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="card-footer">
                    {% if booking.status == 'pending' or booking.status == 'confirmed' %}
                    <a href="{% url 'booking_cart_app:cancel_booking' booking.slug %}" 
                       class="btn btn-danger"
                       onclick="return confirm('Are you sure you want to cancel this booking?')">
                        Cancel Booking
                    </a>
                    {% endif %}
                    <a href="{% url 'booking_cart_app:booking_list' %}" class="btn btn-secondary">
                        Back to My Bookings
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6>Booking Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6>Booking Created</h6>
                                <p class="text-muted">{{ booking.created_at|date:"M d, Y H:i" }}</p>
                            </div>
                        </div>
                        
                        {% if booking.confirmed_at %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6>Booking Confirmed</h6>
                                <p class="text-muted">{{ booking.confirmed_at|date:"M d, Y H:i" }}</p>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if booking.cancelled_at %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6>Booking Cancelled</h6>
                                <p class="text-muted">{{ booking.cancelled_at|date:"M d, Y H:i" }}</p>
                                {% if booking.cancellation_reason %}
                                <p><strong>Reason:</strong> {{ booking.cancellation_reason }}</p>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
</style>
{% endblock %}
