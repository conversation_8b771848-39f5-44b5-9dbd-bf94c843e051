{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Add to Cart - {{ service.service_title }}{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Add to Cart</h4>
                    <p class="mb-0 text-muted">Select your preferred date and time</p>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="form-group">
                            <label for="{{ form.selected_date.id_for_label }}">Select Date</label>
                            {{ form.selected_date }}
                            {% if form.selected_date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.selected_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.selected_time_slot.id_for_label }}">Select Time</label>
                            {{ form.selected_time_slot }}
                            {% if form.selected_time_slot.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.selected_time_slot.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.quantity.id_for_label }}">Quantity</label>
                            {{ form.quantity }}
                            {% if form.quantity.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.quantity.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                Add to Cart
                            </button>
                            <a href="/" class="btn btn-secondary btn-lg ml-2">
                                Back to Service
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Service Details</h5>
                </div>
                <div class="card-body">
                    <h6>{{ service.service_title }}</h6>
                    <p class="text-muted">{{ service.short_description }}</p>
                    
                    <div class="mb-2">
                        <strong>Venue:</strong> {{ service.venue.venue_name }}
                    </div>
                    
                    <div class="mb-2">
                        <strong>Duration:</strong> {{ service.duration_minutes }} minutes
                    </div>
                    
                    <div class="mb-2">
                        <strong>Price:</strong> ${{ service.price_min }}
                        {% if service.price_max and service.price_max != service.price_min %}
                            - ${{ service.price_max }}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block booking_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('id_selected_date');
    const timeSlotSelect = document.getElementById('id_selected_time_slot');
    const serviceId = {{ service.id }};

    console.log('Add to cart JavaScript loaded');
    console.log('Date input:', dateInput);
    console.log('Time slot select:', timeSlotSelect);
    console.log('Service ID:', serviceId);

    // Set minimum date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.min = tomorrow.toISOString().split('T')[0];

    // Set maximum date to 30 days from now
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 30);
    dateInput.max = maxDate.toISOString().split('T')[0];

    // Load time slots when date changes
    dateInput.addEventListener('change', function() {
        const selectedDate = this.value;

        if (!selectedDate) {
            timeSlotSelect.innerHTML = '<option value="">Select a date first</option>';
            return;
        }

        // Show loading state
        timeSlotSelect.innerHTML = '<option value="">Loading available times...</option>';
        timeSlotSelect.disabled = true;

        // Fetch available time slots
        fetch(`/bookings/ajax/slots/${serviceId}/?date=${selectedDate}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                timeSlotSelect.innerHTML = '<option value="">Choose a time slot...</option>';
                timeSlotSelect.disabled = false;

                if (data.slots && data.slots.length > 0) {
                    data.slots.forEach(slot => {
                        const option = document.createElement('option');
                        option.value = slot.time;
                        option.textContent = `${slot.display} (${slot.available_spots} available)`;
                        timeSlotSelect.appendChild(option);
                    });
                } else {
                    timeSlotSelect.innerHTML = '<option value="">No available time slots</option>';
                }
            })
            .catch(error => {
                console.error('Error loading time slots:', error);
                timeSlotSelect.innerHTML = '<option value="">Error loading time slots</option>';
                timeSlotSelect.disabled = false;
            });
    });

    // Trigger change event if date is already selected (for form validation errors)
    if (dateInput.value) {
        dateInput.dispatchEvent(new Event('change'));
    }
});
</script>
{% endblock %}


