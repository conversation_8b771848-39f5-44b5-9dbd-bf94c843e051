{% extends 'booking_cart_app/base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Update Cart Item - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Update Cart Item</h4>
                </div>
                <div class="card-body">
                    <!-- Service Information -->
                    <div class="mb-4 p-3 bg-light rounded">
                        <h6>{{ cart_item.service.service_title }}</h6>
                        <p class="text-muted mb-1">{{ cart_item.service.short_description }}</p>
                        <p class="mb-1"><strong>Venue:</strong> {{ cart_item.service.venue.venue_name }}</p>
                        <p class="mb-1"><strong>Date:</strong> {{ cart_item.selected_date|date:"F d, Y" }}</p>
                        <p class="mb-1"><strong>Time:</strong> {{ cart_item.selected_time_slot }}</p>
                        <p class="mb-0"><strong>Price per item:</strong> ${{ cart_item.price_per_item }}</p>
                    </div>

                    <!-- Update Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.quantity.id_for_label }}" class="form-label">
                                {{ form.quantity.label }}
                            </label>
                            {{ form.quantity|add_class:"form-control" }}
                            {% if form.quantity.help_text %}
                                <small class="form-text text-muted">{{ form.quantity.help_text }}</small>
                            {% endif %}
                            {% if form.quantity.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.quantity.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Current Total -->
                        <div class="mb-3 p-3 bg-light rounded">
                            <div class="d-flex justify-content-between">
                                <span>Current Total:</span>
                                <span class="fw-bold">${{ cart_item.total_price }}</span>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'booking_cart_app:cart_view' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Cart
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Item
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block booking_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const quantityInput = document.getElementById('{{ form.quantity.id_for_label }}');
    const pricePerItem = {{ cart_item.price_per_item }};
    
    // Update total when quantity changes
    quantityInput.addEventListener('input', function() {
        const quantity = parseInt(this.value) || 0;
        const total = (quantity * pricePerItem).toFixed(2);
        
        // Update the total display if it exists
        const totalDisplay = document.querySelector('.fw-bold');
        if (totalDisplay) {
            totalDisplay.textContent = '$' + total;
        }
    });
});
</script>
{% endblock %}
