{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Checkout - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Checkout</h4>
                    <p class="mb-0 text-muted">Review your booking details and complete your reservation</p>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Cart Items Review -->
                        <h5>Services to Book</h5>
                        {% for item in cart.items.all %}
                        <div class="border p-3 mb-3">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>{{ item.service.service_title }}</h6>
                                    <p class="text-muted">{{ item.service.short_description }}</p>
                                    <p><strong>Venue:</strong> {{ item.service.venue.venue_name }}</p>
                                    <p><strong>Date:</strong> {{ item.selected_date|date:"F d, Y" }}</p>
                                    <p><strong>Time:</strong> {{ item.selected_time_slot }}</p>
                                </div>
                                <div class="col-md-4 text-right">
                                    <p><strong>Quantity:</strong> {{ item.quantity }}</p>
                                    <p><strong>Price:</strong> ${{ item.price_per_item }} each</p>
                                    <p class="h6"><strong>Total:</strong> ${{ item.total_price }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- Special Notes -->
                        <div class="form-group mt-4">
                            <label for="{{ form.notes.id_for_label }}">Special Notes (Optional)</label>
                            {{ form.notes }}
                            {% if form.notes.help_text %}
                            <small class="form-text text-muted">{{ form.notes.help_text }}</small>
                            {% endif %}
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" target="_blank">Terms and Conditions</a> and 
                                <a href="#" target="_blank">Cancellation Policy</a>
                            </label>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                Complete Booking
                            </button>
                            <a href="{% url 'booking_cart_app:cart_view' %}" class="btn btn-secondary btn-lg ml-2">
                                Back to Cart
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Order Summary</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <span>Subtotal:</span>
                        <span>${{ cart.total_price }}</span>
                    </div>
                    
                    {% if discount %}
                    <div class="d-flex justify-content-between text-success">
                        <span>Discount ({{ discount.code }}):</span>
                        <span>-${{ discount_amount }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between">
                        <span>Tax:</span>
                        <span>${{ tax_amount|default:"0.00" }}</span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between h5">
                        <strong>Total:</strong>
                        <strong>${{ final_total|default:cart.total_price }}</strong>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            You will receive a confirmation email after booking.
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6>Cancellation Policy</h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <ul class="mb-0">
                            <li>Free cancellation up to 24 hours before your appointment</li>
                            <li>50% refund for cancellations within 24 hours</li>
                            <li>No refund for no-shows</li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function(e) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
    });
});
</script>
{% endblock %}
