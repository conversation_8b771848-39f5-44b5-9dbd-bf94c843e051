{% extends 'booking_cart_app/base.html' %}

{% block title %}Booking Details - Admin - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<style>
    .admin-bg {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    .detail-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .status-badge {
        font-size: 1rem;
        padding: 0.5rem 1rem;
    }
    .info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.9rem;
    }
    .info-value {
        font-size: 1rem;
        color: #212529;
    }
    .dispute-alert {
        border-left: 4px solid #dc3545;
        background-color: #f8d7da;
    }
    .action-buttons {
        position: sticky;
        top: 20px;
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:admin_booking_dashboard' %}">Booking Management</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:admin_booking_list' %}">All Bookings</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Booking Details</li>
                    </ol>
                </nav>

                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title mb-2">
                            <i class="fas fa-calendar-check me-2"></i>Booking Details
                        </h1>
                        <p class="text-muted">Booking ID: <code>{{ booking.booking_id }}</code></p>
                    </div>
                    <div>
                        {% if booking.status == 'pending' %}
                            <span class="badge status-badge" style="background-color: white; color: black; border: 2px solid black;">{{ booking.get_status_display }}</span>
                        {% elif booking.status == 'confirmed' %}
                            <span class="badge status-badge" style="background-color: black; color: white;">{{ booking.get_status_display }}</span>
                        {% elif booking.status == 'cancelled' %}
                            <span class="badge status-badge" style="background-color: white; color: black; border: 2px solid black;">{{ booking.get_status_display }}</span>
                        {% elif booking.status == 'declined' %}
                            <span class="badge status-badge" style="background-color: black; color: white;">{{ booking.get_status_display }}</span>
                        {% elif booking.status == 'completed' %}
                            <span class="badge status-badge" style="background-color: black; color: white;">{{ booking.get_status_display }}</span>
                        {% elif booking.status == 'disputed' %}
                            <span class="badge status-badge" style="background-color: black; color: white;">{{ booking.get_status_display }}</span>
                        {% elif booking.status == 'no_show' %}
                            <span class="badge status-badge" style="background-color: white; color: black; border: 2px solid black;">{{ booking.get_status_display }}</span>
                        {% else %}
                            <span class="badge status-badge" style="background-color: white; color: black; border: 2px solid black;">{{ booking.get_status_display }}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Dispute Alert -->
        {% if booking.status == 'disputed' and not booking.dispute_resolved_at %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert dispute-alert" role="alert">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>Unresolved Dispute
                    </h6>
                    <p class="mb-2"><strong>Filed by:</strong> {{ booking.get_dispute_filed_by_display }}</p>
                    <p class="mb-2"><strong>Filed on:</strong> {{ booking.dispute_filed_at|date:"M d, Y g:i A" }}</p>
                    <p class="mb-3"><strong>Reason:</strong> {{ booking.dispute_reason|default:"No reason provided" }}</p>
                    <a href="{% url 'booking_cart_app:admin_resolve_dispute' booking.booking_id %}" class="btn btn-danger">
                        <i class="fas fa-gavel me-1"></i>Resolve Dispute
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Booking Information -->
                <div class="card detail-card mb-4">
                    <div class="card-header" style="background-color: black; color: white;">
                        <h6 class="card-title mb-0" style="color: white;">
                            <i class="fas fa-info-circle me-2"></i>Booking Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="info-label">Booking Date</div>
                                    <div class="info-value">{{ booking.booking_date|date:"M d, Y g:i A" }}</div>
                                </div>
                                <div class="mb-3">
                                    <div class="info-label">Total Price</div>
                                    <div class="info-value">${{ booking.total_price|floatformat:2 }}</div>
                                </div>
                                <div class="mb-3">
                                    <div class="info-label">Last Status Change</div>
                                    <div class="info-value">{{ booking.last_status_change|date:"M d, Y g:i A" }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="info-label">Can Be Cancelled</div>
                                    <div class="info-value">
                                        {% if booking.can_be_cancelled %}
                                            <span class="text-success">Yes</span>
                                        {% else %}
                                            <span class="text-danger">No</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% if booking.notes %}
                                <div class="mb-3">
                                    <div class="info-label">Customer Notes</div>
                                    <div class="info-value">{{ booking.notes }}</div>
                                </div>
                                {% endif %}
                                {% if booking.cancellation_reason %}
                                <div class="mb-3">
                                    <div class="info-label">Cancellation Reason</div>
                                    <div class="info-value">{{ booking.cancellation_reason }}</div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="card detail-card mb-4">
                    <div class="card-header" style="background-color: black; color: white;">
                        <h6 class="card-title mb-0" style="color: white;">
                            <i class="fas fa-user me-2"></i>Customer Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="info-label">Name</div>
                                    <div class="info-value">{{ booking.customer.get_full_name|default:"Not provided" }}</div>
                                </div>
                                <div class="mb-3">
                                    <div class="info-label">Email</div>
                                    <div class="info-value">{{ booking.customer.email }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="info-label">Member Since</div>
                                    <div class="info-value">{{ booking.customer.date_joined|date:"M d, Y" }}</div>
                                </div>
                                <div class="mb-3">
                                    <div class="info-label">Total Bookings</div>
                                    <div class="info-value">{{ booking.customer.bookings.count }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Venue Information -->
                <div class="card detail-card mb-4">
                    <div class="card-header" style="background-color: black; color: white;">
                        <h6 class="card-title mb-0" style="color: white;">
                            <i class="fas fa-building me-2"></i>Venue Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="info-label">Venue Name</div>
                                    <div class="info-value">{{ booking.venue.venue_name }}</div>
                                </div>
                                <div class="mb-3">
                                    <div class="info-label">Service Provider</div>
                                    <div class="info-value">{{ booking.venue.service_provider.get_full_name|default:booking.venue.service_provider.user.email }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="info-label">Address</div>
                                    <div class="info-value">{{ booking.venue.address|default:"Not provided" }}</div>
                                </div>
                                <div class="mb-3">
                                    <div class="info-label">Phone</div>
                                    <div class="info-value">{{ booking.venue.phone_number|default:"Not provided" }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking Items -->
                <div class="card detail-card mb-4">
                    <div class="card-header" style="background-color: white; color: black; border: 2px solid black; border-bottom: 2px solid black;">
                        <h6 class="card-title mb-0" style="color: black;">
                            <i class="fas fa-list me-2"></i>Booked Services
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Service</th>
                                        <th>Date & Time</th>
                                        <th>Duration</th>
                                        <th>Quantity</th>
                                        <th>Price</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in booking_items %}
                                    <tr>
                                        <td>
                                            <strong>{{ item.service_title }}</strong>
                                        </td>
                                        <td>
                                            {{ item.scheduled_date|date:"M d, Y" }}<br>
                                            <small style="color: black; opacity: 0.6;">{{ item.scheduled_time|time:"g:i A" }} - {{ item.end_time|time:"g:i A" }}</small>
                                        </td>
                                        <td>{{ item.duration_minutes }} min</td>
                                        <td>{{ item.quantity }}</td>
                                        <td>${{ item.service_price|floatformat:2 }}</td>
                                        <td><strong>${{ item.total_price|floatformat:2 }}</strong></td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="5" class="text-end">Total:</th>
                                        <th>${{ booking.total_price|floatformat:2 }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Sidebar -->
            <div class="col-lg-4">
                <div class="action-buttons">
                    <div class="card detail-card">
                        <div class="card-header" style="background-color: black; color: white;">
                            <h6 class="card-title mb-0" style="color: white;">
                                <i class="fas fa-cogs me-2"></i>Admin Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                {% if can_update_status %}
                                <a href="{% url 'booking_cart_app:admin_update_booking_status' booking.booking_id %}" 
                                   class="btn btn-warning">
                                    <i class="fas fa-edit me-1"></i>Update Status
                                </a>
                                {% endif %}

                                {% if can_resolve_dispute %}
                                <a href="{% url 'booking_cart_app:admin_resolve_dispute' booking.booking_id %}" 
                                   class="btn btn-danger">
                                    <i class="fas fa-gavel me-1"></i>Resolve Dispute
                                </a>
                                {% endif %}

                                <a href="{% url 'booking_cart_app:admin_booking_list' %}" 
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to List
                                </a>

                                <a href="{% url 'booking_cart_app:admin_booking_list' %}?venue={{ booking.venue.id }}" 
                                   class="btn btn-outline-info">
                                    <i class="fas fa-building me-1"></i>Venue Bookings
                                </a>

                                <a href="{% url 'booking_cart_app:admin_booking_list' %}?search={{ booking.customer.email }}" 
                                   class="btn btn-outline-success">
                                    <i class="fas fa-user me-1"></i>Customer Bookings
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Dispute Information (if applicable) -->
                    {% if booking.status == 'disputed' %}
                    <div class="card detail-card mt-3">
                        <div class="card-header" style="background-color: black; color: white;">
                            <h6 class="card-title mb-0" style="color: white;">
                                <i class="fas fa-exclamation-triangle me-2"></i>Dispute Details
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="info-label">Filed By</div>
                                <div class="info-value">{{ booking.get_dispute_filed_by_display }}</div>
                            </div>
                            <div class="mb-3">
                                <div class="info-label">Filed On</div>
                                <div class="info-value">{{ booking.dispute_filed_at|date:"M d, Y g:i A" }}</div>
                            </div>
                            <div class="mb-3">
                                <div class="info-label">Reason</div>
                                <div class="info-value">{{ booking.dispute_reason|default:"No reason provided" }}</div>
                            </div>
                            {% if booking.dispute_resolved_at %}
                            <div class="mb-3">
                                <div class="info-label">Resolved On</div>
                                <div class="info-value">{{ booking.dispute_resolved_at|date:"M d, Y g:i A" }}</div>
                            </div>
                            {% if booking.dispute_resolution_notes %}
                            <div class="mb-3">
                                <div class="info-label">Resolution Notes</div>
                                <div class="info-value">{{ booking.dispute_resolution_notes }}</div>
                            </div>
                            {% endif %}
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    });
</script>
{% endblock %}
