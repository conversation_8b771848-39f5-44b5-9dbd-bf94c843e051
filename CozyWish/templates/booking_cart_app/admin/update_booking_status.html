{% extends 'booking_cart_app/base.html' %}

{% block title %}Update Booking Status - Admin - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<style>
    .admin-bg {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    .detail-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .status-form {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .current-status {
        font-size: 1.2rem;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
    }
    .info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.9rem;
    }
    .info-value {
        font-size: 1rem;
        color: #212529;
    }
</style>
{% endblock %}

{% block booking_content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:admin_booking_dashboard' %}">Booking Management</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:admin_booking_detail' booking.booking_id %}">Booking Details</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Update Status</li>
                    </ol>
                </nav>

                <h1 class="page-title mb-3">
                    <i class="fas fa-edit me-2"></i>Update Booking Status
                </h1>
                <p class="text-muted">Booking ID: <code>{{ booking.booking_id }}</code></p>
            </div>
        </div>

        <div class="row">
            <!-- Status Update Form -->
            <div class="col-lg-8">
                <div class="status-form p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>Change Booking Status
                        </h5>
                        <div>
                            <span class="text-muted me-2">Current Status:</span>
                            {% if booking.status == 'pending' %}
                                <span class="badge current-status" style="background-color: white; color: black; border: 2px solid black;">{{ booking.get_status_display }}</span>
                            {% elif booking.status == 'confirmed' %}
                                <span class="badge current-status" style="background-color: black; color: white;">{{ booking.get_status_display }}</span>
                            {% elif booking.status == 'cancelled' %}
                                <span class="badge current-status" style="background-color: white; color: black; border: 2px solid black;">{{ booking.get_status_display }}</span>
                            {% elif booking.status == 'declined' %}
                                <span class="badge current-status" style="background-color: black; color: white;">{{ booking.get_status_display }}</span>
                            {% elif booking.status == 'completed' %}
                                <span class="badge current-status" style="background-color: black; color: white;">{{ booking.get_status_display }}</span>
                            {% elif booking.status == 'disputed' %}
                                <span class="badge current-status" style="background-color: black; color: white;">{{ booking.get_status_display }}</span>
                            {% elif booking.status == 'no_show' %}
                                <span class="badge current-status" style="background-color: white; color: black; border: 2px solid black;">{{ booking.get_status_display }}</span>
                            {% else %}
                                <span class="badge current-status" style="background-color: white; color: black; border: 2px solid black;">{{ booking.get_status_display }}</span>
                            {% endif %}
                        </div>
                    </div>

                    <form method="post" aria-label="Update booking status form">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.status.label_tag }}
                                    {{ form.status }}
                                    {% if form.status.help_text %}
                                        <div class="form-text">{{ form.status.help_text }}</div>
                                    {% endif %}
                                    {% if form.status.errors %}
                                        <div class="text-danger">
                                            {% for error in form.status.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Last Status Change</label>
                                    <div class="form-control-plaintext">{{ booking.last_status_change|date:"M d, Y g:i A" }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            {{ form.admin_notes.label_tag }}
                            {{ form.admin_notes }}
                            {% if form.admin_notes.help_text %}
                                <div class="form-text">{{ form.admin_notes.help_text }}</div>
                            {% endif %}
                            {% if form.admin_notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.admin_notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Status Change Guidelines
                            </h6>
                            <ul class="mb-0">
                                <li><strong>Confirmed:</strong> Service provider has accepted the booking</li>
                                <li><strong>Completed:</strong> Service has been provided successfully</li>
                                <li><strong>Cancelled:</strong> Booking was cancelled (customer or admin action)</li>
                                <li><strong>No Show:</strong> Customer did not show up for the appointment</li>
                                <li><strong>Disputed:</strong> There is a dispute that needs resolution</li>
                            </ul>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>Update Status
                            </button>
                            <a href="{% url 'booking_cart_app:admin_booking_detail' booking.booking_id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Booking Summary -->
            <div class="col-lg-4">
                <div class="card detail-card">
                    <div class="card-header" style="background-color: black; color: white;">
                        <h6 class="card-title mb-0" style="color: white;">
                            <i class="fas fa-info-circle me-2"></i>Booking Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="info-label">Booking ID</div>
                            <div class="info-value"><code>{{ booking.booking_id }}</code></div>
                        </div>
                        <div class="mb-3">
                            <div class="info-label">Total Amount</div>
                            <div class="info-value">${{ booking.total_price|floatformat:2 }}</div>
                        </div>
                        <div class="mb-3">
                            <div class="info-label">Booking Date</div>
                            <div class="info-value">{{ booking.booking_date|date:"M d, Y g:i A" }}</div>
                        </div>
                        <div class="mb-3">
                            <div class="info-label">Customer</div>
                            <div class="info-value">
                                {{ booking.customer.get_full_name|default:booking.customer.email }}<br>
                                <small style="color: black; opacity: 0.6;">{{ booking.customer.email }}</small>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="info-label">Venue</div>
                            <div class="info-value">
                                {{ booking.venue.venue_name }}<br>
                                <small style="color: black; opacity: 0.6;">{{ booking.venue.service_provider.get_full_name|default:booking.venue.service_provider.user.email }}</small>
                            </div>
                        </div>
                        
                        {% if booking.notes %}
                        <div class="mb-3">
                            <div class="info-label">Customer Notes</div>
                            <div class="info-value">{{ booking.notes }}</div>
                        </div>
                        {% endif %}

                        {% if booking.cancellation_reason %}
                        <div class="mb-3">
                            <div class="info-label">Cancellation Reason</div>
                            <div class="info-value">{{ booking.cancellation_reason }}</div>
                        </div>
                        {% endif %}
                        
                        <hr>
                        
                        <div class="d-grid gap-2">
                            <a href="{% url 'booking_cart_app:admin_booking_detail' booking.booking_id %}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Full Details
                            </a>
                            <a href="{% url 'booking_cart_app:admin_booking_list' %}" 
                               class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-list me-1"></i>All Bookings
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Status History -->
                <div class="card detail-card mt-3">
                    <div class="card-header" style="background-color: black; color: white;">
                        <h6 class="card-title mb-0" style="color: white;">
                            <i class="fas fa-history me-2"></i>Status History
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <div class="fw-bold">Booking Created</div>
                                    <div style="color: black; opacity: 0.6;" class="small">{{ booking.booking_date|date:"M d, Y g:i A" }}</div>
                                </div>
                            </div>
                            {% if booking.last_status_change != booking.booking_date %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <div class="fw-bold">Status: {{ booking.get_status_display }}</div>
                                    <div style="color: black; opacity: 0.6;" class="small">{{ booking.last_status_change|date:"M d, Y g:i A" }}</div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show confirmation before submitting status change
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const newStatus = document.querySelector('#id_status').value;
                const statusText = document.querySelector('#id_status option:checked').text;
                const confirmed = confirm(`Are you sure you want to change the booking status to "${statusText}"?`);
                if (!confirmed) {
                    e.preventDefault();
                }
            });
        }

        // Auto-focus on admin notes textarea
        const adminNotes = document.querySelector('#id_admin_notes');
        if (adminNotes) {
            adminNotes.focus();
        }
    });
</script>

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
    }
    .timeline-content {
        padding-left: 10px;
    }
</style>
{% endblock %}
