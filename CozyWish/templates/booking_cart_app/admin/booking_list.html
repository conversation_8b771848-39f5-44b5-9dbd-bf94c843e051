{% extends 'booking_cart_app/base.html' %}

{% block title %}All Bookings - Admin - CozyWish{% endblock %}

{% block booking_extra_css %}
<style>
    /* Booking list specific styles - black & white theme */
    .section-title {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .section-subtitle {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.7);
        font-size: 1rem;
        margin-bottom: 0;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.75rem;
        font-weight: 500;
        border-radius: 0.5rem;
    }

    .stats-summary {
        background: white;
        color: black;
        border-radius: 1rem;
        border: 2px solid black;
    }

    /* Breadcrumb styling */
    .booking-wrapper .breadcrumb {
        background-color: white;
        border: 2px solid black;
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
    }

    .booking-wrapper .breadcrumb-item a {
        color: black;
        text-decoration: none;
        font-weight: 500;
    }

    .booking-wrapper .breadcrumb-item a:hover {
        color: rgba(0, 0, 0, 0.7);
    }

    .booking-wrapper .breadcrumb-item.active {
        color: rgba(0, 0, 0, 0.6);
    }

    /* Card header styling */
    .booking-wrapper .card-header {
        background-color: black !important;
        color: white;
        border-bottom: 2px solid black;
        font-family: var(--font-heading);
        font-weight: 600;
    }

    .booking-wrapper .card-header.bg-light {
        background-color: white !important;
        color: black;
        border-bottom: 2px solid black;
    }
</style>
{% endblock %}

{% block booking_content %}
<!-- Header -->
<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:admin_booking_dashboard' %}">Booking Management</a></li>
                <li class="breadcrumb-item active" aria-current="page">All Bookings</li>
            </ol>
        </nav>

        <h1 class="section-title">
            <i class="fas fa-list me-2"></i>All Bookings
        </h1>
        <p class="section-subtitle">Manage and monitor all platform bookings</p>
    </div>
</div>

<!-- Statistics Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card stats-summary">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h3 class="mb-1">{{ total_count }}</h3>
                        <small>Total Bookings</small>
                    </div>
                    <div class="col-md-3">
                        <h3 class="mb-1">{{ pending_count }}</h3>
                        <small>Pending</small>
                    </div>
                    <div class="col-md-3">
                        <h3 class="mb-1">{{ disputed_count }}</h3>
                        <small>Disputed</small>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'booking_cart_app:admin_booking_analytics' %}" class="btn btn-outline-primary">
                            <i class="fas fa-chart-line me-1"></i>View Analytics
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>Filters
                </h6>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3" aria-label="Booking filter">
                    <div class="col-md-2">
                        {{ filter_form.status.label_tag }}
                        {{ filter_form.status }}
                    </div>
                    <div class="col-md-3">
                        {{ filter_form.venue.label_tag }}
                        {{ filter_form.venue }}
                    </div>
                    <div class="col-md-2">
                        {{ filter_form.date_from.label_tag }}
                        {{ filter_form.date_from }}
                    </div>
                    <div class="col-md-2">
                        {{ filter_form.date_to.label_tag }}
                        {{ filter_form.date_to }}
                    </div>
                    <div class="col-md-3">
                        {{ filter_form.search.label_tag }}
                        {{ filter_form.search }}
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Apply Filters
                        </button>
                        <a href="{% url 'booking_cart_app:admin_booking_list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bookings List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    Bookings ({{ bookings.paginator.count }} total)
                </h6>
            </div>
            <div class="card-body p-0">
                {% if bookings %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Customer</th>
                                    <th>Venue</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                                    <tbody>
                                        {% for booking in bookings %}
                                        <tr>
                                            <td>
                                                <code>{{ booking.booking_id|truncatechars:8 }}</code>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ booking.customer.get_full_name|default:booking.customer.email }}</strong><br>
                                                    <small class="text-muted">{{ booking.customer.email }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ booking.venue.venue_name|truncatechars:25 }}</strong><br>
                                                    <small class="text-muted">{{ booking.venue.service_provider.get_full_name|default:booking.venue.service_provider.user.email }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                {% if booking.status == 'pending' %}
                                                    <span class="badge bg-warning status-badge">{{ booking.get_status_display }}</span>
                                                {% elif booking.status == 'confirmed' %}
                                                    <span class="badge bg-success status-badge">{{ booking.get_status_display }}</span>
                                                {% elif booking.status == 'cancelled' %}
                                                    <span class="badge bg-primary status-badge">{{ booking.get_status_display }}</span>
                                                {% elif booking.status == 'declined' %}
                                                    <span class="badge bg-primary status-badge">{{ booking.get_status_display }}</span>
                                                {% elif booking.status == 'completed' %}
                                                    <span class="badge bg-success status-badge">{{ booking.get_status_display }}</span>
                                                {% elif booking.status == 'disputed' %}
                                                    <span class="badge bg-danger status-badge">{{ booking.get_status_display }}</span>
                                                {% elif booking.status == 'no_show' %}
                                                    <span class="badge bg-warning status-badge">{{ booking.get_status_display }}</span>
                                                {% else %}
                                                    <span class="badge bg-primary status-badge">{{ booking.get_status_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <strong>${{ booking.total_price|floatformat:2 }}</strong>
                                            </td>
                                            <td>
                                                <div>
                                                    {{ booking.booking_date|date:"M d, Y" }}<br>
                                                    <small class="text-muted">{{ booking.booking_date|time:"g:i A" }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'booking_cart_app:admin_booking_detail' booking.booking_id %}" 
                                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    {% if booking.status == 'disputed' and not booking.dispute_resolved_at %}
                                                    <a href="{% url 'booking_cart_app:admin_resolve_dispute' booking.booking_id %}" 
                                                       class="btn btn-sm btn-outline-danger" title="Resolve Dispute">
                                                        <i class="fas fa-gavel"></i>
                                                    </a>
                                                    {% endif %}
                                                    {% if booking.status not in 'completed' %}
                                                    <a href="{% url 'booking_cart_app:admin_update_booking_status' booking.booking_id %}" 
                                                       class="btn btn-sm btn-outline-warning" title="Update Status">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5>No bookings found</h5>
                                <p class="text-muted">Try adjusting your filters or check back later.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

<!-- Pagination -->
{% if bookings.has_other_pages %}
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Bookings pagination">
            <ul class="pagination justify-content-center">
                {% if bookings.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ bookings.previous_page_number }}">Previous</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        Page {{ bookings.number }} of {{ bookings.paginator.num_pages }}
                    </span>
                </li>

                {% if bookings.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ bookings.next_page_number }}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ bookings.paginator.num_pages }}">Last</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover effects to booking rows
        const bookingRows = document.querySelectorAll('tbody tr');
        bookingRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#f8f9fa';
            });
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    });
</script>
{% endblock %}
