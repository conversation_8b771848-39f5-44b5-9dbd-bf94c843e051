{% extends 'booking_cart_app/base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Bulk Add Availability - {{ service.service_title }} - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h4>Bulk Add Availability Slots</h4>
                    <small class="text-muted">{{ service.service_title }} - {{ service.venue.venue_name }}</small>
                </div>
                <div class="card-body">
                    <!-- Service Information -->
                    <div class="mb-4 p-3 bg-light rounded">
                        <div class="row">
                            <div class="col-md-4">
                                <p class="mb-1"><strong>Service:</strong> {{ service.service_title }}</p>
                                <p class="mb-0"><strong>Duration:</strong> {{ service.duration_display }}</p>
                            </div>
                            <div class="col-md-4">
                                <p class="mb-1"><strong>Price:</strong> {{ service.price_display }}</p>
                                <p class="mb-0"><strong>Venue:</strong> {{ service.venue.venue_name }}</p>
                            </div>
                            <div class="col-md-4">
                                <div class="alert alert-info mb-0 py-2">
                                    <small><i class="fas fa-info-circle"></i> This will create multiple time slots across the selected date range.</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Availability Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Date Range -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.start_date.id_for_label }}" class="form-label">
                                        {{ form.start_date.label }}
                                    </label>
                                    {{ form.start_date|add_class:"form-control" }}
                                    {% if form.start_date.help_text %}
                                        <small class="form-text text-muted">{{ form.start_date.help_text }}</small>
                                    {% endif %}
                                    {% if form.start_date.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.start_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.end_date.id_for_label }}" class="form-label">
                                        {{ form.end_date.label }}
                                    </label>
                                    {{ form.end_date|add_class:"form-control" }}
                                    {% if form.end_date.help_text %}
                                        <small class="form-text text-muted">{{ form.end_date.help_text }}</small>
                                    {% endif %}
                                    {% if form.end_date.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.end_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Time Range -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.start_time.id_for_label }}" class="form-label">
                                        {{ form.start_time.label }}
                                    </label>
                                    {{ form.start_time|add_class:"form-control" }}
                                    {% if form.start_time.help_text %}
                                        <small class="form-text text-muted">{{ form.start_time.help_text }}</small>
                                    {% endif %}
                                    {% if form.start_time.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.start_time.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.end_time.id_for_label }}" class="form-label">
                                        {{ form.end_time.label }}
                                    </label>
                                    {{ form.end_time|add_class:"form-control" }}
                                    {% if form.end_time.help_text %}
                                        <small class="form-text text-muted">{{ form.end_time.help_text }}</small>
                                    {% endif %}
                                    {% if form.end_time.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.end_time.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Slot Configuration -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="{{ form.interval.id_for_label }}" class="form-label">
                                        {{ form.interval.label }}
                                    </label>
                                    {{ form.interval|add_class:"form-control" }}
                                    {% if form.interval.help_text %}
                                        <small class="form-text text-muted">{{ form.interval.help_text }}</small>
                                    {% endif %}
                                    {% if form.interval.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.interval.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="{{ form.max_bookings.id_for_label }}" class="form-label">
                                        {{ form.max_bookings.label }}
                                    </label>
                                    {{ form.max_bookings|add_class:"form-control" }}
                                    {% if form.max_bookings.help_text %}
                                        <small class="form-text text-muted">{{ form.max_bookings.help_text }}</small>
                                    {% endif %}
                                    {% if form.max_bookings.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.max_bookings.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="form-check">
                                        {{ form.is_available|add_class:"form-check-input" }}
                                        <label for="{{ form.is_available.id_for_label }}" class="form-check-label">
                                            {{ form.is_available.label }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Section -->
                        <div class="mb-4 p-3 bg-light rounded">
                            <h6>Preview</h6>
                            <p class="mb-0 text-muted" id="preview-text">Configure the settings above to see a preview of slots that will be created.</p>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'booking_cart_app:provider_service_availability' service.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-layer-group"></i> Create Slots
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block booking_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum dates to today
    const startDateInput = document.getElementById('{{ form.start_date.id_for_label }}');
    const endDateInput = document.getElementById('{{ form.end_date.id_for_label }}');
    
    if (startDateInput && endDateInput) {
        const today = new Date().toISOString().split('T')[0];
        startDateInput.min = today;
        endDateInput.min = today;
        
        // Update end date minimum when start date changes
        startDateInput.addEventListener('change', function() {
            endDateInput.min = this.value;
        });
    }
    
    // Preview functionality
    function updatePreview() {
        const startDate = startDateInput?.value;
        const endDate = endDateInput?.value;
        const startTime = document.getElementById('{{ form.start_time.id_for_label }}')?.value;
        const endTime = document.getElementById('{{ form.end_time.id_for_label }}')?.value;
        const interval = document.getElementById('{{ form.interval.id_for_label }}')?.value;
        
        const previewText = document.getElementById('preview-text');
        
        if (startDate && endDate && startTime && endTime && interval) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;
            
            const startMinutes = timeToMinutes(startTime);
            const endMinutes = timeToMinutes(endTime);
            const slotsPerDay = Math.floor((endMinutes - startMinutes) / parseInt(interval));
            const totalSlots = days * slotsPerDay;
            
            previewText.textContent = `This will create approximately ${totalSlots} slots across ${days} day(s), with ${slotsPerDay} slots per day.`;
        }
    }
    
    function timeToMinutes(time) {
        const [hours, minutes] = time.split(':').map(Number);
        return hours * 60 + minutes;
    }
    
    // Add event listeners for preview
    ['{{ form.start_date.id_for_label }}', '{{ form.end_date.id_for_label }}', 
     '{{ form.start_time.id_for_label }}', '{{ form.end_time.id_for_label }}', 
     '{{ form.interval.id_for_label }}'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', updatePreview);
        }
    });
});
</script>
{% endblock %}
