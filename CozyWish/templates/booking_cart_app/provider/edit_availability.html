{% extends 'booking_cart_app/base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Edit Availability - {{ service.service_title }} - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Edit Availability Slot</h4>
                    <small class="text-muted">{{ service.service_title }} - {{ service.venue.venue_name }}</small>
                </div>
                <div class="card-body">
                    <!-- Current Slot Information -->
                    <div class="mb-4 p-3 bg-light rounded">
                        <h6>Current Slot Details</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Date:</strong> {{ availability.available_date|date:"F d, Y" }}</p>
                                <p class="mb-1"><strong>Time:</strong> {{ availability.start_time }} - {{ availability.end_time }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Max Bookings:</strong> {{ availability.max_bookings }}</p>
                                <p class="mb-1"><strong>Current Bookings:</strong> {{ availability.current_bookings }}</p>
                                <p class="mb-0">
                                    <strong>Status:</strong> 
                                    {% if availability.is_available %}
                                        <span class="badge bg-success">Available</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Unavailable</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Warning for existing bookings -->
                    {% if availability.current_bookings > 0 %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Warning:</strong> This slot has {{ availability.current_bookings }} existing booking(s). 
                        Changes may affect customer bookings.
                    </div>
                    {% endif %}

                    <!-- Edit Availability Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.available_date.id_for_label }}" class="form-label">
                                        {{ form.available_date.label }}
                                    </label>
                                    {{ form.available_date|add_class:"form-control" }}
                                    {% if form.available_date.help_text %}
                                        <small class="form-text text-muted">{{ form.available_date.help_text }}</small>
                                    {% endif %}
                                    {% if form.available_date.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.available_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.max_bookings.id_for_label }}" class="form-label">
                                        {{ form.max_bookings.label }}
                                    </label>
                                    {{ form.max_bookings|add_class:"form-control" }}
                                    {% if form.max_bookings.help_text %}
                                        <small class="form-text text-muted">{{ form.max_bookings.help_text }}</small>
                                    {% endif %}
                                    {% if form.max_bookings.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.max_bookings.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.start_time.id_for_label }}" class="form-label">
                                        {{ form.start_time.label }}
                                    </label>
                                    {{ form.start_time|add_class:"form-control" }}
                                    {% if form.start_time.help_text %}
                                        <small class="form-text text-muted">{{ form.start_time.help_text }}</small>
                                    {% endif %}
                                    {% if form.start_time.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.start_time.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.end_time.id_for_label }}" class="form-label">
                                        {{ form.end_time.label }}
                                    </label>
                                    {{ form.end_time|add_class:"form-control" }}
                                    {% if form.end_time.help_text %}
                                        <small class="form-text text-muted">{{ form.end_time.help_text }}</small>
                                    {% endif %}
                                    {% if form.end_time.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.end_time.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <div class="form-check">
                                {{ form.is_available|add_class:"form-check-input" }}
                                <label for="{{ form.is_available.id_for_label }}" class="form-check-label">
                                    {{ form.is_available.label }}
                                </label>
                                {% if form.is_available.help_text %}
                                    <small class="form-text text-muted d-block">{{ form.is_available.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Available Spots Display -->
                        <div class="mb-3 p-3 bg-light rounded">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Available Spots:</strong> {{ availability.available_spots }}
                                </div>
                                <div class="col-md-6">
                                    <strong>Fully Booked:</strong> 
                                    {% if availability.is_fully_booked %}
                                        <span class="text-danger">Yes</span>
                                    {% else %}
                                        <span class="text-success">No</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'booking_cart_app:provider_service_availability' service.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                            <div>
                                {% if availability.current_bookings == 0 %}
                                <form method="post" action="{% url 'booking_cart_app:provider_delete_availability' availability.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-danger me-2" 
                                            onclick="return confirm('Are you sure you want to delete this availability slot?')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                                {% endif %}
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Availability
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block booking_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validate max bookings against current bookings
    const maxBookingsInput = document.getElementById('{{ form.max_bookings.id_for_label }}');
    const currentBookings = {{ availability.current_bookings }};
    
    if (maxBookingsInput) {
        maxBookingsInput.addEventListener('change', function() {
            const newMax = parseInt(this.value);
            if (newMax < currentBookings) {
                alert(`Warning: Maximum bookings cannot be less than current bookings (${currentBookings})`);
                this.value = currentBookings;
            }
        });
        
        // Set minimum value
        maxBookingsInput.min = currentBookings;
    }
});
</script>
{% endblock %}
