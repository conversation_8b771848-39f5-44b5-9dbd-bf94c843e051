{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Availability Calendar - CozyWish{% endblock %}

{% block booking_extra_css %}
<style>
    /* Availability calendar specific styles - black & white theme */
    .section-title {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .calendar-date {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        font-size: 1.25rem;
        margin-top: 2rem;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid black;
    }

    .slot-item {
        background: white;
        border: 2px solid black;
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        font-family: var(--font-primary);
        font-weight: 500;
        color: black;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .slot-item.full {
        background-color: rgba(0, 0, 0, 0.1);
        color: rgba(0, 0, 0, 0.5);
    }

    .slot-status {
        font-size: 0.8rem;
        padding: 0.25rem 0.75rem;
        border-radius: 0.5rem;
        font-weight: 500;
    }

    .slot-status.available {
        background-color: white;
        color: black;
        border: 2px solid black;
    }

    .slot-status.full {
        background-color: white;
        color: black;
        border: 2px solid black;
    }
</style>
{% endblock %}

{% block booking_content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'booking_cart_app:provider_availability_list' %}">Availability</a></li>
        <li class="breadcrumb-item active" aria-current="page">Calendar</li>
    </ol>
</nav>

<!-- Page Header -->
<h1 class="section-title">{{ service.service_title }} Availability Calendar</h1>

<!-- Calendar Container -->
<div class="card">
    <div class="card-body">
        {% for date, slots in calendar.items %}
        <h5 class="calendar-date">{{ date }}</h5>
        <div class="row">
            {% for slot in slots %}
            <div class="col-md-6 col-lg-4 mb-2">
                <div class="slot-item {% if slot.is_fully_booked %}full{% endif %}">
                    <span>{{ slot.start_time }} - {{ slot.end_time }}</span>
                    {% if slot.is_fully_booked %}
                        <span class="slot-status full">Full</span>
                    {% else %}
                        <span class="slot-status available">Available</span>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% empty %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h5>No availability slots</h5>
            <p class="text-muted">Set up your availability to start accepting bookings.</p>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
