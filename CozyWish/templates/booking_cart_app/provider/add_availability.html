{% extends 'booking_cart_app/base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Add Availability - {{ service.service_title }} - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Add Availability Slot</h4>
                    <small class="text-muted">{{ service.service_title }} - {{ service.venue.venue_name }}</small>
                </div>
                <div class="card-body">
                    <!-- Service Information -->
                    <div class="mb-4 p-3 bg-light rounded">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Service:</strong> {{ service.service_title }}</p>
                                <p class="mb-1"><strong>Duration:</strong> {{ service.duration_display }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Price:</strong> {{ service.price_display }}</p>
                                <p class="mb-0"><strong>Venue:</strong> {{ service.venue.venue_name }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Add Availability Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.available_date.id_for_label }}" class="form-label">
                                        {{ form.available_date.label }}
                                    </label>
                                    {{ form.available_date|add_class:"form-control" }}
                                    {% if form.available_date.help_text %}
                                        <small class="form-text text-muted">{{ form.available_date.help_text }}</small>
                                    {% endif %}
                                    {% if form.available_date.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.available_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.max_bookings.id_for_label }}" class="form-label">
                                        {{ form.max_bookings.label }}
                                    </label>
                                    {{ form.max_bookings|add_class:"form-control" }}
                                    {% if form.max_bookings.help_text %}
                                        <small class="form-text text-muted">{{ form.max_bookings.help_text }}</small>
                                    {% endif %}
                                    {% if form.max_bookings.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.max_bookings.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.start_time.id_for_label }}" class="form-label">
                                        {{ form.start_time.label }}
                                    </label>
                                    {{ form.start_time|add_class:"form-control" }}
                                    {% if form.start_time.help_text %}
                                        <small class="form-text text-muted">{{ form.start_time.help_text }}</small>
                                    {% endif %}
                                    {% if form.start_time.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.start_time.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.end_time.id_for_label }}" class="form-label">
                                        {{ form.end_time.label }}
                                    </label>
                                    {{ form.end_time|add_class:"form-control" }}
                                    {% if form.end_time.help_text %}
                                        <small class="form-text text-muted">{{ form.end_time.help_text }}</small>
                                    {% endif %}
                                    {% if form.end_time.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.end_time.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <div class="form-check">
                                {{ form.is_available|add_class:"form-check-input" }}
                                <label for="{{ form.is_available.id_for_label }}" class="form-check-label">
                                    {{ form.is_available.label }}
                                </label>
                                {% if form.is_available.help_text %}
                                    <small class="form-text text-muted d-block">{{ form.is_available.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'booking_cart_app:provider_service_availability' service.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Availability
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block booking_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const dateInput = document.getElementById('{{ form.available_date.id_for_label }}');
    if (dateInput) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.min = today;
    }
});
</script>
{% endblock %}
