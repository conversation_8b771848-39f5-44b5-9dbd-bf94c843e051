{% extends 'discount_app/base_discount.html' %}

{% block title %}{{ title }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
<link rel="stylesheet" href="{% static 'css/discount_app/discount_wireframe.css' %}">
{% endblock %}

{% block discount_content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center mb-3">
                <a href="{% url 'discount_app:provider_discount_list' %}" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h1 class="h3 mb-1">{{ title }}</h1>
                    <p class="text-muted mb-0">
                        {% if action == 'Create' %}
                            Create a discount that applies to all services in your venue
                        {% else %}
                            Update the venue discount details
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        <h5 class="mb-3">Basic Info</h5>

                        <!-- Discount Name -->
                        <div class="mb-4">
                            <label class="form-label" for="{{ form.name.id_for_label }}">
                                {{ form.name.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.name|add_class:"form-control" }}
                            {% if form.name.help_text %}
                                <div class="form-text">{{ form.name.help_text }}</div>
                            {% endif %}
                            {% if form.name.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.name.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label class="form-label" for="{{ form.description.id_for_label }}">
                                {{ form.description.label }}
                            </label>
                            {{ form.description|add_class:"form-control" }}
                            {% if form.description.help_text %}
                                <div class="form-text">{{ form.description.help_text }}</div>
                            {% endif %}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.description.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Discount Type and Value -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label" for="{{ form.discount_type.id_for_label }}">
                                    {{ form.discount_type.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.discount_type|add_class:"form-select" }}
                                {% if form.discount_type.help_text %}
                                    <div class="form-text">{{ form.discount_type.help_text }}</div>
                                {% endif %}
                                {% if form.discount_type.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.discount_type.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="{{ form.discount_value.id_for_label }}">
                                    {{ form.discount_value.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text" id="discount-prefix">%</span>
                                    {{ form.discount_value|add_class:"form-control" }}
                                </div>
                                {% if form.discount_value.help_text %}
                                    <div class="form-text">{{ form.discount_value.help_text }}</div>
                                {% endif %}
                                <div class="form-text" id="venue-price-preview"></div>
                                {% if form.discount_value.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.discount_value.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <h5 class="mb-3">Validity</h5>
                        <!-- Date Range -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label" for="{{ form.start_date.id_for_label }}">
                                    {{ form.start_date.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.start_date|add_class:"form-control" }}
                                {% if form.start_date.help_text %}
                                    <div class="form-text">{{ form.start_date.help_text }}</div>
                                {% endif %}
                                {% if form.start_date.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.start_date.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="{{ form.end_date.id_for_label }}">
                                    {{ form.end_date.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.end_date|add_class:"form-control" }}
                                {% if form.end_date.help_text %}
                                    <div class="form-text">{{ form.end_date.help_text }}</div>
                                {% endif %}
                                {% if form.end_date.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.end_date.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Advanced Options -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>Advanced Options
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label" for="{{ form.min_booking_value.id_for_label }}">
                                            {{ form.min_booking_value.label }}
                                            <i class="fas fa-question-circle ms-1" data-bs-toggle="tooltip" title="Minimum booking total required to apply this discount."></i>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            {{ form.min_booking_value|add_class:"form-control" }}
                                        </div>
                                        {% if form.min_booking_value.help_text %}
                                            <div class="form-text">{{ form.min_booking_value.help_text }}</div>
                                        {% endif %}
                                        {% if form.min_booking_value.errors %}
                                            <div class="text-danger small mt-1">
                                                {% for error in form.min_booking_value.errors %}
                                                    <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label" for="{{ form.max_discount_amount.id_for_label }}">
                                            {{ form.max_discount_amount.label }}
                                            <i class="fas fa-question-circle ms-1" data-bs-toggle="tooltip" title="Maximum discount amount allowed when using a percentage value."></i>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            {{ form.max_discount_amount|add_class:"form-control" }}
                                        </div>
                                        {% if form.max_discount_amount.help_text %}
                                            <div class="form-text">{{ form.max_discount_amount.help_text }}</div>
                                        {% endif %}
                                        {% if form.max_discount_amount.errors %}
                                            <div class="text-danger small mt-1">
                                                {% for error in form.max_discount_amount.errors %}
                                                    <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form-wide errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger mb-4">
                                {% for error in form.non_field_errors %}
                                    <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Important Notes -->
                        <div class="alert alert-info mb-4">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Important Notes
                            </h6>
                            <ul class="mb-0">
                                <li>Maximum discount allowed is 80% for percentage discounts</li>
                                <li>Venue discounts apply to all services in your venue</li>
                                <li>Discounts require admin approval before becoming visible to customers</li>
                                <li>Minimum booking value sets a threshold for discount eligibility</li>
                                <li>Maximum discount amount caps the total discount for percentage discounts</li>
                            </ul>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'discount_app:provider_discount_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ action }} Discount
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const discountTypeField = document.getElementById('{{ form.discount_type.id_for_label }}');
    const discountPrefix = document.getElementById('discount-prefix');
    const discountValueField = document.getElementById('{{ form.discount_value.id_for_label }}');
    const preview = document.getElementById('venue-price-preview');

    function updateDiscountPrefix() {
        if (discountTypeField.value === 'percentage') {
            discountPrefix.textContent = '%';
        } else {
            discountPrefix.textContent = '$';
        }
    }

    function updatePreview() {
        const value = parseFloat(discountValueField.value);
        const base = 100;
        if (!value) {
            preview.textContent = '';
            return;
        }
        let discounted = base;
        if (discountTypeField.value === 'percentage') {
            discounted = Math.max(base - (base * value / 100), 0).toFixed(2);
        } else {
            discounted = Math.max(base - value, 0).toFixed(2);
        }
        preview.textContent = `Example price after discount: $${discounted} on a $${base} booking`;
    }

    // Update prefix on page load
    updateDiscountPrefix();
    updatePreview();

    // Update prefix when discount type changes
    discountTypeField.addEventListener('change', function(){
        updateDiscountPrefix();
        updatePreview();
    });
    discountValueField.addEventListener('input', updatePreview);
});
</script>
{% endblock %}
