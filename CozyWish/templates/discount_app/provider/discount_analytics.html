{% extends 'discount_app/base_discount.html' %}
{% load static i18n %}
{% load discount_tags %}

{% block title %}{% trans "Discount Analytics" %} - CozyWish{% endblock %}

{% block discount_extra_css %}
<style>
    /* Analytics specific styles - black & white theme */
    .analytics-card {
        background-color: white;
        border: 2px solid black;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: transform 0.2s ease;
    }

    .analytics-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: black;
        font-family: var(--font-heading);
        line-height: 1;
    }

    .metric-label {
        color: rgba(0, 0, 0, 0.7);
        font-size: 0.9rem;
        font-weight: 500;
        margin-top: 0.5rem;
    }

    .metric-icon {
        background-color: black;
        color: white;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .chart-container {
        background-color: white;
        border: 2px solid black;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .discount-performance-item {
        background-color: #f8f9fa;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 0.75rem;
        transition: background-color 0.2s ease;
    }

    .discount-performance-item:hover {
        background-color: #e9ecef;
    }

    .performance-rank {
        background-color: black;
        color: white;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .date-range-selector {
        background-color: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }

    .date-range-selector .btn {
        border: none;
        background: transparent;
        color: black;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }

    .date-range-selector .btn.active {
        background-color: black;
        color: white;
    }

    .date-range-selector .btn:hover:not(.active) {
        background-color: #f8f9fa;
    }

    .section-title {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .no-data-message {
        text-align: center;
        padding: 3rem 1rem;
        color: rgba(0, 0, 0, 0.6);
    }

    .no-data-message i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: rgba(0, 0, 0, 0.3);
    }
</style>
{% endblock %}

{% block discount_content %}
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="section-title">{% trans "Discount Analytics" %}</h1>
                <p class="text-muted">{% trans "Performance insights for" %} {{ venue.venue_name }}</p>
            </div>
            <div class="date-range-selector">
                <a href="?range=7" class="btn {% if date_range == '7' %}active{% endif %}">7 Days</a>
                <a href="?range=30" class="btn {% if date_range == '30' %}active{% endif %}">30 Days</a>
                <a href="?range=90" class="btn {% if date_range == '90' %}active{% endif %}">90 Days</a>
                <a href="?range=365" class="btn {% if date_range == '365' %}active{% endif %}">1 Year</a>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row">
    <div class="col-md-3">
        <div class="analytics-card">
            <div class="d-flex align-items-center">
                <div class="metric-icon me-3">
                    <i class="fas fa-tags"></i>
                </div>
                <div>
                    <div class="metric-value">{{ total_discounts }}</div>
                    <div class="metric-label">Total Discounts</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="analytics-card">
            <div class="d-flex align-items-center">
                <div class="metric-icon me-3">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div>
                    <div class="metric-value">{{ active_discounts }}</div>
                    <div class="metric-label">Active Discounts</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="analytics-card">
            <div class="d-flex align-items-center">
                <div class="metric-icon me-3">
                    <i class="fas fa-users"></i>
                </div>
                <div>
                    <div class="metric-value">{{ total_usage }}</div>
                    <div class="metric-label">Total Uses</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="analytics-card">
            <div class="d-flex align-items-center">
                <div class="metric-icon me-3">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div>
                    <div class="metric-value">${{ total_savings|floatformat:0 }}</div>
                    <div class="metric-label">Customer Savings</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Performing Discounts -->
<div class="row">
    <div class="col-12">
        <div class="chart-container">
            <h3 class="section-title">
                <i class="fas fa-trophy me-2"></i>Top Performing Discounts
            </h3>
            {% if top_discounts %}
                {% for discount_data in top_discounts %}
                <div class="discount-performance-item">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="performance-rank">{{ forloop.counter }}</div>
                        </div>
                        <div class="col">
                            <h6 class="mb-1">{{ discount_data.discount.name }}</h6>
                            <small class="text-muted">
                                {% if discount_data.type == 'service' %}
                                    <i class="fas fa-concierge-bell me-1"></i>{{ discount_data.discount.service.service_title }}
                                {% else %}
                                    <i class="fas fa-building me-1"></i>Venue-wide discount
                                {% endif %}
                            </small>
                        </div>
                        <div class="col-auto text-end">
                            <div class="fw-bold">{{ discount_data.usage_count }} uses</div>
                            <small class="text-muted">${{ discount_data.savings_total|floatformat:0 }} saved</small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-data-message">
                    <i class="fas fa-chart-bar"></i>
                    <h5>No Usage Data</h5>
                    <p>No discount usage recorded for the selected period.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Discount Breakdown -->
<div class="row">
    <div class="col-md-6">
        <div class="chart-container">
            <h3 class="section-title">
                <i class="fas fa-pie-chart me-2"></i>Discount Types
            </h3>
            <div class="row text-center">
                <div class="col-6">
                    <div class="metric-value text-primary">{{ service_discounts_count }}</div>
                    <div class="metric-label">Service Discounts</div>
                </div>
                <div class="col-6">
                    <div class="metric-value text-success">{{ venue_discounts_count }}</div>
                    <div class="metric-label">Venue Discounts</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="chart-container">
            <h3 class="section-title">
                <i class="fas fa-calendar me-2"></i>Period Summary
            </h3>
            <div class="row text-center">
                <div class="col-12 mb-3">
                    <small class="text-muted">{{ start_date|date:"M d, Y" }} - {{ end_date|date:"M d, Y" }}</small>
                </div>
                <div class="col-6">
                    <div class="metric-value text-info">${{ total_revenue|floatformat:0 }}</div>
                    <div class="metric-label">Revenue Generated</div>
                </div>
                <div class="col-6">
                    <div class="metric-value text-warning">
                        {% if total_revenue and total_savings %}
                            {{ total_savings|floatformat:0|add:total_revenue|floatformat:0|floatformat:1 }}%
                        {% else %}
                            0%
                        {% endif %}
                    </div>
                    <div class="metric-label">Avg Discount Rate</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row">
    <div class="col-12 text-center">
        <a href="{% url 'discount_app:provider_discount_list' %}" class="btn btn-outline-primary me-2">
            <i class="fas fa-list me-2"></i>Manage Discounts
        </a>
        <a href="{% url 'discount_app:create_service_discount' %}" class="btn btn-primary me-2">
            <i class="fas fa-plus me-2"></i>Create Service Discount
        </a>
        <a href="{% url 'discount_app:create_venue_discount' %}" class="btn btn-outline-primary">
            <i class="fas fa-plus me-2"></i>Create Venue Discount
        </a>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive features for analytics
    console.log('Discount Analytics loaded');
    
    // Animate metric values on load
    const metricValues = document.querySelectorAll('.metric-value');
    metricValues.forEach(function(element) {
        const finalValue = parseInt(element.textContent.replace(/[^0-9]/g, ''));
        if (finalValue > 0) {
            let currentValue = 0;
            const increment = Math.ceil(finalValue / 20);
            const timer = setInterval(function() {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    currentValue = finalValue;
                    clearInterval(timer);
                }
                element.textContent = element.textContent.replace(/\d+/, currentValue);
            }, 50);
        }
    });
});
</script>
{% endblock %}
