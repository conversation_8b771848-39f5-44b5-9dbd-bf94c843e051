{% extends 'discount_app/base_discount_admin.html' %}
{% load i18n %}

{% block title %}{{ title }} - {% trans "Platform Discount" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-globe me-2"></i>
                        {{ title }} {% trans "Platform Discount" %}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        {{ form.name.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.discount_type.id_for_label }}" class="form-label">
                                        {{ form.discount_type.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.discount_type }}
                                    {% if form.discount_type.errors %}
                                        <div class="text-danger small">{{ form.discount_type.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.discount_value.id_for_label }}" class="form-label">
                                        {{ form.discount_value.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.discount_value }}
                                    {% if form.discount_value.errors %}
                                        <div class="text-danger small">{{ form.discount_value.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">
                                        {% trans "Enter percentage (1-100) or fixed amount in dollars" %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.max_uses.id_for_label }}" class="form-label">
                                        {{ form.max_uses.label }}
                                    </label>
                                    {{ form.max_uses }}
                                    {% if form.max_uses.errors %}
                                        <div class="text-danger small">{{ form.max_uses.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">
                                        {% trans "Leave empty for unlimited uses" %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.start_date.id_for_label }}" class="form-label">
                                        {{ form.start_date.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.start_date }}
                                    {% if form.start_date.errors %}
                                        <div class="text-danger small">{{ form.start_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.end_date.id_for_label }}" class="form-label">
                                        {{ form.end_date.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.end_date }}
                                    {% if form.end_date.errors %}
                                        <div class="text-danger small">{{ form.end_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small">{{ form.description.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                {% trans "Provide details about this platform-wide discount" %}
                            </div>
                        </div>

                        {% if action == 'Edit' %}
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">{% trans "Current Status" %}</label>
                                        <div>
                                            {% if discount.is_active %}
                                                <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">{% trans "Usage Count" %}</label>
                                        <div>
                                            <span class="badge bg-info">{{ discount.get_usage_count }} {% trans "uses" %}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ action }} {% trans "Platform Discount" %}
                            </button>
                            <a href="{% url 'discount_app:admin_discount_list' 'platform' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to List" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form classes for Bootstrap styling
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        if (!input.classList.contains('form-check-input')) {
            input.classList.add('form-control');
        }
    });
    
    // Handle discount type change
    const discountTypeField = document.querySelector('#{{ form.discount_type.id_for_label }}');
    const discountValueField = document.querySelector('#{{ form.discount_value.id_for_label }}');
    
    if (discountTypeField && discountValueField) {
        function updateDiscountValuePlaceholder() {
            if (discountTypeField.value === 'percentage') {
                discountValueField.placeholder = '{% trans "Enter percentage (1-100)" %}';
                discountValueField.setAttribute('max', '100');
                discountValueField.setAttribute('min', '1');
            } else {
                discountValueField.placeholder = '{% trans "Enter dollar amount" %}';
                discountValueField.removeAttribute('max');
                discountValueField.setAttribute('min', '0.01');
            }
        }
        
        discountTypeField.addEventListener('change', updateDiscountValuePlaceholder);
        updateDiscountValuePlaceholder(); // Initial call
    }
});
</script>
{% endblock %}
