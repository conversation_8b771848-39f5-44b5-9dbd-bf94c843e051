{% extends 'discount_app/base_discount_admin.html' %}

{% block title %}{{ title }} - Admin Dashboard - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'css/admin_app/admin_app.css' %}">
<style>
    .admin-bg {
        background-color: #f8f9fa;
        min-height: 100vh;
    }
    .filter-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .discount-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .status-badge {
        font-size: 0.8rem;
    }
    .usage-stats {
        font-size: 0.85rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'discount_app:admin_discount_dashboard' %}">Discount Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
                    </ol>
                </nav>

                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title mb-1">
                            <i class="fas fa-list me-2"></i>{{ title }}
                        </h1>
                        <p class="text-muted">Manage and monitor {{ discount_type }} discounts</p>
                    </div>
                    {% if discount_type == 'platform' %}
                    <a href="{% url 'discount_app:admin_create_platform_discount' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Create Platform Discount
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card filter-card">
                    <div class="card-body">
                        <form method="get" class="row align-items-end" aria-label="Admin discount filter">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" name="search" id="search" class="form-control" aria-label="Search discounts"
                                       value="{{ search_query }}" placeholder="Search discounts...">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-select" aria-label="Status filter">
                                    <option value="">All Statuses</option>
                                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                                    <option value="scheduled" {% if status_filter == 'scheduled' %}selected{% endif %}>Scheduled</option>
                                    <option value="expired" {% if status_filter == 'expired' %}selected{% endif %}>Expired</option>
                                    {% if discount_type != 'platform' %}
                                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending Approval</option>
                                    {% endif %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="discount_type" class="form-label">Type</label>
                                <select name="discount_type" id="discount_type" class="form-select" aria-label="Discount type">
                                    <option value="">All Types</option>
                                    <option value="percentage" {% if discount_type_filter == 'percentage' %}selected{% endif %}>Percentage</option>
                                    <option value="fixed_amount" {% if discount_type_filter == 'fixed_amount' %}selected{% endif %}>Fixed Amount</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i> Filter
                                </button>
                                <a href="{% url 'discount_app:admin_discount_list' discount_type=discount_type %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i> Clear
                                </a>
                            </div>
                            <div class="col-md-2 text-end">
                                <small class="text-muted">{{ total_discounts }} total discounts</small>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a href="{% url 'discount_app:admin_discount_list' discount_type='venue' %}" 
                               class="btn {% if discount_type == 'venue' %}btn-success{% else %}btn-outline-success{% endif %}">
                                <i class="fas fa-store me-1"></i> Venue Discounts
                            </a>
                            <a href="{% url 'discount_app:admin_discount_list' discount_type='service' %}" 
                               class="btn {% if discount_type == 'service' %}btn-info{% else %}btn-outline-info{% endif %}">
                                <i class="fas fa-spa me-1"></i> Service Discounts
                            </a>
                            <a href="{% url 'discount_app:admin_discount_list' discount_type='platform' %}" 
                               class="btn {% if discount_type == 'platform' %}btn-secondary{% else %}btn-outline-secondary{% endif %}">
                                <i class="fas fa-globe me-1"></i> Platform Discounts
                            </a>
                        </div>
                        <div class="float-end">
                            <a href="{% url 'discount_app:admin_discount_dashboard' %}" class="btn btn-outline-primary">
                                <i class="fas fa-chart-line me-1"></i> Dashboard
                            </a>
                            <a href="{% url 'discount_app:admin_usage_analytics' %}" class="btn btn-warning">
                                <i class="fas fa-chart-bar me-1"></i> Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Discounts Table -->
        <div class="row">
            <div class="col-12">
                <div class="discount-table">
                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Discount Name</th>
                                    <th>Value</th>
                                    {% if discount_type == 'venue' %}
                                    <th>Venue</th>
                                    {% elif discount_type == 'service' %}
                                    <th>Service</th>
                                    {% elif discount_type == 'platform' %}
                                    <th>Category</th>
                                    {% endif %}
                                    <th>Valid Period</th>
                                    <th>Status</th>
                                    <th>Usage Stats</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in page_obj %}
                                {% with discount=item.discount %}
                                <tr>
                                    <td>
                                        <strong>{{ discount.name }}</strong>
                                        {% if discount.description %}
                                        <br><small class="text-muted">{{ discount.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if discount.discount_type == 'percentage' %}
                                        <span class="badge bg-primary">{{ discount.discount_value }}%</span>
                                        {% else %}
                                        <span class="badge bg-secondary">${{ discount.discount_value }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if discount_type == 'venue' %}
                                        <a href="{% url 'venues_app:venue_detail' discount.venue.slug %}">
                                            {{ discount.venue.venue_name|truncatechars:30 }}
                                        </a>
                                        {% elif discount_type == 'service' %}
                                        {{ discount.service.service_title|truncatechars:30 }}<br>
                                        <small class="text-muted">{{ discount.service.venue.venue_name|truncatechars:25 }}</small>
                                        {% elif discount_type == 'platform' %}
                                        {% if discount.category %}{{ discount.category.name }}{% else %}All Categories{% endif %}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>
                                            {{ discount.start_date|date:"M d, Y" }}<br>
                                            to {{ discount.end_date|date:"M d, Y" }}
                                        </small>
                                    </td>
                                    <td>
                                        {% if discount.get_status == 'active' %}
                                        <span class="badge bg-success status-badge">Active</span>
                                        {% elif discount.get_status == 'scheduled' %}
                                        <span class="badge bg-info status-badge">Scheduled</span>
                                        {% elif discount.get_status == 'expired' %}
                                        <span class="badge bg-secondary status-badge">Expired</span>
                                        {% else %}
                                        <span class="badge bg-warning status-badge">{{ discount.get_status|title }}</span>
                                        {% endif %}
                                        
                                        {% if discount_type != 'platform' %}
                                        <br>
                                        {% if discount.is_approved %}
                                        <span class="badge bg-success status-badge">Approved</span>
                                        {% else %}
                                        <span class="badge bg-warning status-badge">Pending</span>
                                        {% endif %}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="usage-stats">
                                            <strong>{{ item.usage_count }}</strong> uses<br>
                                            <small>${{ item.total_savings|floatformat:2 }} saved</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical btn-group-sm">
                                            <a href="{% url 'discount_app:admin_discount_detail' discount_type=discount_type discount_id=discount.id %}" 
                                               class="btn btn-outline-primary btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if discount_type == 'platform' %}
                                            <a href="{% url 'discount_app:admin_edit_platform_discount' discount_id=discount.id %}" 
                                               class="btn btn-outline-warning btn-sm" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'discount_app:admin_delete_platform_discount' discount_id=discount.id %}" 
                                               class="btn btn-outline-danger btn-sm" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            {% else %}
                                            {% if not discount.is_approved %}
                                            <a href="{% url 'discount_app:admin_approve_discount' discount_type=discount_type discount_id=discount.id %}" 
                                               class="btn btn-outline-success btn-sm" title="Approve">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            {% endif %}
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endwith %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <div class="card-footer">
                        <nav aria-label="Discount pagination">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if discount_type_filter %}&discount_type={{ discount_type_filter }}{% endif %}">Previous</a>
                                </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if discount_type_filter %}&discount_type={{ discount_type_filter }}{% endif %}">{{ num }}</a>
                                </li>
                                {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if discount_type_filter %}&discount_type={{ discount_type_filter }}{% endif %}">Next</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5>No discounts found</h5>
                        <p class="text-muted">No {{ discount_type }} discounts match your current filters.</p>
                        {% if discount_type == 'platform' %}
                        <a href="{% url 'discount_app:admin_create_platform_discount' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Create First Platform Discount
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when filters change
        const filterSelects = document.querySelectorAll('#status, #discount_type');
        filterSelects.forEach(select => {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });

        // Search on Enter key
        const searchInput = document.getElementById('search');
        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    this.form.submit();
                }
            });
        }
    });
</script>
{% endblock %}
