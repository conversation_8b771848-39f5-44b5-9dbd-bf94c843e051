{% extends 'discount_app/base_discount.html' %}
{% block title %}{{ discount.name }} - Discount Details - CozyWish{% endblock %}

{% block discount_extra_css %}
<style>
    /* Enhanced discount detail styles - black & white theme */
    .discount-hero {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .discount-value {
        font-size: 3rem;
        font-weight: 700;
        color: black;
        font-family: var(--font-heading);
    }

    .discount-type-badge {
        background-color: black !important;
        color: white;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .price-comparison {
        background-color: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin: 1rem 0;
    }

    .original-price {
        text-decoration: line-through;
        color: rgba(0, 0, 0, 0.5);
        font-size: 1.2rem;
        font-weight: 400;
    }

    .discounted-price {
        color: black;
        font-weight: 700;
        font-size: 1.8rem;
        font-family: var(--font-heading);
    }

    .savings-highlight {
        background-color: black;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 600;
        display: inline-block;
        margin-top: 0.5rem;
    }

    .info-card {
        background-color: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .info-card h6 {
        color: black;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-family: var(--font-heading);
    }

    .countdown-timer {
        background-color: #f8f9fa;
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        margin: 1rem 0;
    }

    .countdown-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: black;
        font-family: var(--font-heading);
    }

    .urgency-badge {
        background-color: #dc3545;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .share-buttons {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        margin-top: 1rem;
    }

    .share-btn {
        background-color: white;
        color: black;
        border: 2px solid black;
        padding: 0.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .share-btn:hover {
        background-color: black;
        color: white;
    }

    .venue-card {
        background-color: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        overflow: hidden;
        transition: transform 0.2s ease;
    }

    .venue-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .venue-image {
        height: 200px;
        object-fit: cover;
        border-bottom: 2px solid black;
    }

    .cta-section {
        background-color: black;
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        text-align: center;
        margin: 2rem 0;
    }

    .cta-section h4 {
        color: white;
        margin-bottom: 1rem;
        font-family: var(--font-heading);
    }

    .btn-cta {
        background-color: white;
        color: black;
        border: 2px solid white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-cta:hover {
        background-color: transparent;
        color: white;
        border-color: white;
    }
</style>
{% endblock %}

{% block discount_content %}
{% load discount_tags %}

<!-- Discount Hero Section -->
<div class="row">
    <div class="col-12">
        <div class="discount-hero">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h2 mb-3">{{ discount.name }}</h1>
                    <span class="discount-type-badge">{{ discount_type|title }} Discount</span>
                    {% if discount.description %}
                        <p class="mt-3 mb-0">{{ discount.description }}</p>
                    {% endif %}
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="discount-value">
                        {% if discount.discount_type == 'percentage' %}
                            {{ discount.discount_value }}%
                        {% else %}
                            ${{ discount.discount_value }}
                        {% endif %}
                    </div>
                    <div class="text-muted">
                        {% if discount.discount_type == 'percentage' %}
                            OFF
                        {% else %}
                            DISCOUNT
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Countdown Timer (if discount is ending soon) -->
{% if discount|is_expiring_soon:7 %}
<div class="row">
    <div class="col-12">
        <div class="countdown-timer">
            <div class="d-flex align-items-center justify-content-center">
                <i class="fas fa-clock me-2"></i>
                <span class="countdown-number">{{ discount|days_until_expiry }}</span>
                <span class="ms-2">day{{ discount|days_until_expiry|pluralize }} remaining</span>
                {% if discount|is_expiring_soon:2 %}
                    <span class="urgency-badge ms-2">Limited Time!</span>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Price Comparison (for service discounts) -->
{% if discount_type == 'service' and related_entity %}
<div class="row">
    <div class="col-12">
        <div class="price-comparison">
            <h6><i class="fas fa-calculator me-2"></i>Price Comparison</h6>
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="original-price">Original Price: ${{ related_entity.price_min }}</div>
                    <div class="discounted-price">
                        Your Price: ${{ discount|calculate_discounted_price:related_entity.price_min }}
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="savings-highlight">
                        You Save: ${{ discount|calculate_savings:related_entity.price_min }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Discount Information Cards -->
<div class="row">
    <div class="col-md-6">
        <div class="info-card">
            <h6><i class="fas fa-info-circle me-2"></i>Discount Details</h6>
            <ul class="list-unstyled mb-0">
                <li><strong>Type:</strong> {{ discount.get_discount_type_display }}</li>
                <li><strong>Value:</strong>
                    {% if discount.discount_type == 'percentage' %}
                        {{ discount.discount_value }}%
                    {% else %}
                        ${{ discount.discount_value }}
                    {% endif %}
                </li>
                <li><strong>Valid From:</strong> {{ discount.start_date|date:"M d, Y" }}</li>
                <li><strong>Valid Until:</strong> {{ discount.end_date|date:"M d, Y" }}</li>
                {% if discount.max_uses %}
                    <li><strong>Usage Limit:</strong> {{ discount.max_uses }} uses</li>
                {% endif %}
            </ul>
        </div>
    </div>
    <div class="col-md-6">
        <div class="info-card">
            <h6><i class="fas fa-rules me-2"></i>Terms & Conditions</h6>
            <ul class="list-unstyled mb-0">
                {% if discount_type == 'venue' and discount.min_booking_value %}
                    <li>Minimum booking value: ${{ discount.min_booking_value }}</li>
                {% endif %}
                {% if discount.max_discount_amount %}
                    <li>Maximum discount: ${{ discount.max_discount_amount }}</li>
                {% endif %}
                <li>Valid for new bookings only</li>
                <li>Cannot be combined with other offers</li>
                <li>Subject to availability</li>
            </ul>
        </div>
    </div>
</div>

<!-- Venue/Service Information -->
{% if related_entity %}
<div class="row">
    <div class="col-12">
        <div class="venue-card">
            {% if discount_type == 'service' %}
                <img src="{{ related_entity.venue.get_primary_image|default:'https://via.placeholder.com/400x200' }}"
                     class="venue-image w-100" alt="{{ related_entity.venue.venue_name }}">
                <div class="card-body">
                    <h5 class="card-title">{{ related_entity.service_title }}</h5>
                    <p class="text-muted mb-2">
                        <i class="fas fa-building me-1"></i>{{ related_entity.venue.venue_name }}
                    </p>
                    <p class="text-muted mb-3">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ related_entity.venue.city }}, {{ related_entity.venue.state }}
                    </p>
                    <p class="card-text">{{ related_entity.service_description|truncatewords:20 }}</p>
                </div>
            {% elif discount_type == 'venue' %}
                <img src="{{ related_entity.get_primary_image|default:'https://via.placeholder.com/400x200' }}"
                     class="venue-image w-100" alt="{{ related_entity.venue_name }}">
                <div class="card-body">
                    <h5 class="card-title">{{ related_entity.venue_name }}</h5>
                    <p class="text-muted mb-3">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ related_entity.city }}, {{ related_entity.state }}
                    </p>
                    <p class="card-text">{{ related_entity.venue_description|truncatewords:20 }}</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

<!-- Call to Action Section -->
<div class="row">
    <div class="col-12">
        <div class="cta-section">
            <h4>Ready to Save?</h4>
            <p class="mb-3">Book now and enjoy this exclusive discount!</p>
            {% if discount_type == 'service' and related_entity %}
                <a href="{% url 'venues_app:service_detail' venue_slug=related_entity.venue.slug service_slug=related_entity.slug %}"
                   class="btn btn-cta me-2">
                    <i class="fas fa-calendar-plus me-2"></i>Book This Service
                </a>
            {% elif discount_type == 'venue' and related_entity %}
                <a href="{% url 'venues_app:venue_detail' slug=related_entity.slug %}"
                   class="btn btn-cta me-2">
                    <i class="fas fa-eye me-2"></i>View Venue
                </a>
            {% else %}
                <a href="{% url 'venues_app:venue_list' %}"
                   class="btn btn-cta me-2">
                    <i class="fas fa-search me-2"></i>Browse Venues
                </a>
            {% endif %}

            <!-- Share Buttons -->
            <div class="share-buttons">
                <button id="copy-btn" class="share-btn" title="Copy Link">
                    <i class="fas fa-copy"></i>
                </button>
                <a href="mailto:?subject=Check out this discount&body=I found this great discount: {{ request.build_absolute_uri }}"
                   class="share-btn" title="Share via Email">
                    <i class="fas fa-envelope"></i>
                </a>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="copy-toast" class="toast text-bg-success" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">Link copied to clipboard!</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Copy link functionality
    document.getElementById('copy-btn').addEventListener('click', function(){
        navigator.clipboard.writeText(window.location.href).then(function(){
            new bootstrap.Toast(document.getElementById('copy-toast')).show();
        }).catch(function() {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = window.location.href;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            new bootstrap.Toast(document.getElementById('copy-toast')).show();
        });
    });

    // Countdown timer animation (if present)
    const countdownElement = document.querySelector('.countdown-number');
    if (countdownElement) {
        const daysRemaining = parseInt(countdownElement.textContent);
        if (daysRemaining <= 2) {
            // Add pulsing animation for urgency
            countdownElement.style.animation = 'pulse 1.5s infinite';
        }
    }
});

// CSS animation for urgency
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
