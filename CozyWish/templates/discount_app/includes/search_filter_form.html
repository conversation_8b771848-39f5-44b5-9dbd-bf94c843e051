{% load i18n %}
<form method="get" action="{% url 'discount_app:search_discounts' %}">
    <div class="row g-3">
        <div class="col-12 col-sm-6 col-md-4">
            <label for="search" class="form-label">{% trans "Search Keywords" %}</label>
            <input type="text" class="form-control" id="search" name="search" aria-label="Search keywords"
                   value="{{ search_query }}" placeholder="{% trans 'Service, venue, or discount name...' %}">
        </div>
        <div class="col-6 col-sm-6 col-md-2">
            <label for="type" class="form-label">{% trans "Discount Type" %}</label>
            <select class="form-select" id="type" name="type" aria-label="Discount type">
                <option value="all" {% if discount_type_filter == 'all' %}selected{% endif %}>{% trans "All Types" %}</option>
                <option value="service" {% if discount_type_filter == 'service' %}selected{% endif %}>{% trans "Service" %}</option>
                <option value="venue" {% if discount_type_filter == 'venue' %}selected{% endif %}>{% trans "Venue" %}</option>
                <option value="platform" {% if discount_type_filter == 'platform' %}selected{% endif %}>{% trans "Platform" %}</option>
            </select>
        </div>
        <div class="col-6 col-sm-6 col-md-2">
            <label for="category" class="form-label">{% trans "Category" %}</label>
            <select class="form-select" id="category" name="category" aria-label="Category">
                <option value="">{% trans "All Categories" %}</option>
                {% for category in categories %}
                    <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:'s' %}selected{% endif %}>
                        {{ category.category_name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-6 col-sm-6 col-md-2">
            <label for="location" class="form-label">{% trans "Location" %}</label>
            <input type="text" class="form-control" id="location" name="location" aria-label="Location"
                   value="{{ location_filter }}" placeholder="{% trans 'City or State' %}" list="locationList" autocomplete="off">
            <datalist id="locationList"></datalist>
        </div>
        <div class="col-6 col-sm-6 col-md-2">
            <label for="min_discount" class="form-label">{% trans "Min Discount" %}</label>
            <input type="number" class="form-control" id="min_discount" name="min_discount" aria-label="Minimum discount percent"
                   value="{{ min_discount }}" placeholder="0" min="0" max="100">
        </div>
    </div>
    <div class="row g-3 mt-2">
        <div class="col-6 col-sm-6 col-md-2">
            <label for="start_date" class="form-label">{% trans "Start Date" %}</label>
            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}" aria-label="Start date">
        </div>
        <div class="col-6 col-sm-6 col-md-2">
            <label for="end_date" class="form-label">{% trans "End Date" %}</label>
            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}" aria-label="End date">
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-12">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-2"></i>{% trans "Search" %}
            </button>
            <a href="{% url 'discount_app:search_discounts' %}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-2"></i>{% trans "Clear Filters" %}
            </a>
        </div>
    </div>
</form>
