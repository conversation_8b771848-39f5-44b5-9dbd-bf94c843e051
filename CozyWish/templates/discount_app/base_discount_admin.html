{% extends 'admin_app/base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Discount Admin - Black & White Design */
        /* Matching homepage and accounts_app design with clean typography and spacing */

        /* CSS Variables for fonts */
        :root {
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Admin discount wrapper - clean white background */
        .discount-admin-wrapper {
            background-color: white;
            font-family: var(--font-primary);
        }

        /* Typography */
        .discount-admin-wrapper h1, .discount-admin-wrapper h2, .discount-admin-wrapper h3,
        .discount-admin-wrapper h4, .discount-admin-wrapper h5, .discount-admin-wrapper h6 {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            margin-bottom: 1rem;
        }

        .discount-admin-wrapper p, .discount-admin-wrapper span, .discount-admin-wrapper div {
            color: black;
        }

        /* Cards - clean white with black border */
        .discount-admin-wrapper .card {
            background: white;
            border: 2px solid black;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* Buttons - black and white theme */
        .discount-admin-wrapper .btn {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid black;
            transition: all 0.3s ease;
        }

        .discount-admin-wrapper .btn-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .discount-admin-wrapper .btn-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .discount-admin-wrapper .btn-outline-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .discount-admin-wrapper .btn-outline-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Form elements */
        .discount-admin-wrapper .form-control, .discount-admin-wrapper .form-select {
            font-family: var(--font-primary);
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            background-color: white;
            color: black;
        }

        .discount-admin-wrapper .form-control:focus, .discount-admin-wrapper .form-select:focus {
            border-color: black;
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
            background-color: white;
            color: black;
        }

        /* Labels */
        .discount-admin-wrapper .form-label {
            font-family: var(--font-primary);
            font-weight: 500;
            color: black;
            margin-bottom: 0.5rem;
        }

        /* Badges */
        .discount-admin-wrapper .badge {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }

        .discount-admin-wrapper .badge.bg-success {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .discount-admin-wrapper .badge.bg-primary {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        /* Text colors */
        .discount-admin-wrapper .text-muted {
            color: rgba(0, 0, 0, 0.6) !important;
        }

        .discount-admin-wrapper .text-primary {
            color: black !important;
        }
    </style>
    {% block discount_extra_css %}{% endblock %}
{% endblock %}

{% block admin_content %}
<div class="discount-admin-wrapper">
    <div class="container py-4">
        {% block discount_admin_content %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block admin_js %}
{% block discount_extra_js %}{% endblock %}
{% endblock %}
