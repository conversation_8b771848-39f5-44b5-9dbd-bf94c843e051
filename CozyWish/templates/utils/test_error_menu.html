{% extends 'base.html' %}

{% block title %}Error Testing Menu - CozyWish{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header" style="background-color: white; color: black; border: 2px solid black; border-bottom: 2px solid black;">
                    <h4 class="mb-0" style="color: black;">
                        <i class="fas fa-bug me-2"></i>Error Testing Menu
                    </h4>
                    <small style="color: black; opacity: 0.7;">Only available in DEBUG mode</small>
                </div>
                <div class="card-body">
                    <p style="color: black; opacity: 0.7;" class="mb-4">
                        Use these links to test different error pages and their logging functionality.
                        Each error will be logged using the centralized logging system.
                    </p>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <h5 class="card-title text-danger">404 Error</h5>
                                    <p class="card-text">Test page not found error</p>
                                    <a href="{% url 'utils:test_404' %}" class="btn btn-outline-danger">
                                        Test 404
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <h5 class="card-title text-danger">500 Error</h5>
                                    <p class="card-text">Test server error</p>
                                    <a href="{% url 'utils:test_500' %}" class="btn btn-outline-danger">
                                        Test 500
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h5 class="card-title text-warning">403 Error</h5>
                                    <p class="card-text">Test permission denied</p>
                                    <a href="{% url 'utils:test_403' %}" class="btn btn-outline-warning">
                                        Test 403
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <h5 class="card-title text-info">400 Error</h5>
                                    <p class="card-text">Test bad request</p>
                                    <a href="{% url 'utils:test_400' %}" class="btn btn-outline-info">
                                        Test 400
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-info-circle me-2"></i>Testing Notes:</h6>
                        <ul class="mb-0">
                            <li>Each error will be logged with request details</li>
                            <li>Error templates include user-friendly messages</li>
                            <li>Navigation links help users recover from errors</li>
                            <li>Check the console/logs to see error logging in action</li>
                        </ul>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'venues_app:home' %}" class="btn btn-secondary">
                        <i class="fas fa-home me-2"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
