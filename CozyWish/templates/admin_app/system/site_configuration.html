{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Site Configuration - CozyWish Admin{% endblock %}

{% block breadcrumbs %}
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Site Configuration</li>
  </ol>
</nav>
{% endblock %}

{% block admin_content %}
<h1 class="h3 mb-4">Site Configuration</h1>
<form method="post" novalidate>
  {% csrf_token %}
  <div class="accordion" id="configAccordion">
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingGeneral">
        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGeneral" aria-expanded="true" aria-controls="collapseGeneral">
          General
        </button>
      </h2>
      <div id="collapseGeneral" class="accordion-collapse collapse show" aria-labelledby="headingGeneral" data-bs-parent="#configAccordion">
        <div class="accordion-body">
          {{ form.site_name }}
          {{ form.site_tagline }}
          {{ form.site_description }}
        </div>
      </div>
    </div>
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingContact">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseContact" aria-expanded="false" aria-controls="collapseContact">
          Contact Info
        </button>
      </h2>
      <div id="collapseContact" class="accordion-collapse collapse" aria-labelledby="headingContact" data-bs-parent="#configAccordion">
        <div class="accordion-body">
          {{ form.contact_email }}
          {{ form.contact_phone }}
          {{ form.contact_address }}
        </div>
      </div>
    </div>
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingSocial">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSocial" aria-expanded="false" aria-controls="collapseSocial">
          Social Media
        </button>
      </h2>
      <div id="collapseSocial" class="accordion-collapse collapse" aria-labelledby="headingSocial" data-bs-parent="#configAccordion">
        <div class="accordion-body">
          {{ form.facebook_url }}
          {{ form.twitter_url }}
          {{ form.instagram_url }}
          {{ form.linkedin_url }}
        </div>
      </div>
    </div>
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingSeo">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSeo" aria-expanded="false" aria-controls="collapseSeo">
          SEO & Analytics
        </button>
      </h2>
      <div id="collapseSeo" class="accordion-collapse collapse" aria-labelledby="headingSeo" data-bs-parent="#configAccordion">
        <div class="accordion-body">
          {{ form.default_meta_title }}
          {{ form.default_meta_description }}
          {{ form.default_meta_keywords }}
          {{ form.google_analytics_id }}
          {{ form.facebook_pixel_id }}
        </div>
      </div>
    </div>
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingSettings">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSettings" aria-expanded="false" aria-controls="collapseSettings">
          Registration & Maintenance
        </button>
      </h2>
      <div id="collapseSettings" class="accordion-collapse collapse" aria-labelledby="headingSettings" data-bs-parent="#configAccordion">
        <div class="accordion-body">
          {{ form.allow_user_registration }} {{ form.allow_user_registration.label_tag }}<br>
          {{ form.require_email_verification }} {{ form.require_email_verification.label_tag }}<br>
          {{ form.maintenance_mode }} {{ form.maintenance_mode.label_tag }}<br>
          {{ form.maintenance_message }}
        </div>
      </div>
    </div>
  </div>
  <div class="mt-4">
    <button type="submit" class="btn btn-primary spinner-button">
      <i class="fas fa-save me-1"></i> Save Changes
    </button>
    <a href="{% url 'admin_app:admin_dashboard' %}" class="btn btn-outline-secondary ms-2">
      Cancel
    </a>
  </div>
</form>
{% endblock %}

{% block admin_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function () {
  var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.forEach(function (tooltipTriggerEl) { new bootstrap.Tooltip(tooltipTriggerEl); });
});
</script>
{% endblock %}
