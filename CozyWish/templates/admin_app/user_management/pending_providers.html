{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Pending Provider Approvals - CozyWish Admin{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Pending Provider Approvals</h1>
    <div class="text-muted">
        <i class="fas fa-clock me-1"></i>
        {{ pending_providers.count }} pending approval{{ pending_providers.count|pluralize }}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row section-spacing">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pending Approvals
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ pending_count|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Approved This Month
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ approved_this_month|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Rejected This Month
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ rejected_this_month|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Average Review Time
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ avg_review_time|default:"N/A" }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-stopwatch fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pending Providers List -->
<div class="card shadow">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">Pending Provider Applications</h6>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                    data-bs-toggle="dropdown" aria-expanded="false">
                Sort by
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="?sort=newest">Newest First</a></li>
                <li><a class="dropdown-item" href="?sort=oldest">Oldest First</a></li>
                <li><a class="dropdown-item" href="?sort=business_name">Business Name</a></li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        {% if pending_providers %}
            {% for provider in pending_providers %}
            <div class="card mb-3 border-left-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            {% if provider.business_logo %}
                                <img src="{{ provider.business_logo.url }}" 
                                     alt="Business Logo" class="rounded-circle" 
                                     style="width: 80px; height: 80px; object-fit: cover;">
                            {% else %}
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 80px; height: 80px;">
                                    <i class="fas fa-building fa-2x text-muted"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-1">{{ provider.business_name }}</h5>
                            <p class="mb-1 text-muted">{{ provider.user.email }}</p>
                            <p class="mb-1">
                                <strong>Contact:</strong> {{ provider.contact_first_name }} {{ provider.contact_last_name }}
                            </p>
                            <p class="mb-1">
                                <strong>Phone:</strong> {{ provider.business_phone|default:"Not provided" }}
                            </p>
                            <p class="mb-1">
                                <strong>Address:</strong> {{ provider.business_address|default:"Not provided" }}
                            </p>
                            {% if provider.business_website %}
                            <p class="mb-1">
                                <strong>Website:</strong> 
                                <a href="{{ provider.business_website }}" target="_blank">{{ provider.business_website }}</a>
                            </p>
                            {% endif %}
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="mb-2">
                                <small class="text-muted">Applied</small>
                                <br>
                                <strong>{{ provider.user.date_joined|timesince }} ago</strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Status</small>
                                <br>
                                <span class="badge bg-warning">Pending</span>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="d-grid gap-2">
                                <a href="{% url 'admin_app:provider_approval' user_id=provider.user.id %}" 
                                   class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>
                                    Review
                                </a>
                                <a href="{% url 'admin_app:user_detail' user_id=provider.user.id %}" 
                                   class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-user me-1"></i>
                                    Profile
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Business Description -->
                    {% if provider.business_description %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="border-top pt-3">
                                <h6>Business Description:</h6>
                                <p class="text-muted">{{ provider.business_description|truncatewords:30 }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Quick Actions -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="border-top pt-3">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-success" 
                                            onclick="quickApprove({{ provider.user.id }}, '{{ provider.business_name|escapejs }}')">
                                        <i class="fas fa-check me-1"></i>
                                        Quick Approve
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger" 
                                            onclick="quickReject({{ provider.user.id }}, '{{ provider.business_name|escapejs }}')">
                                        <i class="fas fa-times me-1"></i>
                                        Quick Reject
                                    </button>
                                    <a href="mailto:{{ provider.user.email }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-envelope me-1"></i>
                                        Contact
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Provider pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <img src="{% static 'img/empty_state.svg' %}" alt="No providers" width="120" class="mb-3">
                <h5 class="text-muted">No pending provider applications</h5>
                <p class="text-muted">All provider applications have been reviewed.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Quick Action Modals -->
<!-- Quick Approve Modal -->
<div class="modal fade" id="quickApproveModal" tabindex="-1" aria-labelledby="quickApproveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickApproveModalLabel">Quick Approve Provider</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to approve <strong id="approveName"></strong> as a service provider?</p>
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    The provider will be notified via email and can start creating venues.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="#" style="display: inline;" id="approveForm">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="approve">
                    <input type="hidden" name="user_id" id="approveUserId">
                    <button type="submit" class="btn btn-success">Approve Provider</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Quick Reject Modal -->
<div class="modal fade" id="quickRejectModal" tabindex="-1" aria-labelledby="quickRejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickRejectModalLabel">Quick Reject Provider</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reject <strong id="rejectName"></strong>'s provider application?</p>
                <div class="mb-3">
                    <label for="rejectReason" class="form-label">Reason for rejection:</label>
                    <textarea class="form-control" id="rejectReason" name="rejection_reason" rows="3" 
                              placeholder="Please provide a reason for rejection..."></textarea>
                </div>
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    The provider will be notified via email with the rejection reason.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="#" style="display: inline;" id="rejectForm">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="reject">
                    <input type="hidden" name="user_id" id="rejectUserId">
                    <button type="submit" class="btn btn-danger">Reject Provider</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
function quickApprove(userId, businessName) {
    document.getElementById('approveName').textContent = businessName;
    document.getElementById('approveUserId').value = userId;
    document.getElementById('approveForm').action = '{% url "admin_app:provider_approval" user_id=0 %}'.replace('0', userId);
    
    const modal = new bootstrap.Modal(document.getElementById('quickApproveModal'));
    modal.show();
}

function quickReject(userId, businessName) {
    document.getElementById('rejectName').textContent = businessName;
    document.getElementById('rejectUserId').value = userId;
    document.getElementById('rejectForm').action = '{% url "admin_app:provider_approval" user_id=0 %}'.replace('0', userId);
    
    const modal = new bootstrap.Modal(document.getElementById('quickRejectModal'));
    modal.show();
}

// Handle reject form submission to include reason
document.getElementById('rejectForm').addEventListener('submit', function(e) {
    const reason = document.getElementById('rejectReason').value;
    if (!reason.trim()) {
        e.preventDefault();
        alert('Please provide a reason for rejection.');
        return;
    }
    
    // Add reason to form
    const reasonInput = document.createElement('input');
    reasonInput.type = 'hidden';
    reasonInput.name = 'rejection_reason';
    reasonInput.value = reason;
    this.appendChild(reasonInput);
});
</script>
{% endblock %}
