{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}{{ user_detail.get_full_name|default:user_detail.email }} - User Details{% endblock %}

{% block breadcrumbs %}
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'admin_app:user_list' %}">Users</a></li>
        <li class="breadcrumb-item active">{{ user_detail.get_full_name|default:user_detail.email }}</li>
    </ol>
</nav>
{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">User Details</h1>
    <div>
        <a href="{% url 'admin_app:user_edit' user_id=user_detail.id %}" class="btn btn-primary me-2">
            <i class="fas fa-edit me-1"></i>
            Edit User
        </a>
        {% if user_detail.is_service_provider and not user_detail.service_provider_profile.is_approved %}
        <a href="{% url 'admin_app:provider_approval' user_id=user_detail.id %}" class="btn btn-warning">
            <i class="fas fa-check me-1"></i>
            Review Provider
        </a>
        {% endif %}
    </div>
</div>

<!-- User Profile Card -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3 text-center">
                        <div class="profile-picture mb-3">
                            {% if user_detail.is_customer and user_detail.customer_profile.profile_picture %}
                                <img src="{{ user_detail.customer_profile.profile_picture.url }}" 
                                     alt="Profile Picture" class="rounded-circle" 
                                     style="width: 150px; height: 150px; object-fit: cover;">
                            {% elif user_detail.is_service_provider and user_detail.service_provider_profile.business_logo %}
                                <img src="{{ user_detail.service_provider_profile.business_logo.url }}" 
                                     alt="Business Logo" class="rounded-circle" 
                                     style="width: 150px; height: 150px; object-fit: cover;">
                            {% else %}
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 150px; height: 150px;">
                                    <i class="fas fa-user fa-4x text-muted"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="text-center">
                            {% if user_detail.is_customer %}
                                <span class="badge bg-primary fs-6">Customer</span>
                            {% elif user_detail.is_service_provider %}
                                <span class="badge bg-success fs-6">Service Provider</span>
                            {% elif user_detail.is_staff %}
                                <span class="badge bg-warning fs-6">Staff</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-9">
                        <h2 class="mb-3">{{ user_detail.get_full_name|default:"No Name Provided" }}</h2>
                        <p class="text-muted mb-3">{{ user_detail.email }}</p>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Status:</strong></div>
                            <div class="col-sm-9">
                                {% if user_detail.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Member Since:</strong></div>
                            <div class="col-sm-9">{{ user_detail.date_joined|date:"F d, Y g:i A" }}</div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Last Login:</strong></div>
                            <div class="col-sm-9">
                                {% if user_detail.last_login %}
                                    {{ user_detail.last_login|date:"F d, Y g:i A" }} ({{ user_detail.last_login|timesince }} ago)
                                {% else %}
                                    Never logged in
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if user_detail.is_service_provider %}
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Provider Status:</strong></div>
                            <div class="col-sm-9">
                                {% if user_detail.service_provider_profile.is_approved %}
                                    <span class="badge bg-success">Approved</span>
                                {% else %}
                                    <span class="badge bg-warning">Pending Approval</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Information -->
<div class="row">
    <!-- Personal Information -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h5>
            </div>
            <div class="card-body">
                {% if user_detail.is_customer %}
                    {% with profile=user_detail.customer_profile %}
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>First Name:</strong></div>
                        <div class="col-sm-8">{{ profile.first_name|default:"Not provided" }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Last Name:</strong></div>
                        <div class="col-sm-8">{{ profile.last_name|default:"Not provided" }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Phone:</strong></div>
                        <div class="col-sm-8">{{ profile.phone_number|default:"Not provided" }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Gender:</strong></div>
                        <div class="col-sm-8">{{ profile.get_gender_display|default:"Not provided" }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Birth Date:</strong></div>
                        <div class="col-sm-8">
                            {% if profile.birth_month and profile.birth_year %}
                                {{ profile.get_birth_month_display }} {{ profile.birth_year }}
                            {% else %}
                                Not provided
                            {% endif %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4"><strong>Address:</strong></div>
                        <div class="col-sm-8">{{ profile.default_address|default:"Not provided" }}</div>
                    </div>
                    {% endwith %}
                {% elif user_detail.is_service_provider %}
                    {% with profile=user_detail.service_provider_profile %}
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Business Name:</strong></div>
                        <div class="col-sm-8">{{ profile.business_name|default:"Not provided" }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Contact Name:</strong></div>
                        <div class="col-sm-8">{{ profile.contact_first_name }} {{ profile.contact_last_name }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Phone:</strong></div>
                        <div class="col-sm-8">{{ profile.business_phone|default:"Not provided" }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Website:</strong></div>
                        <div class="col-sm-8">
                            {% if profile.business_website %}
                                <a href="{{ profile.business_website }}" target="_blank">{{ profile.business_website }}</a>
                            {% else %}
                                Not provided
                            {% endif %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4"><strong>Address:</strong></div>
                        <div class="col-sm-8">{{ profile.business_address|default:"Not provided" }}</div>
                    </div>
                    {% endwith %}
                {% else %}
                    <p class="text-muted">No additional profile information available.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Account Activity -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activity</h5>
            </div>
            <div class="card-body">
                {% if recent_login_history %}
                    <div class="list-group list-group-flush">
                        {% for login in recent_login_history %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">
                                    {% if login.login_successful %}
                                        <i class="fas fa-check-circle text-success me-1"></i>
                                        Successful Login
                                    {% else %}
                                        <i class="fas fa-times-circle text-danger me-1"></i>
                                        Failed Login
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    {{ login.login_time|date:"M d, Y g:i A" }}
                                    {% if login.ip_address %} - {{ login.ip_address }}{% endif %}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-history fa-3x mb-3"></i>
                        <p>No recent activity to display</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Account Statistics -->
        {% if user_detail.is_customer %}
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Customer Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-primary">{{ total_bookings|default:0 }}</h4>
                            <small class="text-muted">Total Bookings</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-success">${{ total_spent|default:0 }}</h4>
                            <small class="text-muted">Total Spent</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h4 class="text-info">{{ total_reviews|default:0 }}</h4>
                        <small class="text-muted">Reviews</small>
                    </div>
                </div>
            </div>
        </div>
        {% elif user_detail.is_service_provider %}
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Provider Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-primary">{{ total_venues|default:0 }}</h4>
                            <small class="text-muted">Venues</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-success">{{ total_bookings|default:0 }}</h4>
                            <small class="text-muted">Bookings</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h4 class="text-info">${{ total_earnings|default:0 }}</h4>
                        <small class="text-muted">Earnings</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
