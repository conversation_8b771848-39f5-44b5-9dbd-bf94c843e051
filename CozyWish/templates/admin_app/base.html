{% extends 'base.html' %}
{% load static %}

{% block title %}CozyWish Admin{% endblock %}

{% block extra_css %}
    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Admin App - Black & White Design */
        /* Matching homepage and accounts_app design with clean typography and spacing */

        /* CSS Variables for fonts */
        :root {
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
        }

        /* Admin wrapper - clean white background */
        .admin-wrapper {
            background-color: white;
            min-height: 100vh;
            padding: 2rem 0;
            font-family: var(--font-primary);
        }

        /* Typography */
        .admin-wrapper h1, .admin-wrapper h2, .admin-wrapper h3,
        .admin-wrapper h4, .admin-wrapper h5, .admin-wrapper h6 {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            margin-bottom: 1rem;
        }

        .admin-wrapper p, .admin-wrapper span, .admin-wrapper div {
            color: black;
        }

        /* Cards - clean white with black border */
        .admin-wrapper .card {
            background: white;
            border: 2px solid black;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .admin-wrapper .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        /* Buttons - black and white theme */
        .admin-wrapper .btn {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid black;
            transition: all 0.3s ease;
        }

        .admin-wrapper .btn-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-outline-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-outline-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-success {
            background-color: white;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-success:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-danger {
            background-color: white;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-danger:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-warning {
            background-color: white;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-warning:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-info {
            background-color: white;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-info:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-secondary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .admin-wrapper .btn-secondary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Form elements */
        .admin-wrapper .form-control, .admin-wrapper .form-select {
            font-family: var(--font-primary);
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            background-color: white;
            color: black;
        }

        .admin-wrapper .form-control:focus, .admin-wrapper .form-select:focus {
            border-color: black;
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
            background-color: white;
            color: black;
        }

        /* Labels */
        .admin-wrapper .form-label {
            font-family: var(--font-primary);
            font-weight: 500;
            color: black;
            margin-bottom: 0.5rem;
        }

        /* Tables */
        .admin-wrapper .table {
            color: black;
            font-family: var(--font-primary);
        }

        .admin-wrapper .table th {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            border-bottom: 2px solid black;
            background-color: white;
        }

        .admin-wrapper .table td {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* Badges */
        .admin-wrapper .badge {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }

        .admin-wrapper .badge.bg-success {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .admin-wrapper .badge.bg-primary {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .admin-wrapper .badge.bg-warning {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .admin-wrapper .badge.bg-danger {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .admin-wrapper .badge.bg-info {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .admin-wrapper .badge.bg-secondary {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        /* Pagination */
        .admin-wrapper .pagination .page-link {
            color: black;
            border: 2px solid black;
            background-color: white;
            font-family: var(--font-primary);
            font-weight: 500;
        }

        .admin-wrapper .pagination .page-link:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .admin-wrapper .pagination .page-item.active .page-link {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Text colors */
        .admin-wrapper .text-muted {
            color: rgba(0, 0, 0, 0.6) !important;
        }

        .admin-wrapper .text-primary {
            color: black !important;
        }

        .admin-wrapper .text-success {
            color: black !important;
        }

        .admin-wrapper .text-danger {
            color: black !important;
        }

        .admin-wrapper .text-warning {
            color: black !important;
        }

        .admin-wrapper .text-info {
            color: black !important;
        }

        /* Breadcrumb styling */
        .admin-wrapper .breadcrumb {
            background-color: white;
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
        }

        .admin-wrapper .breadcrumb-item a {
            color: black;
            text-decoration: none;
            font-weight: 500;
        }

        .admin-wrapper .breadcrumb-item a:hover {
            color: rgba(0, 0, 0, 0.7);
        }

        .admin-wrapper .breadcrumb-item.active {
            color: rgba(0, 0, 0, 0.6);
        }

        /* Alert styling */
        .admin-wrapper .alert {
            border: 2px solid black;
            border-radius: 0.75rem;
            background-color: white;
            color: black;
        }

        .admin-wrapper .alert-success {
            border-color: black;
        }

        .admin-wrapper .alert-info {
            border-color: black;
        }

        .admin-wrapper .alert-warning {
            border-color: black;
        }

        .admin-wrapper .alert-danger {
            border-color: black;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .admin-wrapper {
                padding: 1rem 0;
            }

            .admin-wrapper .card {
                margin-bottom: 1.5rem;
            }
        }
    </style>
    {% block admin_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="admin-wrapper">
    <div class="container py-4">
        <div class="mx-auto" style="max-width: 960px;">
            {% if messages %}
            <div class="mb-3">
                {% for message in messages %}
                <div class="alert alert-info mb-2" role="alert">{{ message }}</div>
                {% endfor %}
            </div>
            {% endif %}
            {% block breadcrumbs %}{% endblock %}
            <div class="card">
                <div class="card-body p-4">
                    {% block admin_content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% block admin_js %}{% endblock %}
{% endblock %}
