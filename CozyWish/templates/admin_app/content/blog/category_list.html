{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Blog Categories - Admin Panel{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Blog Categories</h1>
    <div>
        <a href="{% url 'admin_app:blog_post_list' %}" class="btn btn-outline-primary me-2">
            <i class="fas fa-blog me-2"></i>Manage Posts
        </a>
        <a href="{% url 'admin_app:blog_category_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create New Category
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-8">
                <input type="text" name="search" class="form-control" placeholder="Search categories..." 
                       value="{{ request.GET.search }}">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-outline-primary">Search</button>
                <a href="{% url 'admin_app:blog_category_list' %}" class="btn btn-outline-secondary">Clear</a>
            </div>
        </form>
    </div>
</div>

<!-- Categories List -->
<div class="card">
    <div class="card-body">
        {% if categories %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Slug</th>
                            <th>Description</th>
                            <th>Posts</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                        <tr>
                            <td>
                                <strong>{{ category.name }}</strong>
                            </td>
                            <td>
                                <code>{{ category.slug }}</code>
                            </td>
                            <td>
                                {% if category.description %}
                                    <small>{{ category.description|truncatechars:60 }}</small>
                                {% else %}
                                    <small class="text-muted">No description</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ category.blogpost_set.count }}</span>
                            </td>
                            <td>
                                <small>{{ category.created_at|date:"M d, Y" }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" 
                                            onclick="editCategory({{ category.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteCategory({{ category.id }}, '{{ category.name }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No blog categories found</h5>
                <p class="text-muted">Create your first blog category to organize your posts.</p>
                <a href="{% url 'admin_app:blog_category_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New Category
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
function editCategory(id) {
    // Implement edit functionality
    window.location.href = `/admin-panel/content/blog/categories/${id}/edit/`;
}

function deleteCategory(id, name) {
    if (confirm(`Are you sure you want to delete the category "${name}"?`)) {
        // Implement delete functionality
        window.location.href = `/admin-panel/content/blog/categories/${id}/delete/`;
    }
}
</script>
{% endblock %}
