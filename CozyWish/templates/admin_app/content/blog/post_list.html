{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Blog Posts - Admin Panel{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Blog Posts</h1>
    <div>
        <a href="{% url 'admin_app:blog_category_list' %}" class="btn btn-outline-primary me-2">
            <i class="fas fa-tags me-2"></i>Manage Categories
        </a>
        <a href="{% url 'admin_app:blog_post_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create New Post
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <input type="text" name="search" class="form-control" placeholder="Search posts..." 
                       value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <select name="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>Draft</option>
                    <option value="published" {% if request.GET.status == 'published' %}selected{% endif %}>Published</option>
                    <option value="archived" {% if request.GET.status == 'archived' %}selected{% endif %}>Archived</option>
                </select>
            </div>
            <div class="col-md-3">
                <select name="category" class="form-select">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                        <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-12">
                <button type="submit" class="btn btn-outline-primary">Filter</button>
                <a href="{% url 'admin_app:blog_post_list' %}" class="btn btn-outline-secondary">Clear</a>
            </div>
        </form>
    </div>
</div>

<!-- Posts List -->
<div class="card">
    <div class="card-body">
        {% if posts %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Author</th>
                            <th>Published</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for post in posts %}
                        <tr>
                            <td>
                                <strong>{{ post.title }}</strong><br>
                                <small class="text-muted">{{ post.slug }}</small>
                                {% if post.excerpt %}
                                    <br><small class="text-muted">{{ post.excerpt|truncatechars:60 }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if post.category %}
                                    <span class="badge bg-info">{{ post.category.name }}</span>
                                {% else %}
                                    <span class="text-muted">No category</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if post.status == 'published' %}
                                    <span class="badge bg-success">Published</span>
                                {% elif post.status == 'draft' %}
                                    <span class="badge bg-warning">Draft</span>
                                {% else %}
                                    <span class="badge bg-secondary">Archived</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ post.author.get_full_name|default:post.author.email }}</small>
                            </td>
                            <td>
                                {% if post.published_at %}
                                    <small>{{ post.published_at|date:"M d, Y" }}</small>
                                {% else %}
                                    <small class="text-muted">Not published</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'admin_app:blog_post_edit' slug=post.slug %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="confirmDelete('{{ post.title }}', '{% url 'admin_app:blog_post_delete' slug=post.slug %}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No blog posts found</h5>
                <p class="text-muted">Create your first blog post to get started.</p>
                <a href="{% url 'admin_app:blog_post_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New Post
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
function confirmDelete(title, url) {
    if (confirm(`Are you sure you want to delete the blog post "${title}"?`)) {
        window.location.href = url;
    }
}
</script>
{% endblock %}
