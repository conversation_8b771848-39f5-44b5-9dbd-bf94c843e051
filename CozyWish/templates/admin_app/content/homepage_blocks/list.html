{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Homepage Blocks - Admin Panel{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Homepage Blocks</h1>
    <a href="{% url 'admin_app:homepage_block_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Create New Block
    </a>
</div>

<!-- Blocks List -->
<div class="card">
    <div class="card-body">
        {% if blocks %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Block Type</th>
                            <th>Title</th>
                            <th>Status</th>
                            <th>Order</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for block in blocks %}
                        <tr>
                            <td>
                                <span class="badge bg-info">{{ block.get_block_type_display }}</span>
                            </td>
                            <td>
                                <strong>{{ block.title }}</strong>
                                {% if block.subtitle %}
                                    <br><small class="text-muted">{{ block.subtitle|truncatechars:50 }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if block.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ block.display_order }}</span>
                            </td>
                            <td>
                                <small>{{ block.updated_at|date:"M d, Y H:i" }}</small>
                                {% if block.updated_by %}
                                    <br><small class="text-muted">by {{ block.updated_by.get_full_name|default:block.updated_by.email }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" 
                                            onclick="editBlock({{ block.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteBlock({{ block.id }}, '{{ block.title }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-th-large fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No homepage blocks found</h5>
                <p class="text-muted">Create homepage blocks to customize your site's homepage.</p>
                <a href="{% url 'admin_app:homepage_block_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New Block
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Block Types Info -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">Available Block Types</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <ul class="list-unstyled">
                    <li><strong>Hero Section:</strong> Main banner with call-to-action</li>
                    <li><strong>How It Works:</strong> Step-by-step process explanation</li>
                    <li><strong>Top Deals:</strong> Featured discounts and offers</li>
                    <li><strong>Testimonials:</strong> Customer reviews and feedback</li>
                </ul>
            </div>
            <div class="col-md-6">
                <ul class="list-unstyled">
                    <li><strong>Features:</strong> Platform features and benefits</li>
                    <li><strong>Call to Action:</strong> Conversion-focused section</li>
                    <li><strong>Custom Block:</strong> Flexible content block</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
function editBlock(id) {
    // Implement edit functionality
    window.location.href = `/admin-panel/content/homepage-blocks/${id}/edit/`;
}

function deleteBlock(id, title) {
    if (confirm(`Are you sure you want to delete the block "${title}"?`)) {
        // Implement delete functionality
        window.location.href = `/admin-panel/content/homepage-blocks/${id}/delete/`;
    }
}
</script>
{% endblock %}
