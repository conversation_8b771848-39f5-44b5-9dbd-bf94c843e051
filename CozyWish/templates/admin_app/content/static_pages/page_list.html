{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Static Pages - CozyWish Admin{% endblock %}

{% block admin_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Static Pages</h1>
    <a href="{% url 'admin_app:static_page_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        Create New Page
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Pages
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_pages|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Published
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ published_pages|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Draft
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ draft_pages|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-edit fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Featured
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ featured_pages|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-star fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request.GET.search }}" placeholder="Page title, content...">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="published" {% if request.GET.status == 'published' %}selected{% endif %}>Published</option>
                    <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>Draft</option>
                    <option value="archived" {% if request.GET.status == 'archived' %}selected{% endif %}>Archived</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="featured" class="form-label">Featured</label>
                <select class="form-select" id="featured" name="featured">
                    <option value="">All Pages</option>
                    <option value="yes" {% if request.GET.featured == 'yes' %}selected{% endif %}>Featured Only</option>
                    <option value="no" {% if request.GET.featured == 'no' %}selected{% endif %}>Not Featured</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="sort" class="form-label">Sort By</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="title" {% if request.GET.sort == 'title' %}selected{% endif %}>Title</option>
                    <option value="-updated_at" {% if request.GET.sort == '-updated_at' %}selected{% endif %}>Recently Updated</option>
                    <option value="-created_at" {% if request.GET.sort == '-created_at' %}selected{% endif %}>Recently Created</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>
                    Filter
                </button>
                <a href="{% url 'admin_app:static_page_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Pages Table -->
<div class="card shadow">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">Static Pages</h6>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                    data-bs-toggle="dropdown" aria-expanded="false">
                Bulk Actions
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="bulkAction('publish')">Publish Selected</a></li>
                <li><a class="dropdown-item" href="#" onclick="bulkAction('unpublish')">Unpublish Selected</a></li>
                <li><a class="dropdown-item" href="#" onclick="bulkAction('archive')">Archive Selected</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">Delete Selected</a></li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        {% if pages %}
            <div class="table-responsive">
                <table class="table table-hover table-sticky">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>Page</th>
                            <th>Status</th>
                            <th>Featured</th>
                            <th>Last Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for page in pages %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input page-checkbox" 
                                       value="{{ page.id }}">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if page.featured_image %}
                                        <img src="{{ page.featured_image.url }}" 
                                             class="rounded me-3" width="50" height="50" 
                                             style="object-fit: cover;">
                                    {% else %}
                                        <div class="rounded bg-light d-flex align-items-center justify-content-center me-3" 
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-file-alt text-muted"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <div class="fw-bold">{{ page.title }}</div>
                                        <small class="text-muted">/{{ page.slug }}</small>
                                        {% if page.meta_description %}
                                        <br><small class="text-muted">{{ page.meta_description|truncatechars:60 }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if page.status == 'published' %}
                                    <span class="badge bg-success">Published</span>
                                {% elif page.status == 'draft' %}
                                    <span class="badge bg-warning">Draft</span>
                                {% elif page.status == 'archived' %}
                                    <span class="badge bg-secondary">Archived</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if page.is_featured %}
                                    <i class="fas fa-star text-warning" title="Featured"></i>
                                {% else %}
                                    <i class="far fa-star text-muted" title="Not Featured"></i>
                                {% endif %}
                            </td>
                            <td>
                                <small>
                                    {{ page.updated_at|date:"M d, Y g:i A" }}
                                    {% if page.updated_by %}
                                    <br>by {{ page.updated_by.email }}
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    {% if page.status == 'published' %}
                                    <a href="/{{ page.slug }}/" target="_blank"
                                       class="btn btn-sm btn-outline-info" title="View Page" data-bs-toggle="tooltip">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    {% endif %}
                                    <a href="{% url 'admin_app:static_page_edit' slug=page.slug %}"
                                       class="btn btn-sm btn-outline-primary" title="Edit" data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deletePage('{{ page.id }}', '{{ page.title|escapejs }}')" title="Delete" data-bs-toggle="tooltip">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.featured %}&featured={{ request.GET.featured }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.featured %}&featured={{ request.GET.featured }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.featured %}&featured={{ request.GET.featured }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.featured %}&featured={{ request.GET.featured }}{% endif %}{% if request.GET.sort %}&sort={{ request.GET.sort }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <img src="{% static 'img/empty_state.svg' %}" alt="No pages" width="120" class="mb-3">
                <h5 class="text-muted">No static pages found</h5>
                <p class="text-muted">Create your first static page to get started.</p>
                <a href="{% url 'admin_app:static_page_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Create New Page
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Delete Static Page</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the page <strong id="deletePageTitle"></strong>?</p>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This action cannot be undone. The page will be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="#" style="display: inline;" id="deleteForm">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete Page</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block admin_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const pageCheckboxes = document.querySelectorAll('.page-checkbox');

    selectAllCheckbox.addEventListener('change', function() {
        pageCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Update select all when individual checkboxes change
    pageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.page-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === pageCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < pageCheckboxes.length;
        });
    });
});

function deletePage(pageId, pageTitle) {
    document.getElementById('deletePageTitle').textContent = pageTitle;
    document.getElementById('deleteForm').action = '{% url "admin_app:static_page_delete" slug="PLACEHOLDER" %}'.replace('PLACEHOLDER', pageId);
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function bulkAction(action) {
    const selectedPages = Array.from(document.querySelectorAll('.page-checkbox:checked')).map(cb => cb.value);
    
    if (selectedPages.length === 0) {
        alert('Please select at least one page.');
        return;
    }
    
    const actionText = action.charAt(0).toUpperCase() + action.slice(1);
    if (confirm(`Are you sure you want to ${action} ${selectedPages.length} selected page(s)?`)) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'post';
        form.action = '{% url "admin_app:static_page_list" %}';
        
        // Add CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        // Add action
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'bulk_action';
        actionInput.value = action;
        form.appendChild(actionInput);
        
        // Add selected pages
        selectedPages.forEach(pageId => {
            const pageInput = document.createElement('input');
            pageInput.type = 'hidden';
            pageInput.name = 'selected_pages';
            pageInput.value = pageId;
            form.appendChild(pageInput);
        });
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
