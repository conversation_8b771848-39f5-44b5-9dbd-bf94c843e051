{% extends 'admin_app/base.html' %}
{% load static %}

{% block title %}Admin Login{% endblock %}

{% block admin_content %}
<h1 class="h4 text-center mb-4">Admin Login</h1>
<form method="post" novalidate>
    {% csrf_token %}
    {% if form.non_field_errors %}
    <div class="alert alert-danger mb-3">
        {% for error in form.non_field_errors %}{{ error }}{% endfor %}
    </div>
    {% endif %}
    <div class="mb-3">
        <label for="id_email" class="form-label">Email</label>
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
            <input type="email" name="email" required class="form-control" id="id_email" placeholder="Email">
        </div>
    </div>
    <div class="mb-3">
        <label for="id_password" class="form-label">Password</label>
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-lock"></i></span>
            <input type="password" name="password" required class="form-control" id="id_password" placeholder="Password">
        </div>
    </div>
    <div class="d-grid mb-3">
        <button class="btn btn-primary" type="submit">Sign In</button>
    </div>
</form>
<div class="text-center">
    <a href="{% url 'home' %}">Back to site</a>
</div>
{% endblock %}
