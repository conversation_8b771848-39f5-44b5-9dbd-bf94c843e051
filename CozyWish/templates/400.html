{% extends 'base.html' %}
{% load static %}

{% block title %}Bad Request - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        min-height: 60vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 2rem 0;
    }
    
    .error-content {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .error-code {
        font-size: 8rem;
        font-weight: bold;
        color: #fd7e14;
        line-height: 1;
        margin-bottom: 1rem;
    }
    
    .error-title {
        font-size: 2rem;
        font-weight: 600;
        color: #212529;
        margin-bottom: 1rem;
    }
    
    .error-message {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-primary {
        background-color: #000;
        border-color: #000;
        padding: 0.75rem 2rem;
        font-weight: 500;
    }
    
    .btn-primary:hover {
        background-color: #333;
        border-color: #333;
    }
    
    .btn-outline-secondary {
        color: #6c757d;
        border-color: #6c757d;
        padding: 0.75rem 2rem;
        font-weight: 500;
    }
    
    .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
    }
    
    .error-icon {
        font-size: 4rem;
        color: #fd7e14;
        margin-bottom: 1rem;
    }
    
    .request-info {
        background-color: #fff4e6;
        border: 1px solid #ffd6a5;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 2rem;
        text-align: left;
    }
    
    .request-info h6 {
        color: #b45309;
        margin-bottom: 0.5rem;
    }
    
    .request-info p {
        color: #b45309;
        margin-bottom: 0;
        font-size: 0.9rem;
    }
    
    @media (max-width: 768px) {
        .error-code {
            font-size: 6rem;
        }
        
        .error-title {
            font-size: 1.5rem;
        }
        
        .error-actions {
            flex-direction: column;
            align-items: center;
        }
        
        .btn {
            width: 100%;
            max-width: 300px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-exclamation-circle"></i>
        </div>
        
        <div class="error-code">400</div>
        
        <h1 class="error-title">Bad Request</h1>
        
        <p class="error-message">
            Your request could not be processed due to invalid or malformed data. 
            This usually happens when required information is missing or incorrectly formatted.
        </p>
        
        <div class="error-actions">
            <a href="{% url 'home' %}" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>Go to Homepage
            </a>
            <button onclick="window.history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Go Back
            </button>
        </div>
        
        <div class="request-info">
            <h6><i class="fas fa-info-circle me-2"></i>What can you do?</h6>
            <p>
                Please check that all required fields are filled out correctly and try again. 
                If you're submitting a form, make sure all information is valid and complete. 
                You can also try refreshing the page and starting over.
            </p>
        </div>
        
        {% if request_path %}
        <div class="mt-3">
            <small class="text-muted">
                Requested path: <code>{{ request_path }}</code>
            </small>
        </div>
        {% endif %}
        
        <div class="mt-3">
            <small class="text-muted">
                Still having trouble? Contact us at 
                <a href="mailto:{{ support_email|default:'<EMAIL>' }}" class="text-decoration-none">
                    {{ support_email|default:'<EMAIL>' }}
                </a>
            </small>
        </div>
    </div>
</div>
{% endblock %}
