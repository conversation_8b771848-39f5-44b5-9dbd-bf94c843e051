{% extends 'base.html' %}

{% block title %}Manage Services - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: black;
        font-family: var(--font-primary);
    }

    /* Card styling */
    .venues-wrapper .card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .venues-wrapper .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .venues-wrapper .card-body {
        padding: 2rem;
    }

    .venues-wrapper .card-title {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
    }

    .venues-wrapper .card-text {
        color: black;
        opacity: 0.8;
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid black;
    }

    .venues-wrapper .btn-primary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-primary:hover {
        background: #f8f9fa;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary:hover {
        background: #f8f9fa;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-secondary {
        background: white;
        color: black;
        border-color: black;
        opacity: 0.6;
    }

    .venues-wrapper .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    /* Dropdown styling */
    .venues-wrapper .dropdown-menu {
        background: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .venues-wrapper .dropdown-item {
        color: black;
        padding: 0.75rem 1rem;
    }

    .venues-wrapper .dropdown-item:hover {
        background: black;
        color: white;
    }

    .venues-wrapper .dropdown-item.text-danger {
        color: black !important;
        font-weight: 500;
    }

    /* Badge styling */
    .venues-wrapper .badge {
        background: black !important;
        color: white;
        border: 1px solid black;
        font-weight: 500;
        padding: 0.5rem 1rem;
    }

    /* Border styling */
    .venues-wrapper .border-end {
        border-right: 2px solid black !important;
    }

    /* Text styling */
    .venues-wrapper .text-muted {
        color: black !important;
        opacity: 0.7;
    }

    .venues-wrapper .text-primary {
        color: black !important;
        font-weight: 600;
    }

    .venues-wrapper .text-success {
        color: black !important;
        font-weight: 600;
    }

    .venues-wrapper .text-danger {
        color: black !important;
        font-weight: 600;
    }

    /* Empty state styling */
    .empty-state {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 4rem 2rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .empty-state i {
        color: black;
        opacity: 0.7;
        margin-bottom: 2rem;
    }

    /* Guidelines card */
    .guidelines-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .guidelines-card .card-body {
        padding: 2.5rem;
    }

    .guidelines-card .card-title {
        margin-bottom: 2rem;
    }

    .guidelines-card .list-unstyled li {
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .guidelines-card .list-unstyled i {
        margin-right: 0.75rem;
        color: black;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <div class="row">
            <div class="col-12">

                <!-- Header -->
                <div class="d-flex align-items-center justify-content-between mb-4">
                    <div>
                        <h2>Manage Services</h2>
                        <p class="text-muted mb-0">{{ venue.venue_name }} - {{ services.count }}/{{ max_services }} services</p>
                    </div>
                    <div>
                        {% if can_add_service %}
                            <a href="{% url 'venues_app:service_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Service
                            </a>
                        {% else %}
                            <button class="btn btn-secondary" disabled title="Maximum 7 services allowed">
                                <i class="fas fa-plus me-2"></i>Add Service (Limit Reached)
                            </button>
                        {% endif %}
                    </div>
                </div>

                <!-- Services List -->
                {% if services %}
                    <div class="row">
                        {% for service in services %}
                            <div class="col-lg-6 col-xl-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h5 class="card-title mb-0">{{ service.service_title }}</h5>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{% url 'venues_app:service_edit' service.pk %}">
                                                            <i class="fas fa-edit me-2"></i>Edit
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item text-danger" href="{% url 'venues_app:service_delete' service.pk %}">
                                                            <i class="fas fa-trash me-2"></i>Delete
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>

                                        <p class="card-text text-muted small mb-3">{{ service.short_description|truncatewords:20 }}</p>

                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="border-end">
                                                    <div class="fw-bold text-primary">{{ service.price_display }}</div>
                                                    <small class="text-muted">Price</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="fw-bold text-success">{{ service.duration_display }}</div>
                                                <small class="text-muted">Duration</small>
                                            </div>
                                        </div>

                                        <div class="mt-3">
                                            {% if service.is_active %}
                                                <span class="badge">Active</span>
                                            {% else %}
                                                <span class="badge">Inactive</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <!-- Empty State -->
                    <div class="empty-state">
                        <div class="mb-4">
                            <i class="fas fa-spa fa-4x"></i>
                        </div>
                        <h4 class="mb-3">No Services Yet</h4>
                        <p class="text-muted mb-4">Start by adding your first service to attract customers.</p>
                        {% if can_add_service %}
                            <a href="{% url 'venues_app:service_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Your First Service
                            </a>
                        {% endif %}
                    </div>
                {% endif %}

                <!-- Guidelines Card -->
                <div class="row mt-5">
                    <div class="col-lg-8 mx-auto">
                        <div class="guidelines-card card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-lightbulb me-2"></i>Service Management Tips
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled mb-0">
                                            <li>
                                                <i class="fas fa-check-circle"></i>
                                                Keep service descriptions clear and detailed
                                            </li>
                                            <li>
                                                <i class="fas fa-check-circle"></i>
                                                Set competitive and accurate pricing
                                            </li>
                                            <li>
                                                <i class="fas fa-check-circle"></i>
                                                Use realistic duration estimates
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled mb-0">
                                            <li>
                                                <i class="fas fa-info-circle"></i>
                                                Maximum 7 services per venue
                                            </li>
                                            <li>
                                                <i class="fas fa-info-circle"></i>
                                                Variable pricing supported (min-max range)
                                            </li>
                                            <li>
                                                <i class="fas fa-info-circle"></i>
                                                Inactive services won't be bookable
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Back to Venue Button -->
                <div class="text-center mt-4">
                    <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Venue Management
                    </a>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}
