{% extends 'base.html' %}

{% block title %}Create Your Venue - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    /* Form sections */
    .form-section {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
        font-size: 1.5rem;
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: black;
        font-family: var(--font-primary);
    }

    /* Form styling */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .venues-wrapper .form-label {
        font-weight: 500;
        color: black;
        margin-bottom: 0.5rem;
        font-family: var(--font-primary);
        display: block;
    }

    .venues-wrapper .form-control, .venues-wrapper .form-select {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--font-primary);
        background: white;
        color: black;
        transition: all 0.3s ease;
        width: 100%;
    }

    .venues-wrapper .form-control:focus, .venues-wrapper .form-select:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
        background: white;
        color: black;
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid black;
    }

    .venues-wrapper .btn-primary {
        background: black;
        color: white;
        border-color: black;
    }

    .venues-wrapper .btn-primary:hover {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary:hover {
        background: black;
        color: white;
        border-color: black;
    }

    .btn-create {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
    }

    /* Error and help text styling */
    .venues-wrapper .text-danger {
        color: black !important;
        font-weight: 500;
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }

    .venues-wrapper .help-text {
        color: black;
        opacity: 0.7;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .venues-wrapper .required {
        color: black;
        font-weight: 600;
    }

    /* Header styling */
    .venues-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
    }

    .venues-header h2 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .venues-header p {
        opacity: 0.8;
        margin-bottom: 0;
    }

    /* Checkbox and select styling */
    .venues-wrapper input[type="checkbox"] {
        border: 2px solid black;
        border-radius: 0.25rem;
    }

    .venues-wrapper input[type="checkbox"]:checked {
        background-color: black;
        border-color: black;
    }

    /* Image Upload Cards Styling */
    .image-upload-card {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 1rem;
        background: white;
        transition: all 0.3s ease;
        height: 100%;
        min-height: 250px;
        display: flex;
        flex-direction: column;
    }

    .image-upload-card:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .image-preview-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 150px;
        border: 2px dashed black;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
        position: relative;
        overflow: hidden;
    }

    .image-preview {
        max-width: 100%;
        max-height: 100%;
        object-fit: cover;
        border-radius: 0.25rem;
    }

    .placeholder-content {
        text-align: center;
        color: black;
        opacity: 0.6;
    }

    .placeholder-content i {
        opacity: 0.5;
    }

    .image-upload-controls {
        margin-top: auto;
    }

    .image-upload-controls .form-control {
        margin-bottom: 0.5rem;
    }

    .remove-image {
        width: 100%;
        margin-top: 0.5rem;
    }

    /* Hide file input when image is uploaded */
    .image-upload-card.has-image .form-control[type="file"] {
        display: none;
    }

    .image-upload-card.has-image .image-preview-container {
        border-style: solid;
    }

    .image-upload-card.has-image .placeholder-content {
        display: none;
    }

    /* Main image card styling */
    .main-image-card {
        border-color: #ffc107 !important;
        border-width: 3px !important;
    }

    .main-image-card:hover {
        box-shadow: 0 5px 15px rgba(255, 193, 7, 0.2) !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="venues-header d-flex align-items-center">
                    <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2>Create Your Venue</h2>
                        <p>Set up your business venue to start attracting customers</p>
                    </div>
                </div>

                <form method="post">
                    {% csrf_token %}

                    <!-- Form-wide errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger mb-4">
                            <h6 class="mb-2">Please correct the following errors:</h6>
                            {% for error in form.non_field_errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h3>

                        <div class="form-group">
                            <label class="form-label">
                                Venue Name <span class="required">*</span>
                            </label>
                            {% if form.venue_name.errors %}
                                {{ form.venue_name|add_class:"form-control is-invalid" }}
                                <div class="invalid-feedback">{{ form.venue_name.errors.0 }}</div>
                            {% else %}
                                {{ form.venue_name|add_class:"form-control" }}
                            {% endif %}
                            {% if form.venue_name.help_text %}
                                <div class="form-text">{{ form.venue_name.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                Short Description <span class="required">*</span>
                            </label>
                            {% if form.short_description.errors %}
                                {{ form.short_description|add_class:"form-control is-invalid" }}
                                <div class="invalid-feedback">{{ form.short_description.errors.0 }}</div>
                            {% else %}
                                {{ form.short_description|add_class:"form-control" }}
                            {% endif %}
                            {% if form.short_description.help_text %}
                                <div class="form-text">{{ form.short_description.help_text }}</div>
                            {% endif %}
                            <div class="form-text">Briefly describe your venue and the services you offer (minimum 10 characters, maximum 500 characters)</div>
                        </div>
                    </div>



                    <!-- Location Information -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-map-marker-alt me-2"></i>Location
                        </h3>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        State <span class="required">*</span>
                                    </label>
                                    {% if form.state.errors %}
                                        {{ form.state|add_class:"form-control is-invalid" }}
                                        <div class="invalid-feedback">{{ form.state.errors.0 }}</div>
                                    {% else %}
                                        {{ form.state|add_class:"form-control" }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        County <span class="required">*</span>
                                    </label>
                                    {% if form.county.errors %}
                                        {{ form.county|add_class:"form-control is-invalid" }}
                                        <div class="invalid-feedback">{{ form.county.errors.0 }}</div>
                                    {% else %}
                                        {{ form.county|add_class:"form-control" }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        City <span class="required">*</span>
                                    </label>
                                    {% if form.city.errors %}
                                        {{ form.city|add_class:"form-control is-invalid" }}
                                        <div class="invalid-feedback">{{ form.city.errors.0 }}</div>
                                    {% else %}
                                        {{ form.city|add_class:"form-control" }}
                                    {% endif %}
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        Street Number <span class="required">*</span>
                                    </label>
                                    {% if form.street_number.errors %}
                                        {{ form.street_number|add_class:"form-control is-invalid" }}
                                        <div class="invalid-feedback">{{ form.street_number.errors.0 }}</div>
                                    {% else %}
                                        {{ form.street_number|add_class:"form-control" }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="form-group">
                                    <label class="form-label">
                                        Street Name <span class="required">*</span>
                                    </label>
                                    {% if form.street_name.errors %}
                                        {{ form.street_name|add_class:"form-control is-invalid" }}
                                        <div class="invalid-feedback">{{ form.street_name.errors.0 }}</div>
                                    {% else %}
                                        {{ form.street_name|add_class:"form-control" }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Venue Status Selection -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-check-circle me-2"></i>Venue Status
                        </h3>

                        <div class="form-group">
                            <label class="form-label">
                                Choose how to save your venue <span class="required">*</span>
                            </label>
                            <div class="form-text mb-3">You can save as draft to edit later, or submit for admin approval to make it visible to customers</div>

                            {% if form.venue_status.errors %}
                                <div class="invalid-feedback d-block mb-2">{{ form.venue_status.errors.0 }}</div>
                            {% endif %}

                            <div class="venue-status-options">
                                {% for choice in form.venue_status %}
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ choice.tag }}
                                        <label class="form-check-label ms-2" for="{{ choice.id_for_label }}">
                                            <strong>{{ choice.choice_label }}</strong>
                                            {% if choice.data.value == 'draft' %}
                                                <div class="text-muted small mt-1">
                                                    Save your venue privately. You can edit all details (description, images, categories, operating hours) and submit for approval when ready.
                                                </div>
                                            {% else %}
                                                <div class="text-muted small mt-1">
                                                    Submit your venue for admin review. Once approved, it will be visible to customers. You can still edit details after approval.
                                                </div>
                                            {% endif %}
                                        </label>
                                    </div>
                                {% endfor %}
                            </div>

                            {% if form.venue_status.help_text %}
                                <div class="form-text">{{ form.venue_status.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-create">
                            <i class="fas fa-save me-2"></i>Create Venue
                        </button>
                        <div class="mt-3">
                            <small class="text-muted">
                                After creating your venue, you can add more details like description, images, categories, and operating hours from your venue management page.
                            </small>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Simple form enhancement - no complex functionality needed for simplified form
    console.log('Simplified venue creation form loaded');
});
</script>
{% endblock %}
