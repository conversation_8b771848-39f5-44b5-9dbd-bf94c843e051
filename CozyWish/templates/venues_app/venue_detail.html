{% extends 'base.html' %}
{% load discount_tags i18n %}

{% block title %}{{ venue.venue_name }} - {{ venue.service_provider.business_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Venue Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_search' %}">Venues</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ venue.venue_name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Venue Information -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <!-- Venue Images Carousel -->
            <div id="venueCarousel" class="carousel slide mb-4" data-bs-ride="carousel">
                <div class="carousel-inner rounded overflow-hidden ratio ratio-21x9">
                    {% if primary_image %}
                    <div class="carousel-item active">
                        <img src="{{ primary_image.image.url }}" alt="{{ primary_image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% elif venue.main_image %}
                    <div class="carousel-item active">
                        <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% endif %}

                    {% for image in gallery_images %}
                    <div class="carousel-item {% if not primary_image and not venue.main_image and forloop.first %}active{% endif %}">
                        <img src="{{ image.image.url }}" alt="{{ image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% empty %}
                    {% if not primary_image and not venue.main_image %}
                    <div class="carousel-item active">
                        <div class="bg-light d-flex align-items-center justify-content-center h-100">
                            <i class="fas fa-image fa-4x text-muted"></i>
                            <p class="text-muted mt-3">No images available</p>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>

                <!-- Carousel controls (only show if there are multiple images) -->
                {% if images.count > 1 %}
                <button class="carousel-control-prev" type="button" data-bs-target="#venueCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#venueCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>

                <!-- Carousel indicators -->
                <div class="carousel-indicators">
                    {% if primary_image or venue.main_image %}
                    <button type="button" data-bs-target="#venueCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Primary image"></button>
                    {% endif %}
                    {% for image in gallery_images %}
                    <button type="button" data-bs-target="#venueCarousel" data-bs-slide-to="{% if primary_image or venue.main_image %}{{ forloop.counter }}{% else %}{{ forloop.counter0 }}{% endif %}" aria-label="Image {{ forloop.counter }}"></button>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- Venue Description -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h1 class="card-title h2 mb-3">{{ venue.venue_name }}</h1>
                    <p class="text-muted mb-3">{{ venue.service_provider.business_name }}</p>

                    <!-- Categories -->
                    {% if venue.categories.all %}
                    <div class="mb-3">
                        {% for category in venue.categories.all %}
                            <span class="badge bg-light text-dark me-2 mb-2">{{ category.category_name }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Price Range -->
                    {% if price_range %}
                    <div class="mb-3">
                        <span class="badge bg-primary fs-6 px-3 py-2">{{ price_range }}</span>
                    </div>
                    {% endif %}

                    <p class="card-text">{{ venue.short_description }}</p>

                    <!-- Tags -->
                    {% if venue.tags %}
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-tags me-1"></i>
                            {{ venue.tags }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Discount Summary -->
            {% venue_discount_summary venue %}

            <div class="accordion mb-4" id="venueInfoAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingInfo">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseInfo" aria-expanded="true" aria-controls="collapseInfo">
                            <i class="fas fa-info-circle me-2"></i>Venue Information
                        </button>
                    </h2>
                    <div id="collapseInfo" class="accordion-collapse collapse show" aria-labelledby="headingInfo">
                        <div class="accordion-body">
                            <div class="mb-3">
                                <h6><i class="fas fa-map-marker-alt me-2"></i>Location</h6>
                                <p class="mb-1">{{ venue.full_address }}</p>
                            </div>
                            <!-- Structured Operating Hours -->
                            {% if venue.operating_hours_set.all %}
                            <div class="mb-3">
                                <h6><i class="fas fa-clock me-2"></i>Operating Hours</h6>
                                <div class="row">
                                    {% for hours in venue.operating_hours_set.all %}
                                    <div class="col-md-6 mb-1">
                                        <span class="fw-bold">{{ hours.get_day_display }}:</span>
                                        {% if hours.is_closed %}
                                            <span class="text-muted">Closed</span>
                                        {% else %}
                                            <span>{{ hours.opening|time:"g:i A" }} - {{ hours.closing|time:"g:i A" }}</span>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% elif venue.operating_hours %}
                            <div class="mb-3">
                                <h6><i class="fas fa-clock me-2"></i>Operating Hours</h6>
                                <p class="mb-1">{{ venue.operating_hours|linebreaks }}</p>
                            </div>
                            {% endif %}
                            {% if venue.opening_notes %}
                            <div class="mb-3">
                                <h6><i class="fas fa-exclamation-circle me-2"></i>Special Notes</h6>
                                <p class="mb-1">{{ venue.opening_notes|linebreaks }}</p>
                            </div>
                            {% endif %}
                            <div class="mb-3">
                                <h6><i class="fas fa-phone me-2"></i>Contact</h6>
                                {% if venue.phone %}
                                    <p class="mb-1"><i class="fas fa-phone me-2"></i>{{ venue.phone }}</p>
                                {% endif %}
                                {% if venue.email %}
                                    <p class="mb-1"><i class="fas fa-envelope me-2"></i>{{ venue.email }}</p>
                                {% endif %}
                                {% if venue.website_url %}
                                    <p class="mb-1"><i class="fas fa-globe me-2"></i><a href="{{ venue.website_url }}" target="_blank" rel="noopener">Visit Website</a></p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Amenities Section -->
            {% if venue.amenities.all %}
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-star me-2"></i>Amenities & Features</h5>
                    <div class="row">
                        {% for amenity in venue.amenities.all %}
                        {% if amenity.is_active %}
                        <div class="col-md-6 col-lg-4 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>
                                    {% if amenity.custom_name %}
                                        {{ amenity.custom_name }}
                                    {% else %}
                                        {{ amenity.get_amenity_type_display }}
                                    {% endif %}
                                </span>
                            </div>
                            {% if amenity.description %}
                            <small class="text-muted ms-4">{{ amenity.description }}</small>
                            {% endif %}
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}


        </div>
    </div>

    <!-- Services Section -->
    {% if services %}
    <div class="row mb-5">
        <div class="col-12">
            <div class="accordion" id="servicesAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingServices">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseServices" aria-expanded="true" aria-controls="collapseServices">
                            <i class="fas fa-spa me-2"></i>Services Offered
                        </button>
                    </h2>
                    <div id="collapseServices" class="accordion-collapse collapse show" aria-labelledby="headingServices">
                        <div class="accordion-body">
                            <!-- Services List - Vertical Layout -->
                            <div class="services-list">
                                {% for service in services %}
                                <div class="service-item border rounded p-3 mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <!-- Service Info -->
                                        <div class="service-info flex-grow-1 me-3">
                                            <h5 class="service-name mb-1">{{ service.service_title }}</h5>
                                            <p class="service-description text-muted mb-2 small">{{ service.short_description }}</p>

                                            <!-- Price and Duration -->
                                            <div class="service-meta d-flex align-items-center">
                                                {% if service|has_service_discount %}
                                                    <div class="price-info me-3">
                                                        <span class="text-decoration-line-through text-muted me-2">${{ service.price }}</span>
                                                        <span class="text-success fw-bold">${{ service|get_service_discounted_price }}</span>
                                                        <span class="badge bg-success ms-2">
                                                            <i class="fas fa-tag me-1"></i>{{ service|get_service_best_discount|format_discount_value }}
                                                        </span>
                                                    </div>
                                                {% else %}
                                                    <span class="text-primary fw-bold me-3">${{ service.price }}</span>
                                                {% endif %}
                                                <span class="text-muted">{{ service.duration_display }}</span>
                                            </div>
                                        </div>

                                        <!-- Book Service Button -->
                                        <div class="service-action">
                                            <a href="{% url 'venues_app:service_detail' venue_slug=venue.slug service_slug=service.slug %}"
                                               class="btn btn-primary">
                                                <i class="fas fa-calendar-plus me-1"></i>Book Service
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- FAQs Section -->
    {% if faqs %}
    <div class="row mb-5">
        <div class="col-12">
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingFaq">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFaq" aria-expanded="true" aria-controls="collapseFaq">
                            <i class="fas fa-question-circle me-2"></i>Frequently Asked Questions
                        </button>
                    </h2>
                    <div id="collapseFaq" class="accordion-collapse collapse show" aria-labelledby="headingFaq">
                        <div class="accordion-body">
                            {% for faq in faqs %}
                            <div class="faq-item mb-3">
                                <h6 class="fw-bold mb-2">{{ faq.question }}</h6>
                                <p class="mb-0">{{ faq.answer|linebreaks }}</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Flag Venue Button (Fixed Position) -->
{% if can_flag %}
<div class="position-fixed bottom-0 end-0 p-3">
    <a href="{% url 'venues_app:flag_venue' venue_slug=venue.slug %}" class="btn btn-outline-danger rounded-pill shadow">
        <i class="fas fa-flag me-2"></i>{% trans "Report Issue" %}
    </a>
</div>
{% endif %}
{% if user.is_authenticated and user.is_customer %}
<div class="position-fixed bottom-0 start-0 p-3">
    <button id="favorite-btn" data-venue-id="{{ venue.id }}" class="btn btn-outline-primary rounded-pill shadow" aria-label="Toggle favorite">
        <i class="fas fa-heart"></i>
    </button>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const btn = document.getElementById('favorite-btn');
  if (!btn) return;
  const venueId = btn.dataset.venueId;
  const csrftoken = document.querySelector('[name=csrfmiddlewaretoken]').value;
  function updateStatus(isFav) {
    if (isFav) {
      btn.classList.add('text-danger', 'active');
    } else {
      btn.classList.remove('text-danger', 'active');
    }
  }
  fetch('{% url "dashboard_app:check_favorite_status" venue_id=0 %}'.replace('0', venueId))
    .then(r => r.json())
    .then(data => updateStatus(data.is_favorite));
  btn.addEventListener('click', function(e) {
    e.preventDefault();
    const isFav = btn.classList.contains('active');
    const url = isFav ? '{% url "dashboard_app:remove_favorite_venue" venue_id=0 %}' : '{% url "dashboard_app:add_favorite_venue" venue_id=0 %}';
    fetch(url.replace('0', venueId), {
      method: 'POST',
      headers: { 'X-CSRFToken': csrftoken }
    }).then(r => r.json()).then(data => {
      if (data.success) {
        updateStatus(!isFav);
      }
    });
  });
});
</script>
{% endblock %}
