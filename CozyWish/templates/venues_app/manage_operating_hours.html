{% extends 'base.html' %}
{% load widget_tweaks i18n %}

{% block title %}Manage Operating Hours - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    }

    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    .venues-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
    }

    .venues-header h2 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin: 0;
    }

    .venues-header p {
        color: #666;
        margin: 0.5rem 0 0 0;
    }

    .operating-hours-form {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .day-row {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }

    .day-row:last-child {
        border-bottom: none;
    }

    .day-name {
        font-weight: 600;
        width: 120px;
        flex-shrink: 0;
    }

    .time-inputs {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-grow: 1;
    }

    .time-input {
        flex: 1;
        max-width: 150px;
    }

    .closed-checkbox {
        margin-left: 1rem;
    }

    .form-control {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem;
        font-family: var(--font-primary);
    }

    .form-control:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
        background-color: black;
        border-color: black;
        color: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .btn-primary:hover {
        background-color: #333;
        border-color: #333;
    }

    .btn-outline-secondary {
        border: 2px solid black;
        color: black;
        background: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .btn-outline-secondary:hover {
        background-color: black;
        color: white;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: flex-start;
        margin-top: 2rem;
    }

    .text-danger {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="venues-header d-flex align-items-center">
                    <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2>{% trans "Manage Operating Hours" %}</h2>
                        <p>Set your venue's operating hours for each day of the week</p>
                    </div>
                </div>

                <!-- Operating Hours Form -->
                <div class="operating-hours-form">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        {{ formset.management_form }}

                        {% for form, day_name in formset_with_days %}
                            <div class="day-row">
                                <div class="day-name">{{ day_name }}</div>
                                
                                <div class="time-inputs">
                                    {{ form.day.as_hidden }}
                                    
                                    <div class="time-input">
                                        <label class="form-label small">Opening</label>
                                        {{ form.opening|add_class:"form-control" }}
                                        {% for error in form.opening.errors %}
                                            <div class="text-danger">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    
                                    <div class="time-input">
                                        <label class="form-label small">Closing</label>
                                        {{ form.closing|add_class:"form-control" }}
                                        {% for error in form.closing.errors %}
                                            <div class="text-danger">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    
                                    <div class="closed-checkbox">
                                        <div class="form-check">
                                            {{ form.is_closed|add_class:"form-check-input" }}
                                            <label class="form-check-label" for="{{ form.is_closed.id_for_label }}">
                                                Closed
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                {% for error in form.non_field_errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endfor %}

                        {% for error in formset.non_form_errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Operating Hours
                            </button>
                            <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle closed checkbox changes
    const closedCheckboxes = document.querySelectorAll('input[name$="-is_closed"]');
    
    closedCheckboxes.forEach(function(checkbox) {
        const row = checkbox.closest('.day-row');
        const timeInputs = row.querySelectorAll('input[type="time"]');
        
        function toggleTimeInputs() {
            timeInputs.forEach(function(input) {
                input.disabled = checkbox.checked;
                if (checkbox.checked) {
                    input.value = '';
                }
            });
        }
        
        // Initial state
        toggleTimeInputs();
        
        // Handle changes
        checkbox.addEventListener('change', toggleTimeInputs);
    });
});
</script>
{% endblock %}
