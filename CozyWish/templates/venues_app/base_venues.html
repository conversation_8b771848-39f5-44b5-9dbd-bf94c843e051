{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Venues App - Black & White Design */
        /* Matching homepage and accounts_app design with clean typography and spacing */

        /* CSS Variables for fonts */
        :root {
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Venues wrapper - clean white background */
        .venues-wrapper {
            background-color: white;
            min-height: 100vh;
            padding: 2rem 0;
            font-family: var(--font-primary);
        }

        /* Typography */
        .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
        .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            margin-bottom: 1rem;
        }

        .venues-wrapper h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .venues-wrapper .lead {
            font-size: 1.1rem;
            color: black;
            opacity: 0.8;
            margin-bottom: 2rem;
        }

        /* Cards - clean white with black borders */
        .venues-wrapper .card {
            background: white;
            border: 2px solid black;
            border-radius: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .venues-wrapper .card:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .venues-wrapper .card-header {
            background: white !important;
            color: black !important;
            border-bottom: 2px solid black;
            padding: 1rem 1.5rem;
            font-weight: 600;
        }

        .venues-wrapper .card-header h1,
        .venues-wrapper .card-header h2,
        .venues-wrapper .card-header h3,
        .venues-wrapper .card-header h4,
        .venues-wrapper .card-header h5,
        .venues-wrapper .card-header h6 {
            color: black !important;
            margin-bottom: 0;
        }

        .venues-wrapper .card-header .mb-0 {
            color: black !important;
        }

        .venues-wrapper .card-body {
            padding: 1.5rem;
        }

        .venues-wrapper .card-title {
            color: black;
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        /* Buttons - black and white theme */
        .venues-wrapper .btn {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.375rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid black;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .venues-wrapper .btn-primary {
            background: white;
            color: black;
            border-color: black;
        }

        .venues-wrapper .btn-primary:hover {
            background: #f8f9fa;
            color: black;
            border-color: black;
        }

        .venues-wrapper .btn-outline-primary {
            background: white;
            color: black;
            border-color: black;
        }

        .venues-wrapper .btn-outline-primary:hover {
            background: #f8f9fa;
            color: black;
            border-color: black;
        }

        .venues-wrapper .btn-outline-secondary {
            background: white;
            color: black;
            border-color: black;
            opacity: 0.8;
        }

        .venues-wrapper .btn-outline-secondary:hover {
            background: #f8f9fa;
            color: black;
            border-color: black;
            opacity: 1;
        }

        /* Forms - clean styling */
        .venues-wrapper .form-label {
            color: black;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .venues-wrapper .form-control,
        .venues-wrapper .form-select {
            border: 2px solid black;
            border-radius: 0.375rem;
            padding: 0.75rem;
            font-family: var(--font-primary);
            background: white;
            color: black;
        }

        .venues-wrapper .form-control:focus,
        .venues-wrapper .form-select:focus {
            border-color: black;
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
            background: white;
            color: black;
        }

        .venues-wrapper .form-control.is-invalid {
            border-color: black;
        }

        .venues-wrapper .invalid-feedback {
            color: black;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .venues-wrapper .form-text {
            color: black;
            opacity: 0.7;
            font-size: 0.875rem;
        }

        /* Tables - clean black and white */
        .venues-wrapper .table {
            color: black;
            border-color: black;
        }

        .venues-wrapper .table th {
            background: white;
            color: black;
            border-color: black;
            font-weight: 600;
            padding: 1rem;
        }

        .venues-wrapper .table td {
            border-color: black;
            padding: 1rem;
            vertical-align: middle;
        }

        .venues-wrapper .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* Badges - black and white theme with proper contrast */
        .venues-wrapper .badge {
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 0.25rem;
        }

        /* Bootstrap badge overrides for proper visibility */
        .venues-wrapper .badge.bg-success {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .venues-wrapper .badge.bg-secondary {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .venues-wrapper .badge.bg-primary {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .venues-wrapper .badge.bg-info {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .venues-wrapper .badge.bg-warning {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .venues-wrapper .badge.bg-danger {
            background-color: white !important;
            color: black !important;
            border: 2px solid black;
        }

        .venues-wrapper .badge-approved {
            background: white;
            color: black;
            border: 2px solid black;
        }

        .venues-wrapper .badge-pending {
            background: white;
            color: black;
            border: 2px solid black;
        }

        .venues-wrapper .badge-rejected {
            background: white;
            color: black;
            border: 2px solid black;
            opacity: 0.7;
        }

        /* Alerts - clean styling */
        .venues-wrapper .alert {
            border: 2px solid black;
            border-radius: 0.5rem;
            background: white;
            color: black;
        }

        .venues-wrapper .alert-info {
            background: white;
            border-color: black;
            color: black;
        }

        .venues-wrapper .alert-danger {
            background: white;
            border-color: black;
            color: black;
        }

        /* Pagination - clean styling */
        .venues-wrapper .pagination .page-link {
            color: black;
            background: white;
            border: 2px solid black;
            margin: 0 0.125rem;
            border-radius: 0.375rem;
        }

        .venues-wrapper .pagination .page-link:hover {
            color: black;
            background: #f8f9fa;
            border-color: black;
        }

        .venues-wrapper .pagination .page-item.active .page-link {
            color: black;
            background: #f8f9fa;
            border-color: black;
        }

        .venues-wrapper .pagination .page-item.disabled .page-link {
            color: black;
            background: white;
            border-color: black;
            opacity: 0.5;
        }

        /* Statistics cards */
        .venues-wrapper .stat-card {
            text-align: center;
            padding: 2rem 1rem;
        }

        .venues-wrapper .stat-card i {
            font-size: 3rem;
            color: black;
            margin-bottom: 1rem;
        }

        .venues-wrapper .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: black;
            margin-bottom: 0.5rem;
        }

        .venues-wrapper .stat-label {
            color: black;
            font-weight: 500;
            opacity: 0.8;
        }

        /* Additional Bootstrap component fixes for visibility */
        .venues-wrapper .btn-success {
            background: white !important;
            color: black !important;
            border-color: black !important;
        }

        .venues-wrapper .btn-success:hover {
            background: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .venues-wrapper .btn-danger {
            background: white !important;
            color: black !important;
            border-color: black !important;
        }

        .venues-wrapper .btn-danger:hover {
            background: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .venues-wrapper .btn-info {
            background: white !important;
            color: black !important;
            border: 2px solid black !important;
        }

        .venues-wrapper .btn-info:hover {
            background: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        .venues-wrapper .btn-warning {
            background: white !important;
            color: black !important;
            border: 2px solid black !important;
        }

        .venues-wrapper .btn-warning:hover {
            background: #f8f9fa !important;
            color: black !important;
            border-color: black !important;
        }

        /* List group items */
        .venues-wrapper .list-group-item {
            background: white !important;
            color: black !important;
            border: 1px solid black !important;
        }

        .venues-wrapper .list-group-item:hover {
            background: rgba(0, 0, 0, 0.05) !important;
        }

        /* Text colors for better visibility */
        .venues-wrapper .text-muted {
            color: black !important;
            opacity: 0.7;
        }

        .venues-wrapper .text-success {
            color: black !important;
        }

        .venues-wrapper .text-danger {
            color: black !important;
        }

        .venues-wrapper .text-info {
            color: black !important;
        }

        .venues-wrapper .text-warning {
            color: black !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .venues-wrapper {
                padding: 1rem 0;
            }

            .venues-wrapper h1 {
                font-size: 2rem;
            }

            .venues-wrapper .card-body {
                padding: 1rem;
            }

            .venues-wrapper .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }
        }
    </style>

    {% block venues_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container-fluid">
        <div class="container">
            {% block venues_content %}{% endblock %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
    {% block venues_extra_js %}{% endblock %}
{% endblock %}
