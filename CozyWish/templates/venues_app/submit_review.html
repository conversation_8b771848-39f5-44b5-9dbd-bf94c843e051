{% extends 'base.html' %}

{% block title %}Submit Review - {{ venue.name }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --primary-color: black;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    /* Venues card - clean white with black border */
    .venues-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .venues-card-body {
        padding: 3rem 2.5rem;
    }

    .venues-card-header {
        background: white;
        color: white;
        padding: 1.5rem 2.5rem;
        border-radius: 0.8rem 0.8rem 0 0;
        border-bottom: 2px solid black;
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: black;
        font-family: var(--font-primary);
    }

    /* Form styling */
    .venues-wrapper .form-label {
        font-weight: 500;
        color: black;
        margin-bottom: 0.5rem;
        font-family: var(--font-primary);
    }

    .venues-wrapper .form-control {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--font-primary);
        background: white;
        color: black;
        transition: all 0.3s ease;
    }

    .venues-wrapper .form-control:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
        background: white;
        color: black;
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid black;
    }

    .venues-wrapper .btn-primary {
        background: black;
        color: white;
        border-color: black;
    }

    .venues-wrapper .btn-primary:hover {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-primary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-primary:hover {
        background: black;
        color: white;
        border-color: black;
    }

    /* Breadcrumb styling */
    .venues-wrapper .breadcrumb {
        background: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 1rem;
    }

    .venues-wrapper .breadcrumb-item a {
        color: black;
        text-decoration: none;
    }

    .venues-wrapper .breadcrumb-item a:hover {
        color: black;
        opacity: 0.7;
    }

    .venues-wrapper .breadcrumb-item.active {
        color: black;
        font-weight: 500;
    }

    /* Star rating styling */
    .star-rating-input .star-label {
        color: #ccc;
        cursor: pointer;
        transition: color 0.2s ease;
        font-size: 2rem !important;
    }

    .star-rating-input .star-label:hover {
        color: black;
    }

    /* Error styling */
    .venues-wrapper .invalid-feedback {
        color: black;
        font-weight: 500;
        margin-top: 0.5rem;
    }

    .venues-wrapper .is-invalid {
        border-color: black;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_list' %}">Venues</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">{{ venue.name }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Submit Review</li>
                    </ol>
                </nav>

                <div class="venues-card">
                    <div class="venues-card-header">
                        <h5 class="mb-0">{% if existing_review %}Edit{% else %}Submit{% endif %} Review for {{ venue.name }}</h5>
                    </div>
                    <div class="venues-card-body">
                        <form method="post" action="{% url 'venues_app:submit_review' venue_slug=venue.slug %}">
                            {% csrf_token %}

                            <div class="mb-3">
                                <label for="{{ form.rating.id_for_label }}" class="form-label">Rating</label>
                                <div class="star-rating-input">
                                    <div class="d-flex">
                                        {% for i in '12345' %}
                                        <div class="star-container me-2">
                                            <input type="radio" name="{{ form.rating.name }}" id="star{{ i }}" value="{{ i }}" class="d-none" {% if form.rating.value|stringformat:"s" == i %}checked{% endif %}>
                                            <label for="star{{ i }}" class="star-label" style="color: {% if form.rating.value|stringformat:'s' >= i %}black{% else %}#ccc{% endif %}; cursor: pointer;">★</label>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% if form.rating.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.rating.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.comment.id_for_label }}" class="form-label">Your Review</label>
                                <textarea name="{{ form.comment.name }}" id="{{ form.comment.id_for_label }}" class="form-control {% if form.comment.errors %}is-invalid{% endif %}" rows="5" placeholder="Share your experience with this venue...">{{ form.comment.value|default:'' }}</textarea>
                                {% if form.comment.errors %}
                                <div class="invalid-feedback">
                                    {{ form.comment.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-outline-primary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>{% if existing_review %}Update{% else %}Submit{% endif %} Review
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Star rating functionality
        const starLabels = document.querySelectorAll('.star-label');
        const starInputs = document.querySelectorAll('input[name="{{ form.rating.name }}"]');

        // Add event listeners to star labels
        starLabels.forEach((label, index) => {
            label.addEventListener('mouseenter', () => {
                // Highlight stars on hover
                for (let i = 0; i <= index; i++) {
                    starLabels[i].style.color = 'black';
                }
                for (let i = index + 1; i < starLabels.length; i++) {
                    starLabels[i].style.color = '#ccc';
                }
            });

            label.addEventListener('mouseleave', () => {
                // Reset to selected state
                updateStars();
            });
        });

        function updateStars() {
            // Find the selected rating
            let selectedIndex = -1;
            starInputs.forEach((input, index) => {
                if (input.checked) {
                    selectedIndex = index;
                }
            });

            // Update star colors
            starLabels.forEach((label, index) => {
                if (index <= selectedIndex) {
                    label.style.color = 'black';
                } else {
                    label.style.color = '#ccc';
                }
            });
        }
    });
</script>
{% endblock %}
