{% extends 'base.html' %}
{% load widget_tweaks i18n %}

{% block title %}{{ action }} Amenity - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    .venues-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
    }

    .venues-header h2 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin: 0;
    }

    .venues-header p {
        color: #666;
        margin: 0.5rem 0 0 0;
    }

    .amenity-form {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-control {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem;
        font-family: var(--font-primary);
    }

    .form-control:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
    }

    .form-label {
        font-weight: 600;
        color: black;
        margin-bottom: 0.5rem;
    }

    .btn-primary {
        background-color: black;
        border-color: black;
        color: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .btn-primary:hover {
        background-color: #333;
        border-color: #333;
    }

    .btn-outline-secondary {
        border: 2px solid black;
        color: black;
        background: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .btn-outline-secondary:hover {
        background-color: black;
        color: white;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: flex-start;
        margin-top: 2rem;
    }

    .text-danger {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .help-text {
        font-size: 0.875rem;
        color: #666;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="venues-header d-flex align-items-center">
                    <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2>{{ action }} Amenity</h2>
                        <p>{% if action == 'Edit' %}Update{% else %}Add{% endif %} amenity for {{ venue.venue_name }}</p>
                    </div>
                </div>

                <!-- Amenity Form -->
                <div class="amenity-form">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label" for="{{ form.amenity_type.id_for_label }}">{% trans "Amenity Type" %}</label>
                                {{ form.amenity_type|add_class:'form-control' }}
                                {% if form.amenity_type.help_text %}
                                    <div class="help-text">{{ form.amenity_type.help_text }}</div>
                                {% endif %}
                                {% for error in form.amenity_type.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label" for="{{ form.custom_name.id_for_label }}">{% trans "Custom Name" %}</label>
                                {{ form.custom_name|add_class:'form-control' }}
                                {% if form.custom_name.help_text %}
                                    <div class="help-text">{{ form.custom_name.help_text }}</div>
                                {% endif %}
                                {% for error in form.custom_name.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label" for="{{ form.description.id_for_label }}">{% trans "Description" %}</label>
                            {{ form.description|add_class:'form-control' }}
                            {% if form.description.help_text %}
                                <div class="help-text">{{ form.description.help_text }}</div>
                            {% endif %}
                            {% for error in form.description.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_active|add_class:'form-check-input' }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {% trans "Active (visible to customers)" %}
                                </label>
                            </div>
                            {% for error in form.is_active.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ action }} Amenity
                            </button>
                            <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}
