{% extends 'base.html' %}

{% block title %}Delete Service - {{ object.service_title }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    /* Venues card - clean white with black border */
    .venues-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .venues-card-body {
        padding: 3rem 2.5rem;
    }

    /* Warning card styling */
    .venues-card-warning {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .venues-card-warning-header {
        background: white;
        color: black;
        padding: 1.5rem 2.5rem;
        border-radius: 0.8rem 0.8rem 0 0;
        border-bottom: 2px solid black;
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: black;
        font-family: var(--font-primary);
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid black;
    }

    .venues-wrapper .btn-primary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-primary:hover {
        background: #f8f9fa;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary:hover {
        background: #f8f9fa;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-danger {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-danger:hover {
        background: #f8f9fa;
        color: black;
        border-color: black;
    }

    /* Alert styling */
    .venues-wrapper .alert {
        background: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        color: black;
        padding: 1.5rem;
    }

    .venues-wrapper .text-muted {
        color: black !important;
        opacity: 0.7;
    }

    .venues-wrapper .text-danger {
        color: black !important;
        font-weight: 600;
    }

    /* Badge styling */
    .venues-wrapper .badge {
        background: white !important;
        color: black;
        border: 2px solid black;
        font-weight: 500;
    }

    /* Header styling */
    .venues-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
    }

    .venues-header h2 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .venues-header p {
        opacity: 0.8;
        margin-bottom: 0;
    }

    /* Icon styling */
    .venues-wrapper .bg-light {
        background: white !important;
        border: 2px solid black;
        border-radius: 0.5rem;
    }

    .venues-wrapper .rounded-circle {
        border: 2px solid black;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="venues-header d-flex align-items-center">
                    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2 class="text-danger">Delete Service</h2>
                        <p>Permanently remove this service from {{ venue.venue_name }}</p>
                    </div>
                </div>

                <!-- Warning Card -->
                <div class="venues-card-warning">
                    <div class="venues-card-warning-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Confirm Service Deletion
                        </h5>
                    </div>
                    <div class="venues-card-body">

                        <!-- Service Details -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <h5 class="mb-2">{{ object.service_title }}</h5>
                                <p class="text-muted mb-3">{{ object.short_description|truncatewords:30 }}</p>

                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="mb-2">
                                            <strong>Price:</strong> {{ object.price_display }}
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="mb-2">
                                            <strong>Duration:</strong> {{ object.duration_display }}
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-2">
                                    <strong>Status:</strong>
                                    {% if object.is_active %}
                                        <span class="badge">Active</span>
                                    {% else %}
                                        <span class="badge">Inactive</span>
                                    {% endif %}
                                </div>

                                <div class="mb-2">
                                    <strong>Created:</strong> {{ object.created_at|date:"M d, Y" }}
                                </div>
                            </div>

                            <div class="col-md-4 text-center">
                                <div class="bg-light rounded p-4">
                                    <i class="fas fa-spa fa-3x text-muted mb-3"></i>
                                    <p class="mb-0 small text-muted">This service will be permanently deleted</p>
                                </div>
                            </div>
                        </div>

                        <!-- Warning Message -->
                        <div class="alert">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>Important Warning
                            </h6>
                            <p class="mb-2">This action cannot be undone. Deleting this service will:</p>
                            <ul class="mb-0">
                                <li>Permanently remove the service from your venue</li>
                                <li>Make the service unavailable for new bookings</li>
                                <li>Potentially affect existing bookings (if any)</li>
                                <li>Remove all service-related data</li>
                            </ul>
                        </div>

                        <!-- Confirmation Form -->
                        <form method="post">
                            {% csrf_token %}

                            <div class="d-flex justify-content-between align-items-center">
                                <a href="{% url 'venues_app:manage_services' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>

                                <div>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-trash me-2"></i>Yes, Delete Service
                                    </button>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>

                <!-- Alternative Actions -->
                <div class="venues-card mt-4">
                    <div class="venues-card-body">
                        <h6>
                            <i class="fas fa-lightbulb me-2"></i>Alternative Actions
                        </h6>
                        <p class="text-muted">Instead of deleting, you might consider:</p>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-light rounded-circle p-2 me-3">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div>
                                        <strong>Edit Service</strong>
                                        <br>
                                        <small class="text-muted">Update pricing, description, or duration</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-light rounded-circle p-2 me-3">
                                        <i class="fas fa-pause"></i>
                                    </div>
                                    <div>
                                        <strong>Deactivate Service</strong>
                                        <br>
                                        <small class="text-muted">Temporarily hide without deleting</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <a href="{% url 'venues_app:service_edit' object.pk %}" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-edit me-2"></i>Edit Instead
                            </a>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}
