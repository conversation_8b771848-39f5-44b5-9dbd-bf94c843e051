{% extends 'base.html' %}
{% load i18n %}
{% load review_tags %}

{% block title %}{% trans "Search Venues" %} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    /* Search filters */
    .search-filters {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .filter-section {
        margin-bottom: 1.5rem;
    }

    .filter-label {
        font-weight: 500;
        color: black;
        margin-bottom: 0.5rem;
        font-family: var(--font-primary);
        display: block;
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: black;
        font-family: var(--font-primary);
    }

    .venues-wrapper .lead {
        font-size: 1.2rem;
        opacity: 0.8;
    }

    /* Form styling */
    .venues-wrapper .form-control, .venues-wrapper .form-select {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--font-primary);
        background: white;
        color: black;
        transition: all 0.3s ease;
        width: 100%;
    }

    .venues-wrapper .form-control:focus, .venues-wrapper .form-select:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
        background: white;
        color: black;
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid black;
    }

    .venues-wrapper .btn-primary {
        background: black;
        color: white;
        border-color: black;
    }

    .venues-wrapper .btn-primary:hover {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary:hover {
        background: black;
        color: white;
        border-color: black;
    }

    /* Search results header */
    .search-results-header {
        margin: 2rem 0;
        padding: 1rem 0;
        border-bottom: 2px solid black;
    }

    .results-count {
        font-weight: 500;
        color: black;
        font-size: 1.1rem;
    }

    /* Card styling */
    .venues-wrapper .card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    /* Venue card specific styles */
    .venue-card {
        cursor: pointer;
    }

    .venue-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .card-img-container {
        position: relative;
        overflow: hidden;
        border-radius: 1rem 1rem 0 0;
    }

    .card-img-top {
        transition: transform 0.3s ease;
    }

    .venue-card:hover .card-img-top {
        transform: scale(1.05);
    }

    /* Services section styling */
    .services-section {
        border-top: 1px solid #eee;
        padding-top: 1rem;
    }

    .services-title {
        font-weight: 600;
        color: black;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .service-item {
        background: #f8f9fa;
        border: 1px solid #e9ecef !important;
        transition: all 0.2s ease;
    }

    .service-item:hover {
        background: #e9ecef;
        border-color: black !important;
    }

    .service-name {
        font-size: 0.85rem;
        font-weight: 600;
        color: black;
        margin-bottom: 0.25rem;
    }

    .service-description {
        font-size: 0.75rem;
        line-height: 1.3;
    }

    .service-price {
        font-size: 0.8rem;
        color: black;
    }

    .service-duration {
        font-size: 0.7rem;
    }

    .add-to-cart-btn {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        white-space: nowrap;
    }

    .add-to-cart-btn:hover {
        background: black;
        color: white;
        border-color: black;
    }

    .more-services {
        font-style: italic;
    }

    .view-details-btn {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        pointer-events: none;
    }

    .venues-wrapper .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .venues-wrapper .card-img-top {
        border-radius: 0.8rem 0.8rem 0 0;
        border-bottom: 2px solid black;
    }

    .venues-wrapper .card-body {
        padding: 1.5rem;
    }

    .venues-wrapper .card-title a {
        color: black;
        text-decoration: none;
        font-weight: 600;
    }

    .venues-wrapper .card-title a:hover {
        color: black;
        opacity: 0.8;
    }

    /* Rating and location styling */
    .rating-score {
        font-weight: 600;
        color: black;
    }

    .review-count {
        color: black;
        opacity: 0.7;
    }

    .location {
        color: black;
        opacity: 0.8;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    /* Badge styling */
    .venues-wrapper .badge {
        background: white !important;
        color: black;
        border: 1px solid black;
        font-weight: 500;
    }

    /* Price range styling */
    .price-range {
        color: black;
        opacity: 0.8;
    }

    /* Pagination styling */
    .venues-wrapper .pagination .page-link {
        color: black;
        border: 2px solid black;
        background: white;
        margin: 0 0.25rem;
        border-radius: 0.5rem;
    }

    .venues-wrapper .pagination .page-link:hover {
        background: black;
        color: white;
        border-color: black;
    }

    .venues-wrapper .pagination .page-item.active .page-link {
        background: black;
        color: white;
        border-color: black;
    }

    /* No results styling */
    .no-results {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .no-results i {
        font-size: 4rem;
        color: black;
        opacity: 0.7;
        margin-bottom: 2rem;
    }

    .no-results h3 {
        margin-bottom: 1rem;
    }

    .no-results p {
        opacity: 0.8;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1>Search Venues</h1>
                <p class="lead">Find the perfect spa and wellness venue for you</p>
            </div>
        </div>

        <!-- Search and Filter Form -->
        <div class="search-filters">
            <form method="get" action="{% url 'venues_app:venue_search' %}">
                <div class="row">
                    <!-- Search Query -->
                    <div class="col-md-4 filter-section">
                        <label class="filter-label">Search</label>
                        {{ search_form.query }}
                    </div>

                    <!-- Location -->
                    <div class="col-md-4 filter-section">
                        <label class="filter-label">Location</label>
                        {{ search_form.location }}
                    </div>

                    <!-- Category -->
                    <div class="col-md-4 filter-section">
                        <label class="filter-label">Category</label>
                        {{ search_form.category }}
                    </div>
                </div>

                <div class="row">
                    <!-- Sort By -->
                    <div class="col-md-6 filter-section">
                        <label class="filter-label">Sort By</label>
                        {{ filter_form.sort_by }}
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2" aria-label="Search venues">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                        <a href="{% url 'venues_app:venue_search' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Search Results Header -->
        <div class="search-results-header">
            <div class="results-count">
                {% if is_search_results %}
                    {{ total_venues }} venue{{ total_venues|pluralize }} found
                {% else %}
                    Showing all {{ total_venues }} venue{{ total_venues|pluralize }}
                {% endif %}
            </div>
        </div>

        <!-- Venue Results -->
        {% if page_obj %}
        <div class="row g-4">
            {% for venue in page_obj %}
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 venue-card" data-venue-url="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                    <div class="card-img-container">
                        <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}"
                             class="card-img-top" alt="{{ venue.venue_name }}">
                        {% venue_badge_card venue %}
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">{{ venue.venue_name }}</h5>
                        <div class="rating mb-2">
                            <span class="rating-score">{{ venue.avg_rating|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                            <span class="review-count">({{ venue.review_count|default:"0" }})</span>
                        </div>
                        <p class="location">{{ venue.city }}, {{ venue.state }}</p>
                        <p class="card-text">{{ venue.short_description|truncatewords:15 }}</p>

                        <!-- Categories -->
                        {% if venue.categories.all %}
                        <div class="categories mb-3">
                            {% for category in venue.categories.all %}
                                <span class="badge me-1">{{ category.name }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Services Section -->
                        {% if venue.services.all %}
                        <div class="services-section mb-3">
                            <h6 class="services-title mb-2">Featured Services</h6>
                            <div class="services-list">
                                {% for service in venue.services.all|slice:":3" %}
                                <div class="service-item mb-2 p-2 border rounded">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="service-details flex-grow-1">
                                            <h6 class="service-name mb-1">{{ service.service_title }}</h6>
                                            <p class="service-description mb-1 text-muted small">
                                                {{ service.short_description|truncatewords:8 }}
                                            </p>
                                            <div class="service-meta d-flex align-items-center">
                                                <span class="service-price fw-bold me-2">{{ service.price_display }}</span>
                                                <span class="service-duration text-muted small">{{ service.duration_display }}</span>
                                            </div>
                                        </div>
                                        {% if user.is_authenticated and user.is_customer %}
                                        <div class="service-actions ms-2">
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-primary add-to-cart-btn"
                                                    data-service-id="{{ service.id }}"
                                                    data-service-name="{{ service.service_title }}"
                                                    data-venue-name="{{ venue.venue_name }}"
                                                    onclick="event.stopPropagation(); showAddToCartModal({{ service.id }}, '{{ service.service_title }}', '{{ venue.venue_name }}');">
                                                <i class="fas fa-cart-plus me-1"></i>Add
                                            </button>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}

                                {% if venue.services.all.count > 3 %}
                                <div class="more-services text-center mt-2">
                                    <small class="text-muted">+{{ venue.services.all.count|add:"-3" }} more service{{ venue.services.all.count|add:"-3"|pluralize }}</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% else %}
                        <div class="no-services mb-3">
                            <small class="text-muted">No services available</small>
                        </div>
                        {% endif %}

                        <!-- View Details Button -->
                        <div class="text-center">
                            <span class="btn btn-outline-secondary btn-sm view-details-btn">
                                <i class="fas fa-eye me-1"></i>View Details
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Search results pagination" class="mt-5">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <!-- No Results -->
        <div class="no-results">
            <i class="fas fa-search"></i>
            <h3>No venues found</h3>
            <p>Try adjusting your search criteria or browse all venues.</p>
            <a href="{% url 'venues_app:venue_search' %}" class="btn btn-primary" aria-label="Browse all venues">
                <i class="fas fa-list me-2"></i>{% trans "Browse All Venues" %}
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Add to Cart Modal -->
{% if user.is_authenticated and user.is_customer %}
<div class="modal fade" id="addToCartModal" tabindex="-1" aria-labelledby="addToCartModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addToCartModalLabel">Add Service to Cart</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="modalServiceInfo" class="mb-3">
                    <h6 id="modalServiceName"></h6>
                    <p class="text-muted mb-1" id="modalVenueName"></p>
                </div>
                <form id="addToCartForm" method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="selected_date" class="form-label">Select Date</label>
                        <input type="date" class="form-control" id="selected_date" name="selected_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="selected_time_slot" class="form-label">Select Time</label>
                        <select class="form-control" id="selected_time_slot" name="selected_time_slot" required>
                            <option value="">Choose a time slot...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantity</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="1" max="10" value="1" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmAddToCart">Add to Cart</button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Make venue cards clickable
    const venueCards = document.querySelectorAll('.venue-card');
    venueCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on add to cart button
            if (e.target.closest('.add-to-cart-btn')) {
                return;
            }

            const venueUrl = this.dataset.venueUrl;
            if (venueUrl) {
                window.location.href = venueUrl;
            }
        });
    });

    {% if user.is_authenticated and user.is_customer %}
    // Add to cart functionality
    let currentServiceId = null;
    const addToCartModal = new bootstrap.Modal(document.getElementById('addToCartModal'));

    // Set minimum date to tomorrow
    const dateInput = document.getElementById('selected_date');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.min = tomorrow.toISOString().split('T')[0];

    // Set maximum date to 30 days from now
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 30);
    dateInput.max = maxDate.toISOString().split('T')[0];

    // Show add to cart modal
    window.showAddToCartModal = function(serviceId, serviceName, venueName) {
        currentServiceId = serviceId;
        document.getElementById('modalServiceName').textContent = serviceName;
        document.getElementById('modalVenueName').textContent = venueName;

        // Reset form
        document.getElementById('addToCartForm').reset();
        document.getElementById('selected_time_slot').innerHTML = '<option value="">Choose a time slot...</option>';

        addToCartModal.show();
    };

    // Load time slots when date changes
    dateInput.addEventListener('change', function() {
        const selectedDate = this.value;
        const timeSlotSelect = document.getElementById('selected_time_slot');

        if (selectedDate && currentServiceId) {
            // Clear existing options
            timeSlotSelect.innerHTML = '<option value="">Loading...</option>';

            // Fetch available time slots
            fetch(`/bookings/ajax/slots/${currentServiceId}/?date=${selectedDate}`)
                .then(response => response.json())
                .then(data => {
                    timeSlotSelect.innerHTML = '<option value="">Choose a time slot...</option>';

                    if (data.slots && data.slots.length > 0) {
                        data.slots.forEach(slot => {
                            const option = document.createElement('option');
                            option.value = slot.time;
                            option.textContent = `${slot.display} (${slot.available_spots} available)`;
                            timeSlotSelect.appendChild(option);
                        });
                    } else {
                        timeSlotSelect.innerHTML = '<option value="">No available time slots</option>';
                    }
                })
                .catch(error => {
                    console.error('Error loading time slots:', error);
                    timeSlotSelect.innerHTML = '<option value="">Error loading time slots</option>';
                });
        }
    });

    // Handle add to cart confirmation
    document.getElementById('confirmAddToCart').addEventListener('click', function() {
        const form = document.getElementById('addToCartForm');
        const formData = new FormData(form);

        if (currentServiceId && form.checkValidity()) {
            // Submit form to add to cart
            fetch(`/bookings/cart/add/${currentServiceId}/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => {
                if (response.ok) {
                    addToCartModal.hide();
                    // Show success message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                    alertDiv.innerHTML = `
                        <strong>Success!</strong> Service added to cart.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(alertDiv);

                    // Auto-remove alert after 3 seconds
                    setTimeout(() => {
                        if (alertDiv.parentNode) {
                            alertDiv.remove();
                        }
                    }, 3000);
                } else {
                    throw new Error('Failed to add to cart');
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                // Show error message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                alertDiv.innerHTML = `
                    <strong>Error!</strong> Failed to add service to cart. Please try again.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alertDiv);

                // Auto-remove alert after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            });
        } else {
            // Show validation errors
            form.reportValidity();
        }
    });
    {% endif %}
});
</script>
{% endblock %}
