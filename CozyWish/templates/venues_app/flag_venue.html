{% extends 'base.html' %}

{% block title %}Report Issue - {{ venue.venue_name }}{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    .flag-venue-container {
        max-width: 800px;
        margin: 0 auto;
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: black;
        font-family: var(--font-primary);
    }

    /* Venue preview */
    .venue-preview {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .venue-preview img {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 0.5rem;
        border: 2px solid black;
    }

    .venue-preview .bg-light {
        background: white !important;
        border: 2px solid black;
    }

    /* Warning box */
    .warning-box {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .warning-icon {
        color: black;
        font-size: 1.5rem;
    }

    /* Form section */
    .form-section {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    /* Form styling */
    .venues-wrapper .form-label {
        font-weight: 500;
        color: black;
        margin-bottom: 0.5rem;
        font-family: var(--font-primary);
    }

    .venues-wrapper .form-control, .venues-wrapper .form-select {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--font-primary);
        background: white;
        color: black;
        transition: all 0.3s ease;
        width: 100%;
    }

    .venues-wrapper .form-control:focus, .venues-wrapper .form-select:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
        background: white;
        color: black;
    }

    .reason-category-help {
        color: black;
        opacity: 0.7;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .venues-wrapper .form-text {
        color: black;
        opacity: 0.7;
        font-size: 0.9rem;
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid black;
    }

    .btn-cancel {
        background: white;
        color: black !important;
        border-color: black;
    }

    .btn-cancel:hover {
        background: black;
        color: white !important;
        border-color: black;
    }

    .btn-submit {
        background: black;
        color: white !important;
        border-color: black;
    }

    .btn-submit:hover {
        background: white;
        color: black !important;
        border-color: black;
    }

    /* Error styling */
    .venues-wrapper .text-danger {
        color: black !important;
        font-weight: 500;
    }

    .venues-wrapper .text-muted {
        color: black !important;
        opacity: 0.7;
    }

    .venues-wrapper .text-warning {
        color: black !important;
        font-weight: 500;
    }

    /* Character counter styling */
    #char-count {
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-5">
        <div class="flag-venue-container">
            <!-- Page Header -->
            <div class="text-center mb-4">
                <h2><i class="fas fa-flag me-2"></i>Flag Venue - Report an Issue</h2>
                <p>Help us maintain quality by reporting inappropriate content or issues</p>
            </div>

            <!-- Venue Preview -->
            <div class="venue-preview">
                <div class="row align-items-center">
                    <div class="col-auto">
                        {% if venue.main_image %}
                            <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" class="img-fluid">
                        {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; border-radius: 8px;">
                                <i class="fas fa-image fa-2x"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col">
                        <h5 class="mb-1">{{ venue.venue_name }}</h5>
                        <p class="text-muted mb-1">{{ venue.service_provider.business_name }}</p>
                        <small class="text-muted">{{ venue.full_address }}</small>
                    </div>
                </div>
            </div>

            <!-- Warning Box -->
            <div class="warning-box">
                <div class="d-flex align-items-start">
                    <i class="fas fa-exclamation-triangle warning-icon me-3 mt-1"></i>
                    <div>
                        <h6 class="mb-2">Important Notice</h6>
                        <p class="mb-0">
                            Please only report genuine issues. False reports may result in account restrictions.
                            Our team will review all reports carefully and take appropriate action.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Flag Form -->
            <div class="form-section">
                <form method="post" novalidate>
                    {% csrf_token %}

                    <!-- Reason Category -->
                    <div class="mb-4">
                        <label for="{{ form.reason_category.id_for_label }}" class="form-label fw-bold">
                            {{ form.reason_category.label }}
                        </label>
                        {{ form.reason_category }}
                        <div class="reason-category-help">
                            Select the category that best describes the issue you're reporting.
                        </div>
                        {% if form.reason_category.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.reason_category.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Detailed Reason -->
                    <div class="mb-4">
                        <label for="{{ form.reason.id_for_label }}" class="form-label fw-bold">
                            {{ form.reason.label }}
                        </label>
                        {{ form.reason }}
                        {% if form.reason.help_text %}
                            <div class="form-text">{{ form.reason.help_text }}</div>
                        {% endif %}
                        {% if form.reason.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.reason.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Character Counter -->
                    <div class="mb-4">
                        <small class="text-muted">
                            <span id="char-count">0</span> / 1000 characters
                        </small>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-cancel">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-submit">
                            <i class="fas fa-flag me-2"></i>Submit Report
                        </button>
                    </div>
                </form>
            </div>

            <!-- Additional Information -->
            <div class="text-center mt-4">
                <small class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    Your report will be reviewed within 24-48 hours. Thank you for helping us maintain a safe platform.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter for reason field
    const reasonField = document.getElementById('{{ form.reason.id_for_label }}');
    const charCount = document.getElementById('char-count');

    if (reasonField && charCount) {
        function updateCharCount() {
            const count = reasonField.value.length;
            charCount.textContent = count;

            // Change styling based on character count - keeping black theme
            if (count > 900) {
                charCount.style.color = 'black';
                charCount.style.fontWeight = '600';
            } else if (count > 800) {
                charCount.style.color = 'black';
                charCount.style.fontWeight = '500';
            } else {
                charCount.style.color = 'black';
                charCount.style.opacity = '0.7';
                charCount.style.fontWeight = '400';
            }
        }

        // Update on input
        reasonField.addEventListener('input', updateCharCount);

        // Initial update
        updateCharCount();
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const reasonCategory = document.getElementById('{{ form.reason_category.id_for_label }}');
            const reasonDetails = document.getElementById('{{ form.reason.id_for_label }}');

            if (!reasonCategory.value) {
                e.preventDefault();
                alert('Please select a reason category.');
                reasonCategory.focus();
                return;
            }

            if (!reasonDetails.value.trim() || reasonDetails.value.trim().length < 10) {
                e.preventDefault();
                alert('Please provide detailed information (at least 10 characters).');
                reasonDetails.focus();
                return;
            }

            // Confirm submission
            if (!confirm('Are you sure you want to submit this report? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    }
});
</script>
{% endblock %}
