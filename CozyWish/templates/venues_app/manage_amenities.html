{% extends 'base.html' %}
{% load widget_tweaks i18n %}

{% block title %}Manage Amenities - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    }

    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    .venues-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
    }

    .venues-header h2 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin: 0;
    }

    .venues-header p {
        color: #666;
        margin: 0.5rem 0 0 0;
    }

    .amenities-list {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .amenity-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }

    .amenity-item:last-child {
        border-bottom: none;
    }

    .amenity-info h5 {
        margin: 0;
        font-weight: 600;
        color: black;
    }

    .amenity-info p {
        margin: 0.25rem 0 0 0;
        color: #666;
        font-size: 0.875rem;
    }

    .amenity-actions {
        display: flex;
        gap: 0.5rem;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
    }

    .btn-outline-primary {
        border: 1px solid black;
        color: black;
        background: white;
    }

    .btn-outline-primary:hover {
        background-color: black;
        color: white;
    }

    .btn-outline-danger {
        border: 1px solid #dc3545;
        color: #dc3545;
        background: white;
    }

    .btn-outline-danger:hover {
        background-color: #dc3545;
        color: white;
    }

    .add-amenity-form {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-control {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem;
        font-family: var(--font-primary);
    }

    .form-control:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
        background-color: black;
        border-color: black;
        color: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .btn-primary:hover {
        background-color: #333;
        border-color: #333;
    }

    .btn-outline-secondary {
        border: 2px solid black;
        color: black;
        background: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .btn-outline-secondary:hover {
        background-color: black;
        color: white;
    }

    .text-danger {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .no-amenities-message {
        text-align: center;
        padding: 3rem 2rem;
        color: #666;
    }

    .no-amenities-message i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #ccc;
    }

    .amenity-limit-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        color: #666;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="venues-header d-flex align-items-center">
                    <a href="{% url 'venues_app:venue_edit' %}" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2>{% trans "Manage Amenities" %}</h2>
                        <p>Add and manage amenities and features available at your venue</p>
                    </div>
                </div>

                <!-- Current Amenities -->
                <div class="amenities-list">
                    <h4>Current Amenities ({{ amenities.count }}/{{ max_amenities }})</h4>
                    
                    {% if amenities %}
                        {% for amenity in amenities %}
                            <div class="amenity-item">
                                <div class="amenity-info">
                                    <h5>
                                        {% if amenity.custom_name %}
                                            {{ amenity.custom_name }}
                                        {% else %}
                                            {{ amenity.get_amenity_type_display }}
                                        {% endif %}
                                        {% if not amenity.is_active %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </h5>
                                    {% if amenity.description %}
                                        <p>{{ amenity.description }}</p>
                                    {% endif %}
                                </div>
                                <div class="amenity-actions">
                                    <a href="{% url 'venues_app:edit_amenity' amenity.id %}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="{% url 'venues_app:delete_amenity' amenity.id %}" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash"></i> Delete
                                    </a>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="no-amenities-message">
                            <i class="fas fa-star"></i>
                            <p>{% trans "No amenities added yet. Add your first amenity below." %}</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Add New Amenity -->
                {% if can_add_amenity %}
                <div class="add-amenity-form">
                    <h4>{% trans "Add New Amenity" %}</h4>
                    
                    <div class="amenity-limit-info">
                        <i class="fas fa-info-circle me-2"></i>
                        You can add up to {{ max_amenities }} amenities for your venue.
                    </div>
                    
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label" for="{{ form.amenity_type.id_for_label }}">{% trans "Amenity Type" %}</label>
                                {{ form.amenity_type|add_class:'form-control' }}
                                {% for error in form.amenity_type.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label" for="{{ form.custom_name.id_for_label }}">{% trans "Custom Name" %}</label>
                                {{ form.custom_name|add_class:'form-control' }}
                                {% for error in form.custom_name.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label" for="{{ form.description.id_for_label }}">{% trans "Description" %}</label>
                            {{ form.description|add_class:'form-control' }}
                            {% for error in form.description.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_active|add_class:'form-check-input' }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {% trans "Active (visible to customers)" %}
                                </label>
                            </div>
                            {% for error in form.is_active.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>{% trans "Add Amenity" %}
                        </button>
                    </form>
                </div>
                {% else %}
                <div class="amenity-limit-info">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    You have reached the maximum limit of {{ max_amenities }} amenities for your venue.
                </div>
                {% endif %}

            </div>
        </div>
    </div>
</div>
{% endblock %}
