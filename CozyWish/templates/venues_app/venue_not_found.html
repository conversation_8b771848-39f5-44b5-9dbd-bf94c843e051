{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Venue Not Found" %} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 4rem 0;
        font-family: var(--font-primary);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .not-found-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 4rem 3rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        max-width: 600px;
        width: 100%;
    }

    /* Typography */
    .venues-wrapper h1 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1.5rem;
        font-size: 3rem;
    }

    .venues-wrapper p {
        color: black;
        font-family: var(--font-primary);
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.8;
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        transition: all 0.3s ease;
        border: 2px solid black;
        font-size: 1.1rem;
    }

    .venues-wrapper .btn-primary {
        background: black;
        color: white;
        border-color: black;
    }

    .venues-wrapper .btn-primary:hover {
        background: white;
        color: black;
        border-color: black;
    }

    /* Icon styling */
    .not-found-icon {
        font-size: 4rem;
        color: black;
        margin-bottom: 2rem;
        opacity: 0.7;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .venues-wrapper h1 {
            font-size: 2rem;
        }

        .venues-wrapper p {
            font-size: 1rem;
        }

        .not-found-card {
            padding: 3rem 2rem;
            margin: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="not-found-card">
                    <div class="not-found-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h1>{% trans "Venue Not Found" %}</h1>
                    <p>{% trans "The venue you're looking for is not available or may have been removed." %}</p>
                    <a href="{% url 'venues_app:venue_search' %}" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>{% trans "Browse Venues" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
