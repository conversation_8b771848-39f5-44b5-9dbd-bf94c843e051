{% extends 'base.html' %}

{% block title %}Edit {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    /* Form sections */
    .form-section {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
        font-size: 1.5rem;
    }

    /* Current status styling */
    .current-status {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .status-badge {
        background: white;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 0.25rem;
        font-weight: 500;
        font-size: 0.875rem;
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: black;
        font-family: var(--font-primary);
    }

    /* Form styling */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .venues-wrapper .form-label {
        font-weight: 500;
        color: black;
        margin-bottom: 0.5rem;
        font-family: var(--font-primary);
        display: block;
    }

    .venues-wrapper .form-control, .venues-wrapper .form-select {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--font-primary);
        background: white;
        color: black;
        transition: all 0.3s ease;
        width: 100%;
    }

    .venues-wrapper .form-control:focus, .venues-wrapper .form-select:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
        background: white;
        color: black;
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid black;
    }

    .venues-wrapper .btn-primary {
        background: black;
        color: white;
        border-color: black;
    }

    .venues-wrapper .btn-primary:hover {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary, .venues-wrapper .btn-outline-primary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary:hover, .venues-wrapper .btn-outline-primary:hover {
        background: black;
        color: white;
        border-color: black;
    }

    .btn-update {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
    }

    /* Action buttons */
    .action-buttons {
        text-align: center;
        margin-top: 2rem;
    }

    .action-buttons .btn {
        margin: 0 0.5rem;
    }

    /* Error and help text styling */
    .venues-wrapper .text-danger {
        color: black !important;
        font-weight: 500;
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }

    .venues-wrapper .help-text {
        color: black;
        opacity: 0.7;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .venues-wrapper .required {
        color: black;
        font-weight: 600;
    }

    .venues-wrapper .text-muted {
        color: black !important;
        opacity: 0.7;
    }

    /* Header styling */
    .venues-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
    }

    .venues-header h2 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .venues-header p {
        opacity: 0.8;
        margin-bottom: 0;
    }

    /* Checkbox and select styling */
    .venues-wrapper input[type="checkbox"] {
        border: 2px solid black;
        border-radius: 0.25rem;
    }

    .venues-wrapper input[type="checkbox"]:checked {
        background-color: black;
        border-color: black;
    }

    /* Image Upload Cards */
    .image-upload-card {
        border: 2px solid black;
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        background: white;
        transition: all 0.3s ease;
        position: relative;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .image-upload-card:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .image-preview-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        position: relative;
    }

    .image-preview {
        max-width: 100%;
        max-height: 150px;
        border-radius: 0.5rem;
        object-fit: cover;
    }

    .placeholder-content {
        color: #666;
    }

    .placeholder-content i {
        color: #999;
    }

    .image-upload-controls {
        margin-top: auto;
    }

    .image-upload-controls input[type="file"] {
        margin-bottom: 0.5rem;
    }

    .main-image-card {
        border-color: #ffc107;
        background: #fffbf0;
    }

    .main-image-card .placeholder-content i {
        color: #ffc107;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="venues-header d-flex align-items-center">
                    <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2>Edit Venue</h2>
                        <p>Update your venue information</p>
                    </div>
                </div>

                <!-- Current Status -->
                {% if venue %}
                <div class="current-status">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1">{{ venue.venue_name }}</h5>
                            <p class="mb-0 text-muted">Current Status:
                                <span class="status-badge">
                                    {{ venue.get_approval_status_display }}
                                </span>
                            </p>
                        </div>
                        <div>
                            <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Display form errors for debugging -->
                {% if form.errors %}
                    <div class="alert alert-danger">
                        <h5>Form Errors:</h5>
                        <ul>
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <li><strong>{{ field }}:</strong> {{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}

                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h3>

                        <div class="form-group">
                            <label class="form-label">
                                Venue Name <span class="required">*</span>
                            </label>
                            {{ form.venue_name|add_class:"form-control" }}
                            {% if form.venue_name.help_text %}
                                <div class="help-text">{{ form.venue_name.help_text }}</div>
                            {% endif %}
                            {% if form.venue_name.errors %}
                                <div class="text-danger">{{ form.venue_name.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                Short Description <span class="required">*</span>
                            </label>
                            {{ form.short_description|add_class:"form-control" }}
                            {% if form.short_description.help_text %}
                                <div class="help-text">{{ form.short_description.help_text }}</div>
                            {% endif %}
                            {% if form.short_description.errors %}
                                <div class="text-danger">{{ form.short_description.errors.0 }}</div>
                            {% endif %}
                        </div>


                    </div>

                    <!-- Location Information -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-map-marker-alt me-2"></i>Location
                        </h3>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        State <span class="required">*</span>
                                    </label>
                                    {{ form.state|add_class:"form-control" }}
                                    {% if form.state.errors %}
                                        <div class="text-danger">{{ form.state.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        County <span class="required">*</span>
                                    </label>
                                    {{ form.county|add_class:"form-control" }}
                                    {% if form.county.errors %}
                                        <div class="text-danger">{{ form.county.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                City <span class="required">*</span>
                            </label>
                            {{ form.city|add_class:"form-control" }}
                            {% if form.city.errors %}
                                <div class="text-danger">{{ form.city.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        Street Number <span class="required">*</span>
                                    </label>
                                    {{ form.street_number|add_class:"form-control" }}
                                    {% if form.street_number.errors %}
                                        <div class="text-danger">{{ form.street_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="form-group">
                                    <label class="form-label">
                                        Street Name <span class="required">*</span>
                                    </label>
                                    {{ form.street_name|add_class:"form-control" }}
                                    {% if form.street_name.errors %}
                                        <div class="text-danger">{{ form.street_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Categories and Tags -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-tags me-2"></i>Categories & Tags
                        </h3>

                        <div class="form-group">
                            <label class="form-label">
                                Categories <span class="required">*</span>
                            </label>
                            {{ form.categories }}
                            {% if form.categories.help_text %}
                                <div class="help-text">{{ form.categories.help_text }}</div>
                            {% endif %}
                            {% if form.categories.errors %}
                                <div class="text-danger">{{ form.categories.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                Tags
                            </label>
                            {{ form.tags|add_class:"form-control" }}
                            {% if form.tags.help_text %}
                                <div class="help-text">{{ form.tags.help_text }}</div>
                            {% endif %}
                            {% if form.tags.errors %}
                                <div class="text-danger">{{ form.tags.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-phone me-2"></i>Contact Information
                        </h3>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        Phone Number
                                    </label>
                                    {{ form.phone|add_class:"form-control" }}
                                    {% if form.phone.help_text %}
                                        <div class="help-text">{{ form.phone.help_text }}</div>
                                    {% endif %}
                                    {% if form.phone.errors %}
                                        <div class="text-danger">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        Email Address
                                    </label>
                                    {{ form.email|add_class:"form-control" }}
                                    {% if form.email.help_text %}
                                        <div class="help-text">{{ form.email.help_text }}</div>
                                    {% endif %}
                                    {% if form.email.errors %}
                                        <div class="text-danger">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                Website URL
                            </label>
                            {{ form.website_url|add_class:"form-control" }}
                            {% if form.website_url.help_text %}
                                <div class="help-text">{{ form.website_url.help_text }}</div>
                            {% endif %}
                            {% if form.website_url.errors %}
                                <div class="text-danger">{{ form.website_url.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="form-section">
                        <h3 class="section-title">
                            Additional Information
                        </h3>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        Latitude
                                    </label>
                                    {{ form.latitude|add_class:"form-control" }}
                                    {% if form.latitude.help_text %}
                                        <div class="help-text">{{ form.latitude.help_text }}</div>
                                    {% endif %}
                                    {% if form.latitude.errors %}
                                        <div class="text-danger">{{ form.latitude.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        Longitude
                                    </label>
                                    {{ form.longitude|add_class:"form-control" }}
                                    {% if form.longitude.help_text %}
                                        <div class="help-text">{{ form.longitude.help_text }}</div>
                                    {% endif %}
                                    {% if form.longitude.errors %}
                                        <div class="text-danger">{{ form.longitude.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                Opening Notes
                            </label>
                            {{ form.opening_notes|add_class:"form-control" }}
                            {% if form.opening_notes.help_text %}
                                <div class="help-text">{{ form.opening_notes.help_text }}</div>
                            {% endif %}
                            {% if form.opening_notes.errors %}
                                <div class="text-danger">{{ form.opening_notes.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>




                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button type="submit" class="btn btn-primary btn-update">
                            <i class="fas fa-save me-2"></i>Update Venue
                        </button>
                        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


{% endblock %}
