{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}{{ venue.venue_name }} - Venue Details - CozyWish Admin{% endblock %}

{% block venues_content %}
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'venues_app:admin_venue_approval_dashboard' %}" class="text-decoration-none">
                            Admin Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'venues_app:admin_venue_list' %}" class="text-decoration-none">
                            All Venues
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Venue Details</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{ venue.venue_name }}</h4>
                    <div>
                        <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        {% if venue.approval_status == 'pending' %}
                        <a href="{% url 'venues_app:admin_venue_approval' venue_id=venue.id %}" class="btn btn-primary">
                            <i class="fas fa-gavel"></i> Review & Approve
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>Venue Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Venue Name:</strong></td>
                                    <td>{{ venue.venue_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Service Provider:</strong></td>
                                    <td>
                                        {{ venue.service_provider.business_name }}<br>
                                        <small class="text-muted">{{ venue.service_provider.user.email }}</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Categories:</strong></td>
                                    <td>
                                        {% if venue.categories.exists %}
                                            {% for category in venue.categories.all %}
                                                <span class="badge bg-info me-1">{{ category.category_name }}</span>
                                            {% endfor %}
                                        {% else %}
                                            <span class="text-muted">Not set</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Description:</strong></td>
                                    <td>{{ venue.short_description|default:"No description provided"|linebreaks }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Location:</strong></td>
                                    <td>{{ venue.full_address }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        {% if venue.approval_status == 'pending' %}
                                            <span class="badge bg-warning text-dark">Pending</span>
                                        {% elif venue.approval_status == 'approved' %}
                                            <span class="badge bg-success">Approved</span>
                                        {% elif venue.approval_status == 'rejected' %}
                                            <span class="badge bg-danger">Rejected</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Visibility:</strong></td>
                                    <td>
                                        {% if venue.visibility == 'active' %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ venue.get_visibility_display }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Submitted:</strong></td>
                                    <td>{{ venue.created_at|date:"M d, Y H:i" }}</td>
                                </tr>
                                {% if venue.approved_at %}
                                <tr>
                                    <td><strong>Approved:</strong></td>
                                    <td>{{ venue.approved_at|date:"M d, Y H:i" }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-4">
                            <h5>Additional Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Venue Type:</strong></td>
                                    <td>{{ venue.get_venue_type_display|default:"Not specified" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>{{ venue.phone_number|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ venue.email|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Services:</strong></td>
                                    <td>{{ venue.services.count }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Images:</strong></td>
                                    <td>{{ venue.images.count }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Reviews:</strong></td>
                                    <td>{{ venue.reviews.count }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Average Rating:</strong></td>
                                    <td>
                                        {% if venue.reviews.count > 0 %}
                                            {{ venue.get_average_rating|floatformat:1 }}/5.0
                                        {% else %}
                                            <span class="text-muted">No reviews yet</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>FAQs:</strong></td>
                                    <td>{{ venue.faqs.count }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Services Section -->
                    {% if venue.services.exists %}
                    <div class="mt-4">
                        <h5>Services ({{ venue.services.count }})</h5>
                        <div class="row">
                            {% for service in venue.services.all %}
                            <div class="col-md-6 mb-3">
                                <div class="border rounded p-3">
                                    <h6 class="mb-2">{{ service.service_title }}</h6>
                                    <p class="text-muted small mb-2">{{ service.short_description|default:"No description" }}</p>
                                    <div class="d-flex justify-content-between">
                                        <span class="text-success">
                                            <strong>${{ service.price_min }}{% if service.price_max and service.price_max != service.price_min %} - ${{ service.price_max }}{% endif %}</strong>
                                        </span>
                                        <span class="text-muted small">{{ service.duration_minutes }} min</span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Images Section -->
                    {% if venue.images.exists %}
                    <div class="mt-4">
                        <h5>Images ({{ venue.images.count }})</h5>
                        <div class="row">
                            {% for image in venue.images.all %}
                            <div class="col-md-3 mb-3">
                                <div class="position-relative">
                                    <img src="{{ image.image.url }}" class="img-fluid rounded" alt="Venue Image" style="height: 150px; width: 100%; object-fit: cover;">
                                    {% if image.is_primary %}
                                        <span class="position-absolute top-0 start-0 badge bg-primary m-2">Primary</span>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Admin Notes Section -->
                    {% if venue.approval_status == 'rejected' and venue.admin_notes %}
                    <div class="mt-4">
                        <h5>Rejection Reason</h5>
                        <div class="alert alert-danger">
                            {{ venue.admin_notes|linebreaks }}
                        </div>
                    </div>
                    {% elif venue.admin_notes %}
                    <div class="mt-4">
                        <h5>Admin Notes</h5>
                        <div class="alert alert-info">
                            {{ venue.admin_notes|linebreaks }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}