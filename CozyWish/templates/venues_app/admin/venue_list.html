{% extends 'venues_app/base_venues.html' %}

{% block title %}Admin - Venues - CozyWish{% endblock %}

{% block venues_content %}
    <div class="row mb-4">
        <div class="col-12">
            <h1>Venue Management</h1>
            <p class="lead">Manage all venues on the CozyWish platform</p>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Filter Venues</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="status" class="form-label">Approval Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                        <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>Approved</option>
                        <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejected</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">Apply Filters</button>
                    <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-outline-secondary">Clear Filters</a>
                </div>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Venues</h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Owner</th>
                            <th>Category</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for venue in page_obj %}
                        <tr>
                            <td>{{ venue.name }}</td>
                            <td>{{ venue.owner.email }}</td>
                            <td>{{ venue.category.name|default:"Not set" }}</td>
                            <td>{{ venue.city }}, {{ venue.state }}</td>
                            <td>
                                <span class="badge {% if venue.approval_status == 'approved' %}badge-approved{% elif venue.approval_status == 'pending' %}badge-pending{% else %}badge-rejected{% endif %}">
                                    {{ venue.get_approval_status_display }}
                                </span>
                            </td>
                            <td>{{ venue.created_at|date:"M d, Y" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'venues_app:admin_venue_detail' venue_id=venue.id %}" class="btn btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if venue.approval_status == 'pending' %}
                                    <a href="{% url 'venues_app:admin_venue_approval' venue_id=venue.id %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-check-circle"></i>
                                    </a>
                                    {% endif %}
                                    {% if venue.approval_status == 'approved' %}
                                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-outline-secondary" target="_blank">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="alert alert-info">
                <p class="mb-0">No venues found matching your criteria.</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-store"></i>
                    <div class="stat-number">{{ total_venues|default:page_obj.paginator.count }}</div>
                    <div class="stat-label">Total Venues</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-clock"></i>
                    <div class="stat-number">{{ pending_venues|default:"--" }}</div>
                    <div class="stat-label">Pending Approval</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-check-circle"></i>
                    <div class="stat-number">{{ approved_venues|default:"--" }}</div>
                    <div class="stat-label">Approved Venues</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-times-circle"></i>
                    <div class="stat-number">{{ rejected_venues|default:"--" }}</div>
                    <div class="stat-label">Rejected Venues</div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
