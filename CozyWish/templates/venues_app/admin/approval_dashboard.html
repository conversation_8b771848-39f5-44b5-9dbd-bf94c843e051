{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}Venue Approval Dashboard - CozyWish Admin{% endblock %}

{% block venues_content %}
    <div class="row mb-4">
        <div class="col-12">
            <h1>Venue Approval Dashboard</h1>
            <p class="lead">Manage venue approvals and monitor submission statistics</p>
        </div>
    </div>

    <!-- Dashboard Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-clock"></i>
                    <div class="stat-number">{{ pending_count|default:0 }}</div>
                    <div class="stat-label">Pending Approval</div>
                    <div class="mt-3">
                        <a href="{% url 'venues_app:admin_pending_venues' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>Review
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-check"></i>
                    <div class="stat-number">{{ approved_count|default:0 }}</div>
                    <div class="stat-label">Approved Venues</div>
                    <div class="mt-3">
                        <a href="{% url 'venues_app:admin_venue_list' %}?status=approved" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list me-1"></i>View All
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-times"></i>
                    <div class="stat-number">{{ rejected_count|default:0 }}</div>
                    <div class="stat-label">Rejected Venues</div>
                    <div class="mt-3">
                        <a href="{% url 'venues_app:admin_venue_list' %}?status=rejected" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list me-1"></i>View All
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-building"></i>
                    <div class="stat-number">{{ total_count|default:0 }}</div>
                    <div class="stat-label">Total Venues</div>
                    <div class="mt-3">
                        <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list me-1"></i>View All
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'venues_app:admin_pending_venues' %}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <div class="fw-bold">Review Pending</div>
                                <small class="text-muted">Review venues awaiting approval</small>
                            </a>
                        </div>

                        <div class="col-md-3 mb-3">
                            <a href="{% url 'venues_app:admin_venue_list' %}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-list fa-2x mb-2"></i>
                                <div class="fw-bold">All Venues</div>
                                <small class="text-muted">Browse all venue submissions</small>
                            </a>
                        </div>

                        <div class="col-md-3 mb-3">
                            <a href="{% url 'venues_app:admin_category_list' %}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-tags fa-2x mb-2"></i>
                                <div class="fw-bold">Categories</div>
                                <small class="text-muted">Manage venue categories</small>
                            </a>
                        </div>

                        <div class="col-md-3 mb-3">
                            <a href="{% url 'venues_app:admin_flagged_venues' %}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-flag fa-2x mb-2"></i>
                                <div class="fw-bold">Flagged Venues</div>
                                <small class="text-muted">Review flagged content</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_venues %}
                        {% for venue in recent_venues %}
                        <div class="d-flex align-items-center py-3 {% if not forloop.last %}border-bottom{% endif %}">
                            <div class="me-3">
                                <i class="fas fa-building fa-lg"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold">{{ venue.name }}</div>
                                <div class="text-muted small">
                                    Submitted by {{ venue.owner.email }} • {{ venue.created_at|timesince }} ago
                                </div>
                            </div>
                            <div>
                                <a href="{% url 'venues_app:admin_venue_detail' venue_id=venue.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-inbox fa-2x mb-3"></i>
                            <p>No recent activity</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
