{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}Pending Venues - CozyWish Admin{% endblock %}

{% block venues_content %}
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'venues_app:admin_venue_approval_dashboard' %}" class="text-decoration-none">
                            Admin Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Pending Venues</li>
                </ol>
            </nav>

            <h1>Pending Venues</h1>
            <p class="lead">Review and approve venue submissions</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body stat-card">
                    <div class="stat-number">{{ page_obj.paginator.count }}</div>
                    <div class="stat-label">Pending Venues</div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body stat-card">
                    <div class="stat-number">{{ page_obj.paginator.num_pages }}</div>
                    <div class="stat-label">Total Pages</div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body stat-card">
                    <div class="stat-number">{{ page_obj.number }}</div>
                    <div class="stat-label">Current Page</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Venues List -->
    {% if pending_venues %}
        {% for venue in pending_venues %}
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        {% if venue.get_primary_image %}
                            <img src="{{ venue.get_primary_image }}" alt="{{ venue.name }}" class="img-fluid rounded">
                        {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 100px;">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                        {% endif %}
                    </div>

                    <div class="col-md-8">
                        <h5>{{ venue.name }}</h5>
                        <div class="mb-2">
                            <div><i class="fas fa-user me-1"></i>{{ venue.owner.email }}</div>
                            <div><i class="fas fa-map-marker-alt me-1"></i>{{ venue.city }}, {{ venue.state }}</div>
                            <div><i class="fas fa-calendar me-1"></i>Submitted {{ venue.created_at|date:"M d, Y" }}</div>
                            {% if venue.category %}
                            <div><i class="fas fa-tag me-1"></i>{{ venue.category.name }}</div>
                            {% endif %}
                        </div>

                        {% if venue.about %}
                        <p class="text-muted mb-0">{{ venue.about|truncatewords:20 }}</p>
                        {% endif %}
                    </div>

                    <div class="col-md-2 text-end">
                        <div class="mb-2">
                            <span class="badge badge-pending">{{ venue.get_approval_status_display }}</span>
                        </div>
                        <a href="{% url 'venues_app:admin_venue_detail' venue_id=venue.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>Review
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Pending venues pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="text-center py-5">
            <i class="fas fa-check-circle fa-3x mb-3"></i>
            <h3>No Pending Venues</h3>
            <p class="text-muted">All venues have been reviewed. Great job!</p>
            <a href="{% url 'venues_app:admin_venue_approval_dashboard' %}" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    {% endif %}
{% endblock %}
