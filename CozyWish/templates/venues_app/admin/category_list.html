{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}Category Management - CozyWish Admin{% endblock %}

{% block venues_content %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Category Management</h4>
                    <a href="{% url 'venues_app:admin_category_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Category
                    </a>
                </div>
                <div class="card-body">
                    {% if categories %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Venues Count</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for category in categories %}
                                    <tr>
                                        <td>
                                            <strong>{{ category.name }}</strong>
                                        </td>
                                        <td>{{ category.description|truncatechars:50 }}</td>
                                        <td>
                                            {% if category.is_active %}
                                                <span class="badge badge-approved">Active</span>
                                            {% else %}
                                                <span class="badge badge-rejected">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ category.venues.count }}</td>
                                        <td>{{ category.created_at|date:"M d, Y" }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'venues_app:admin_category_edit' category.id %}"
                                                   class="btn btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'venues_app:admin_category_toggle_status' category.id %}"
                                                   class="btn btn-outline-secondary">
                                                    {% if category.is_active %}
                                                        <i class="fas fa-pause"></i>
                                                    {% else %}
                                                        <i class="fas fa-play"></i>
                                                    {% endif %}
                                                </a>
                                                <a href="{% url 'venues_app:admin_category_delete' category.id %}"
                                                   class="btn btn-outline-secondary"
                                                   onclick="return confirm('Are you sure you want to delete this category?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-tags fa-3x mb-3"></i>
                            <h5>No categories found</h5>
                            <p class="text-muted">Start by creating your first category.</p>
                            <a href="{% url 'venues_app:admin_category_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create First Category
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
