{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}Flagged Venues - CozyWish Admin{% endblock %}

{% block venues_content %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Flagged Venues</h4>
                </div>
                <div class="card-body">
                    {% if flagged_venues %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Venue</th>
                                        <th>Flagged By</th>
                                        <th>Reason</th>
                                        <th>Status</th>
                                        <th>Flagged Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for flag in flagged_venues %}
                                    <tr>
                                        <td>
                                            <strong>{{ flag.venue.name }}</strong><br>
                                            <small class="text-muted">{{ flag.venue.owner.email }}</small>
                                        </td>
                                        <td>{{ flag.flagged_by.get_full_name|default:flag.flagged_by.email }}</td>
                                        <td>{{ flag.reason|truncatechars:100 }}</td>
                                        <td>
                                            {% if flag.status == 'pending' %}
                                                <span class="badge badge-pending">Pending</span>
                                            {% elif flag.status == 'reviewed' %}
                                                <span class="badge badge-approved">Reviewed</span>
                                            {% elif flag.status == 'resolved' %}
                                                <span class="badge badge-approved">Resolved</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ flag.created_at|date:"M d, Y H:i" }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'venues_app:admin_venue_detail' venue_id=flag.venue.id %}"
                                                   class="btn btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-flag fa-3x mb-3"></i>
                            <h5>No flagged venues</h5>
                            <p class="text-muted">All venues are in good standing.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
