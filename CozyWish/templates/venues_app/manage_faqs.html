{% extends 'base.html' %}
{% load widget_tweaks i18n %}

{% block title %}Manage FAQs - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: black;
        font-family: var(--font-primary);
    }

    /* List group styling */
    .venues-wrapper .list-group {
        border: 2px solid black;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .venues-wrapper .list-group-item {
        background: white;
        border: none;
        border-bottom: 1px solid black;
        padding: 1.5rem;
        color: black;
    }

    .venues-wrapper .list-group-item:last-child {
        border-bottom: none;
    }

    .venues-wrapper .list-group-item strong {
        color: black;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
    }

    /* Card styling */
    .venues-wrapper .card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .venues-wrapper .card-body {
        padding: 2.5rem;
    }

    .venues-wrapper .card-title {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1.5rem;
    }

    /* Form styling */
    .venues-wrapper .form-label {
        font-weight: 500;
        color: black;
        margin-bottom: 0.5rem;
        font-family: var(--font-primary);
    }

    .venues-wrapper .form-control {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--font-primary);
        background: white;
        color: black;
        transition: all 0.3s ease;
    }

    .venues-wrapper .form-control:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
        background: white;
        color: black;
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid black;
    }

    .venues-wrapper .btn-primary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-primary:hover {
        background: #f8f9fa;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary:hover {
        background: #f8f9fa;
        color: black;
        border-color: black;
    }

    /* Error styling */
    .venues-wrapper .invalid-feedback {
        color: black;
        font-weight: 500;
        margin-top: 0.5rem;
    }

    .venues-wrapper .text-muted {
        color: black !important;
        opacity: 0.7;
    }

    /* No FAQs message */
    .no-faqs-message {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .no-faqs-message i {
        font-size: 3rem;
        color: black;
        opacity: 0.7;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <h2>{% trans "Manage FAQs" %}</h2>

        <div class="mb-4">
            {% if faqs %}
                <ul class="list-group">
                    {% for faq in faqs %}
                        <li class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <strong>{{ faq.order }}. {{ faq.question }}</strong>
                                <p class="mb-0 small text-muted">{{ faq.answer }}</p>
                            </div>
                            <div class="btn-group ms-3" role="group">
                                <a href="{% url 'venues_app:edit_faq' faq.id %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="{% url 'venues_app:delete_faq' faq.id %}" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            {% else %}
                <div class="no-faqs-message">
                    <i class="fas fa-question-circle"></i>
                    <p>{% trans "No FAQs yet. Add your first FAQ below." %}</p>
                </div>
            {% endif %}
        </div>

        {% if can_add_faq %}
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">{% trans "Add FAQ" %}</h5>
                <form method="post" novalidate>
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label" for="{{ form.question.id_for_label }}">{% trans "Question" %}</label>
                        {{ form.question|add_class:'form-control' }}
                        {% for error in form.question.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="{{ form.answer.id_for_label }}">{% trans "Answer" %}</label>
                        {{ form.answer|add_class:'form-control' }}
                        {% for error in form.answer.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add FAQ" %}
                    </button>
                </form>
            </div>
        </div>
        {% else %}
        <div class="card mb-4">
            <div class="card-body text-center">
                <i class="fas fa-info-circle fa-2x mb-3" style="color: black; opacity: 0.7;"></i>
                <p class="text-muted">{% blocktrans with max=max_faqs %}Maximum {{ max }} FAQs reached.{% endblocktrans %}</p>
            </div>
        </div>
        {% endif %}

        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Venue Management" %}
        </a>
    </div>
</div>
{% endblock %}
