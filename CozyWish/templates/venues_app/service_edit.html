{% extends 'base.html' %}

{% block title %}Edit Service - {{ object.service_title }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    /* Venues card - clean white with black border */
    .venues-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .venues-card-body {
        padding: 3rem 2.5rem;
    }

    .venues-card-header {
        background: white;
        color: white;
        padding: 1.5rem 2.5rem;
        border-radius: 0.8rem 0.8rem 0 0;
        border-bottom: 2px solid black;
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: black;
        font-family: var(--font-primary);
    }

    /* Form styling */
    .venues-wrapper .form-label {
        font-weight: 500;
        color: black;
        margin-bottom: 0.5rem;
        font-family: var(--font-primary);
    }

    .venues-wrapper .form-control {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--font-primary);
        background: white;
        color: black;
        transition: all 0.3s ease;
    }

    .venues-wrapper .form-control:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
        background: white;
        color: black;
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid black;
    }

    .venues-wrapper .btn-primary {
        background: black;
        color: white;
        border-color: black;
    }

    .venues-wrapper .btn-primary:hover {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-secondary:hover {
        background: black;
        color: white;
        border-color: black;
    }

    /* Error styling */
    .venues-wrapper .invalid-feedback {
        color: black;
        font-weight: 500;
        margin-top: 0.5rem;
    }

    .venues-wrapper .text-danger {
        color: black !important;
    }

    .venues-wrapper .text-muted {
        color: black !important;
        opacity: 0.7;
    }

    /* Badge styling */
    .venues-wrapper .badge {
        background: black !important;
        color: white;
        border: 1px solid black;
        font-weight: 500;
    }

    /* Header styling */
    .venues-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
    }

    .venues-header h2 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .venues-header p {
        opacity: 0.8;
        margin-bottom: 0;
    }

    /* Alert styling */
    .venues-wrapper .alert {
        background: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        color: black;
        padding: 1.5rem;
    }

    /* Border styling */
    .venues-wrapper .border-end {
        border-right: 2px solid black !important;
    }

    /* Checkbox styling */
    .venues-wrapper .form-check-input {
        border: 2px solid black;
        border-radius: 0.25rem;
        background-color: white;
        width: 1.2em;
        height: 1.2em;
        margin-right: 0.5rem;
    }

    .venues-wrapper .form-check-input:checked {
        background-color: black;
        border-color: black;
    }

    .venues-wrapper .form-check-input:focus {
        border-color: black;
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
    }

    .venues-wrapper .form-check-label {
        color: black;
        font-family: var(--font-primary);
        font-weight: 500;
        margin-left: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <div class="row">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="venues-header d-flex align-items-center">
                    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2>Edit Service</h2>
                        <p>Update service details for {{ venue.venue_name }}</p>
                    </div>
                </div>

                <!-- Service Form -->
                <div class="venues-card">
                    <div class="venues-card-body">
                        <form method="post" novalidate>
                            {% csrf_token %}

                            <!-- Service Title -->
                            <div class="mb-3">
                                <label for="{{ form.service_title.id_for_label }}" class="form-label">
                                    Service Name <span class="text-danger">*</span>
                                </label>
                                {{ form.service_title|add_class:"form-control" }}
                                {% if form.service_title.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.service_title.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.service_title.help_text %}
                                    <small class="form-text text-muted">{{ form.service_title.help_text }}</small>
                                {% endif %}
                            </div>

                            <!-- Service Description -->
                            <div class="mb-3">
                                <label for="{{ form.short_description.id_for_label }}" class="form-label">
                                    Service Description <span class="text-danger">*</span>
                                </label>
                                {{ form.short_description|add_class:"form-control" }}
                                {% if form.short_description.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.short_description.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.short_description.help_text %}
                                    <small class="form-text text-muted">{{ form.short_description.help_text }}</small>
                                {% endif %}
                            </div>

                            <!-- Pricing Section -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.price_min.id_for_label }}" class="form-label">
                                            Minimum Price ($) <span class="text-danger">*</span>
                                        </label>
                                        {{ form.price_min|add_class:"form-control" }}
                                        {% if form.price_min.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.price_min.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        {% if form.price_min.help_text %}
                                            <small class="form-text text-muted">{{ form.price_min.help_text }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.price_max.id_for_label }}" class="form-label">
                                            Maximum Price ($)
                                        </label>
                                        {{ form.price_max|add_class:"form-control" }}
                                        {% if form.price_max.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.price_max.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        {% if form.price_max.help_text %}
                                            <small class="form-text text-muted">{{ form.price_max.help_text }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Duration -->
                            <div class="mb-3">
                                <label for="{{ form.duration_minutes.id_for_label }}" class="form-label">
                                    Duration (minutes) <span class="text-danger">*</span>
                                </label>
                                {{ form.duration_minutes|add_class:"form-control" }}
                                {% if form.duration_minutes.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.duration_minutes.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.duration_minutes.help_text %}
                                    <small class="form-text text-muted">{{ form.duration_minutes.help_text }}</small>
                                {% endif %}
                            </div>

                            <!-- Service Availability Toggle -->
                            <div class="mb-4">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        <strong>Service Available to Customers</strong>
                                    </label>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.is_active.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    {% if form.is_active.help_text %}
                                        <small class="form-text text-muted d-block mt-1">{{ form.is_active.help_text }}</small>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Form Errors -->
                            {% if form.non_field_errors %}
                                <div class="alert">
                                    {% for error in form.non_field_errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <!-- Submit Buttons -->
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'venues_app:manage_services' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Update Service
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">

                <!-- Current Service Info -->
                <div class="venues-card mb-4">
                    <div class="venues-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-spa me-2"></i>Current Service
                        </h5>
                    </div>
                    <div class="venues-card-body">
                        <h6 class="mb-2">{{ object.service_title }}</h6>
                        <p class="text-muted small mb-3">{{ object.short_description|truncatewords:15 }}</p>

                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <div class="border-end">
                                    <div class="fw-bold">{{ object.price_display }}</div>
                                    <small class="text-muted">Current Price</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="fw-bold">{{ object.duration_display }}</div>
                                <small class="text-muted">Current Duration</small>
                            </div>
                        </div>

                        <div class="text-center">
                            {% if object.is_active %}
                                <span class="badge">Active</span>
                            {% else %}
                                <span class="badge">Inactive</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Guidelines -->
                <div class="venues-card mb-4">
                    <div class="venues-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>Update Guidelines
                        </h5>
                    </div>
                    <div class="venues-card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check-circle me-2"></i>
                                Review pricing against competitors
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle me-2"></i>
                                Update descriptions based on feedback
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle me-2"></i>
                                Adjust duration for accuracy
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Changes may affect existing bookings
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Venue Info -->
                <div class="venues-card">
                    <div class="venues-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>Venue Information
                        </h5>
                    </div>
                    <div class="venues-card-body">
                        <h6 class="mb-2">{{ venue.venue_name }}</h6>
                        <p class="text-muted mb-3">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ venue.city }}, {{ venue.state }}
                        </p>
                        <p class="mb-3">
                            <i class="fas fa-spa me-1"></i>
                            {{ venue.services.count }}/7 services
                        </p>
                        <a href="{% url 'venues_app:venue_edit' %}" class="btn btn-outline-secondary btn-sm w-100">
                            <i class="fas fa-edit me-2"></i>Edit Venue
                        </a>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}
