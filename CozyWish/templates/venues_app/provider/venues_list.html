{% extends 'venues_app/base_venues.html' %}

{% block title %}My Venues - CozyWish{% endblock %}

{% block venues_content %}
    <div class="row mb-4">
        <div class="col-12">
            <h1>My Venues</h1>
            <p class="lead">Manage your venues and services on CozyWish</p>
        </div>
    </div>

    <!-- Quick Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title mb-3">
                        <i class="fas fa-tachometer-alt me-2"></i>Quick Actions
                    </h6>
                    <div class="d-flex flex-wrap gap-2">
                        {% if not venues %}
                        <a href="{% url 'venues_app:venue_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Your Venue
                        </a>
                        {% endif %}
                        <a href="{% url 'discount_app:provider_discount_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-percentage me-2"></i>Manage Discounts
                        </a>
                        <a href="{% url 'discount_app:create_service_discount' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-tag me-2"></i>Create Service Discount
                        </a>
                        <a href="{% url 'discount_app:create_venue_discount' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-tags me-2"></i>Create Venue Discount
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card h-100 mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-store"></i>
                    <div class="stat-number">{{ venues.count }}</div>
                    <div class="stat-label">Total Venues</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card h-100 mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-spa"></i>
                    <div class="stat-number">{{ total_services|default:"0" }}</div>
                    <div class="stat-label">Total Services</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card h-100 mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-calendar-check"></i>
                    <div class="stat-number">{{ total_bookings|default:"0" }}</div>
                    <div class="stat-label">Total Bookings</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card h-100 mb-4">
                <div class="card-body stat-card">
                    <i class="fas fa-star"></i>
                    <div class="stat-number">{{ avg_rating|default:"0.0" }}</div>
                    <div class="stat-label">Avg. Rating</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Venues List -->
    <div id="venues-skeleton" class="row" aria-hidden="true">
        {% for i in "123"|make_list %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 placeholder-glow">
                <div class="ratio ratio-16x9">
                    <span class="placeholder w-100 h-100"></span>
                </div>
                <div class="card-body">
                    <h5 class="card-title"><span class="placeholder col-6"></span></h5>
                    <p class="card-text"><span class="placeholder col-7"></span></p>
                    <p class="card-text"><span class="placeholder col-4"></span></p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <div id="venues-list" class="row d-none">
        {% if venues %}
            {% for venue in venues %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="position-relative">
                        <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.name }}">
                        <div class="position-absolute top-0 end-0 m-2 approval-overlay">
                            <span class="badge {% if venue.approval_status == 'approved' %}badge-approved{% elif venue.approval_status == 'pending' %}badge-pending{% else %}badge-rejected{% endif %}">
                                {{ venue.get_approval_status_display }}
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">{{ venue.name }}</h5>
                        <div class="mb-2">
                            <span class="fw-bold">{{ venue.get_average_rating }}★</span>
                            <span class="text-muted">({{ venue.get_review_count }})</span>
                        </div>
                        <p class="text-muted mb-2">
                            <i class="fas fa-map-marker-alt me-1"></i> {{ venue.city }}, {{ venue.state }}
                        </p>
                        <p class="text-muted small mb-3">
                            <i class="fas fa-spa me-1"></i> {{ venue.services.count }} services
                        </p>

                        {% if venue.approval_status == 'rejected' %}
                        <div class="alert alert-danger mt-3 mb-3">
                            <strong>Rejection Reason:</strong> {{ venue.rejection_reason }}
                        </div>
                        {% endif %}

                        <div class="d-grid gap-2">
                            <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i>Manage
                            </a>
                            {% if venue.approval_status == 'approved' %}
                            <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-outline-secondary">
                                <i class="fas fa-eye me-2"></i>Public View
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="alert alert-info">
                    <p class="mb-0">You don't have any venues yet. <a href="{% url 'venues_app:venue_create' %}" class="alert-link">Create your first venue</a> to get started.</p>
                </div>
            </div>
        {% endif %}
    </div>

    {% if page_obj.has_other_pages %}
    <nav aria-label="Venue pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled"><span class="page-link">&laquo;</span></li>
            {% endif %}
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                {% endif %}
            {% endfor %}
            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled"><span class="page-link">&raquo;</span></li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
{% endblock %}

{% block venues_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function(){
  const skeleton = document.getElementById('venues-skeleton');
  const list = document.getElementById('venues-list');
  if(list){
    list.classList.remove('d-none');
  }
  if(skeleton){
    skeleton.remove();
  }
});
</script>
{% endblock %}
