{% extends 'venues_app/base_venues.html' %}

{% block title %}{{ title }} - CozyWish{% endblock %}

{% load widget_tweaks %}

{% block venues_content %}
    <div class="row mb-4">
        <div class="col-12">
            <h1>{{ title }}</h1>
            <p class="lead">{% if 'Edit' in title %}Update your venue information{% else %}Add a new venue to your profile{% endif %}</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Venue Information</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="{{ form.venue_name.id_for_label }}" class="form-label">Venue Name</label>
                            <input type="text" name="{{ form.venue_name.name }}" id="{{ form.venue_name.id_for_label }}" class="form-control {% if form.venue_name.errors %}is-invalid{% endif %}" value="{{ form.venue_name.value|default:'' }}">
                            {% if form.venue_name.errors %}
                            <div class="invalid-feedback">
                                {{ form.venue_name.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.categories.id_for_label }}" class="form-label">Categories</label>
                            {{ form.categories }}
                            {% if form.categories.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.categories.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.tags.id_for_label }}" class="form-label">Tags</label>
                            {{ form.tags|add_class:"form-select" }}
                            {% if form.tags.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.tags.errors }}
                            </div>
                            {% endif %}
                            <div class="form-text">Hold Ctrl (or Cmd on Mac) to select multiple tags.</div>
                        </div>

                        <!-- Venue type field removed as it's not in the VenueForm -->

                        <h5 class="mt-4 mb-3">Location</h5>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.state.id_for_label }}" class="form-label">State</label>
                                {{ form.state|add_class:"form-select" }}
                                {% if form.state.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.state.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.county.id_for_label }}" class="form-label">County</label>
                                {{ form.county|add_class:"form-control" }}
                                {% if form.county.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.county.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.city.id_for_label }}" class="form-label">City</label>
                            {{ form.city|add_class:"form-control" }}
                            {% if form.city.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.city.errors }}
                            </div>
                            {% endif %}
                            <input type="hidden" name="us_city_id" id="id_us_city_id">
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.street_number.id_for_label }}" class="form-label">Street Number</label>
                                <input type="text" name="{{ form.street_number.name }}" id="{{ form.street_number.id_for_label }}" class="form-control {% if form.street_number.errors %}is-invalid{% endif %}" value="{{ form.street_number.value|default:'' }}">
                                {% if form.street_number.errors %}
                                <div class="invalid-feedback">
                                    {{ form.street_number.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-8 mb-3">
                                <label for="{{ form.street_name.id_for_label }}" class="form-label">Street Name</label>
                                <input type="text" name="{{ form.street_name.name }}" id="{{ form.street_name.id_for_label }}" class="form-control {% if form.street_name.errors %}is-invalid{% endif %}" value="{{ form.street_name.value|default:'' }}">
                                {% if form.street_name.errors %}
                                <div class="invalid-feedback">
                                    {{ form.street_name.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.short_description.id_for_label }}" class="form-label">About</label>
                            <textarea name="{{ form.short_description.name }}" id="{{ form.short_description.id_for_label }}" class="form-control {% if form.short_description.errors %}is-invalid{% endif %}" rows="5">{{ form.short_description.value|default:'' }}</textarea>
                            {% if form.short_description.errors %}
                            <div class="invalid-feedback">
                                {{ form.short_description.errors }}
                            </div>
                            {% endif %}
                        </div>

                        <h5 class="mt-4 mb-3">Main Image</h5>

                        <div class="mb-3">
                            <label for="{{ form.main_image.id_for_label }}" class="form-label">Featured Image</label>
                            <input type="file" name="{{ form.main_image.name }}" id="{{ form.main_image.id_for_label }}" class="form-control {% if form.main_image.errors %}is-invalid{% endif %}" accept="image/jpeg,image/jpg,image/png">
                            {% if form.main_image.errors %}
                            <div class="invalid-feedback">
                                {{ form.main_image.errors }}
                            </div>
                            {% endif %}
                            <div class="form-text">Upload a high-quality featured image for your venue (JPG/PNG, max 5MB)</div>
                            {% if form.instance.main_image %}
                            <div class="mt-2">
                                <small class="text-muted">Current image:</small><br>
                                <img src="{{ form.instance.main_image.url }}" alt="Current main image" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{% url 'venues_app:provider_venues' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Venue
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Guidelines</h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">Venue Information</h6>
                    <ul class="list-unstyled mb-4">
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> Provide an accurate and descriptive venue name
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> Select the most appropriate category for your venue
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> Add relevant tags to help customers find your venue
                        </li>
                        <li>
                            <i class="fas fa-check-circle me-2"></i> Write a detailed description of your venue and services
                        </li>
                    </ul>

                    <h6 class="mb-3">Location Information</h6>
                    <ul class="list-unstyled mb-4">
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> Enter your complete and accurate address
                        </li>
                        <li>
                            <i class="fas fa-check-circle me-2"></i> Make sure your location is easy to find for customers
                        </li>
                    </ul>

                    <h6 class="mb-3">Next Steps</h6>
                    <p>After saving your venue, you'll need to:</p>
                    <ol class="mb-0">
                        <li class="mb-2">Add additional venue images (up to 7 total)</li>
                        <li class="mb-2">Set your opening hours</li>
                        <li class="mb-2">Add services you offer</li>
                        <li>Add team members (optional)</li>
                    </ol>

                    {% if form.instance.pk %}
                    <div class="mt-4">
                        <a href="{% url 'venues_app:manage_venue_images' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-images me-2"></i>Manage Images
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block venues_extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // County and city are now text inputs, no dynamic loading needed
    });
</script>
{% endblock %}