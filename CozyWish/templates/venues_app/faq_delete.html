{% extends 'base.html' %}
{% load widget_tweaks i18n %}

{% block title %}Delete FAQ - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Venues App - Black & White Design */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    }

    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    .venues-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
    }

    .venues-header h2 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin: 0;
    }

    .venues-header p {
        color: #666;
        margin: 0.5rem 0 0 0;
    }

    .delete-confirmation {
        background: white;
        border: 2px solid #dc3545;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .faq-preview {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }

    .faq-preview h5 {
        color: black;
        margin-bottom: 0.5rem;
    }

    .faq-preview p {
        color: #666;
        margin: 0;
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .btn-outline-secondary {
        border: 2px solid black;
        color: black;
        background: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-family: var(--font-primary);
    }

    .btn-outline-secondary:hover {
        background-color: #f8f9fa;
        color: black;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: flex-start;
        margin-top: 2rem;
    }

    .warning-icon {
        color: #dc3545;
        font-size: 3rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="venues-header d-flex align-items-center">
                    <a href="{% url 'venues_app:manage_faqs' %}" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2>Delete FAQ</h2>
                        <p>Confirm deletion of FAQ for {{ venue.venue_name }}</p>
                    </div>
                </div>

                <!-- Delete Confirmation -->
                <div class="delete-confirmation text-center">
                    <i class="fas fa-exclamation-triangle warning-icon"></i>
                    <h4 class="mb-3">Are you sure you want to delete this FAQ?</h4>
                    <p class="text-muted mb-4">This action cannot be undone.</p>

                    <!-- FAQ Preview -->
                    <div class="faq-preview text-start">
                        <h5>{{ faq.question }}</h5>
                        <p>{{ faq.answer }}</p>
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Action Buttons -->
                        <div class="action-buttons justify-content-center">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>Yes, Delete FAQ
                            </button>
                            <a href="{% url 'venues_app:manage_faqs' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}
