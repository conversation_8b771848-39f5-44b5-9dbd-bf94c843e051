{% extends 'base.html' %}

{% block title %}{{ service.title }} - {{ venue.name }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>

<style>
    /* CozyWish Venues App - Black & White Design */
    /* Matching homepage and accounts_app design with clean typography and spacing */

    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Venues wrapper - clean white background */
    .venues-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--font-primary);
    }

    /* Venues card - clean white with black border */
    .venues-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .venues-card-body {
        padding: 2.5rem;
    }

    .venues-card-header {
        background: white;
        color: white;
        padding: 1.5rem 2.5rem;
        border-radius: 0.8rem 0.8rem 0 0;
        border-bottom: 2px solid black;
    }

    /* Typography */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6 {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
        margin-bottom: 1rem;
    }

    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div {
        color: black;
        font-family: var(--font-primary);
    }

    /* Button styling */
    .venues-wrapper .btn {
        font-family: var(--font-primary);
        font-weight: 500;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid black;
    }

    .venues-wrapper .btn-primary {
        background: black;
        color: white;
        border-color: black;
    }

    .venues-wrapper .btn-primary:hover {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-primary {
        background: white;
        color: black;
        border-color: black;
    }

    .venues-wrapper .btn-outline-primary:hover {
        background: black;
        color: white;
        border-color: black;
    }

    /* Breadcrumb styling */
    .venues-wrapper .breadcrumb {
        background: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 1rem;
    }

    .venues-wrapper .breadcrumb-item a {
        color: black;
        text-decoration: none;
    }

    .venues-wrapper .breadcrumb-item a:hover {
        color: black;
        opacity: 0.7;
    }

    .venues-wrapper .breadcrumb-item.active {
        color: black;
        font-weight: 500;
    }

    /* Badge styling */
    .venues-wrapper .badge {
        background: black !important;
        color: white;
        border: 1px solid black;
        font-weight: 500;
    }

    /* Text styling */
    .venues-wrapper .text-muted {
        color: black !important;
        opacity: 0.7;
    }

    .venues-wrapper .text-decoration-line-through {
        color: black !important;
        opacity: 0.6;
    }

    /* Rating styling */
    .venues-wrapper .rating-score {
        font-weight: 600;
        color: black;
    }

    .venues-wrapper .review-count {
        color: black;
        opacity: 0.7;
    }

    /* Back to top button */
    .back-to-top {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: black;
        color: white;
        border: 2px solid black;
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        transition: all 0.3s ease;
    }

    .back-to-top.show {
        display: flex;
    }

    .back-to-top:hover {
        background: white;
        color: black;
        border-color: black;
    }

    /* Opening hours styling */
    .opening-hours-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .opening-hours-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid black;
    }

    .opening-hours-item:last-child {
        border-bottom: none;
    }

    .opening-hours-day {
        font-weight: 500;
        color: black;
    }

    .opening-hours-time {
        color: black;
    }

    .opening-hours-closed {
        color: black;
        opacity: 0.7;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container">
        <div class="row">
            <!-- Service Details -->
            <div class="col-lg-8">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_list' %}">Venues</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">{{ venue.venue_name }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ service.service_title }}</li>
                    </ol>
                </nav>

                <!-- Service Header -->
                <div class="mb-4">
                    <h1 class="mb-2">{{ service.service_title }}</h1>
                    <div class="d-flex align-items-center mb-2">
                        <div class="rating me-3">
                            <span class="rating-score">{{ venue.get_average_rating }}★</span>
                            <span class="review-count">({{ venue.get_review_count }})</span>
                        </div>
                        <span class="venue-name">{{ venue.venue_name }}</span>
                    </div>
                </div>



                <!-- Service Details -->
                <div class="venues-card mb-4">
                    <div class="venues-card-header">
                        <h5 class="mb-0">Service Details</h5>
                    </div>
                    <div class="venues-card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <h6>Price</h6>
                                <div>
                                    {% if service.discounted_price %}
                                    <span class="text-decoration-line-through text-muted">${{ service.price_min }}</span>
                                    <span class="fw-bold">${{ service.discounted_price }}</span>
                                    <span class="badge">{{ service.get_discount_percentage }}% OFF</span>
                                    {% else %}
                                    <span class="fw-bold">{{ service.price_display }}</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6>Duration</h6>
                                <p>{{ service.duration_display }}</p>
                            </div>
                            <div class="col-md-4">
                                <h6>Category</h6>
                                <p>{% for category in venue.categories.all %}{{ category.category_name }}{% if not forloop.last %}, {% endif %}{% empty %}Spa & Wellness{% endfor %}</p>
                            </div>
                        </div>
                        <h6>Description</h6>
                        <p>{{ service.short_description }}</p>
                    </div>
                </div>

                <!-- Booking Button -->
                <div class="text-center mb-4">
                    {% if user.is_authenticated and user.is_customer %}
                    <a href="{% url 'booking_cart_app:add_to_cart' service_id=service.id %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                    </a>
                    {% else %}
                    <a href="{% url 'accounts_app:customer_login' %}?next={% url 'booking_cart_app:add_to_cart' service_id=service.id %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Login to Book
                    </a>
                    {% endif %}
                </div>

                <!-- Venue Information -->
                <div class="venues-card mb-4">
                    <div class="venues-card-header">
                        <h5 class="mb-0">About {{ venue.venue_name }}</h5>
                    </div>
                    <div class="venues-card-body">
                        <p>{{ venue.about_venue }}</p>
                        <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-outline-primary">
                            <i class="fas fa-store me-2"></i>View Venue Details
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Opening Hours -->
                <div class="venues-card mb-4">
                    <div class="venues-card-header">
                        <h5 class="mb-0">Opening Hours</h5>
                    </div>
                    <div class="venues-card-body">
                        <ul class="opening-hours-list">
                            {% for hour in opening_hours %}
                            <li class="opening-hours-item">
                                <span class="opening-hours-day">{{ hour.get_day_display }}</span>
                                {% if hour.is_closed %}
                                <span class="opening-hours-closed">Closed</span>
                                {% else %}
                                <span class="opening-hours-time">{{ hour.opening|time:"g:i A" }} - {{ hour.closing|time:"g:i A" }}</span>
                                {% endif %}
                            </li>
                            {% empty %}
                            <li class="opening-hours-item">
                                <span class="text-muted">No opening hours available</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>

                <!-- Location Map -->
                <div class="venues-card mb-4">
                    <div class="venues-card-header">
                        <h5 class="mb-0">Location</h5>
                    </div>
                    <div class="venues-card-body">
                        <p class="mb-2">{{ venue.get_full_address }}</p>
                        <div id="venue-map" style="height: 300px; border: 2px solid black; border-radius: 0.5rem;" class="mb-3"></div>
                        <a href="https://www.google.com/maps/dir/?api=1&destination={{ venue.latitude }},{{ venue.longitude }}" target="_blank" class="btn btn-outline-primary w-100">
                            <i class="fas fa-directions me-2"></i>Get Directions
                        </a>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="venues-card mb-4">
                    <div class="venues-card-header">
                        <h5 class="mb-0">Contact</h5>
                    </div>
                    <div class="venues-card-body">
                        <p class="mb-2"><i class="fas fa-envelope me-2"></i> {{ venue.service_provider.user.email }}</p>
                        {% if venue.service_provider.phone_number %}
                        <p class="mb-0"><i class="fas fa-phone me-2"></i> {{ venue.service_provider.phone_number }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<a href="#" id="back-to-top" class="back-to-top" aria-label="Back to top">
    <i class="fas fa-arrow-up"></i>
</a>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map if coordinates are available
        {% if venue.latitude and venue.longitude %}
        const venueMap = L.map('venue-map').setView([{{ venue.latitude }}, {{ venue.longitude }}], 15);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(venueMap);

        // Add marker for venue
        L.marker([{{ venue.latitude }}, {{ venue.longitude }}])
            .addTo(venueMap)
            .bindPopup('{{ venue.venue_name }}')
            .openPopup();
        {% endif %}
        const backBtn = document.getElementById('back-to-top');
        window.addEventListener('scroll', function(){
            if(window.scrollY > 200){
                backBtn.classList.add('show');
            } else {
                backBtn.classList.remove('show');
            }
        });
        backBtn.addEventListener('click', function(e){
            e.preventDefault();
            window.scrollTo({top:0, behavior:'smooth'});
        });
    });
</script>
{% endblock %}
