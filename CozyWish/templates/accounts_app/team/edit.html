{% extends 'base.html' %}

{% block title %}Edit Team Member - CozyWish{% endblock %}

{% load static %}
{% load widget_tweaks %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-8">

      <!-- Header -->
      <div class="d-flex align-items-center mb-4">
        <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-primary me-3">
          <i class="fas fa-arrow-left"></i>
        </a>
        <div>
          <h2 class="h3 mb-1">Edit Team Member</h2>
          <p class="text-muted mb-0">Update team member information</p>
        </div>
      </div>

      <!-- Display messages -->
      {% if messages %}
        {% for message in messages %}
          <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        {% endfor %}
      {% endif %}

      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <h5 class="mb-0">
            <i class="fas fa-user-edit me-2 text-primary"></i>Team Member Information
          </h5>
        </div>
        <div class="card-body p-4">
          <form method="post" enctype="multipart/form-data" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger mb-4">
              {% for error in form.non_field_errors %}
              <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
              {% endfor %}
            </div>
            {% endif %}

            <!-- Current team member info -->
            {% if team_member %}
            <div class="alert alert-info mb-4">
              <i class="fas fa-info-circle me-2"></i>
              <strong>Editing:</strong> {{ team_member.name }}
            </div>
            {% endif %}

            <!-- Name field -->
            <div class="mb-4">
              <label class="form-label fw-bold">
                <i class="fas fa-user me-2"></i>Team Member Name
              </label>
              <input type="text" name="name" class="form-control" placeholder="Enter team member name" value="{{ team_member.name|default:'' }}">
            </div>

            <!-- Role field -->
            <div class="mb-4">
              <label class="form-label fw-bold">
                <i class="fas fa-briefcase me-2"></i>Role/Position
              </label>
              <input type="text" name="role" class="form-control" placeholder="e.g., Licensed Massage Therapist" value="{{ team_member.role|default:'' }}">
            </div>

            <!-- Email field -->
            <div class="mb-4">
              <label class="form-label fw-bold">
                <i class="fas fa-envelope me-2"></i>Email Address
              </label>
              <input type="email" name="email" class="form-control" placeholder="<EMAIL>" value="{{ team_member.email|default:'' }}">
            </div>

            <!-- Phone Number field -->
            <div class="mb-4">
              <label class="form-label fw-bold">
                <i class="fas fa-phone me-2"></i>Phone Number
              </label>
              <input type="tel" name="phone_number" class="form-control" placeholder="(*************" value="{{ team_member.phone_number|default:'' }}">
            </div>

            <!-- Active Status -->
            <div class="mb-4">
              <div class="form-check">
                {{ form.is_active|add_class:"form-check-input" }}
                <label class="form-check-label fw-bold" for="{{ form.is_active.id_for_label }}">
                  {{ form.is_active.label }}
                </label>
                {% if form.is_active.help_text %}
                <div class="form-text">{{ form.is_active.help_text }}</div>
                {% endif %}
              </div>
            </div>

            <!-- Action buttons -->
            <div class="d-flex justify-content-between flex-wrap gap-3 pt-3 border-top">
              <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-2"></i>Cancel
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>Update Team Member
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Tips for team management -->
      <div class="card mt-4 shadow-sm">
        <div class="card-header bg-light">
          <h6 class="mb-0">
            <i class="fas fa-lightbulb me-2 text-warning"></i>Tips for Team Management
          </h6>
        </div>
        <div class="card-body">
          <ul class="mb-0 small text-muted">
            <li class="mb-1">Use clear, professional names for better customer trust</li>
            <li class="mb-1">Include specific titles (e.g., "Licensed Massage Therapist" vs "Therapist")</li>
            <li class="mb-1">You can temporarily deactivate team members without removing them</li>
            <li>Team member information helps customers choose the right service provider</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
