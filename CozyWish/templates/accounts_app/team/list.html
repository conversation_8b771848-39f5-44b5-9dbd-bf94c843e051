{% extends 'base.html' %}

{% block title %}Team Members - CozyWish{% endblock %}

{% load static %}
{% load widget_tweaks %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-10">

      <!-- Header -->
      <div class="d-flex align-items-center justify-content-between mb-4">
        <div class="d-flex align-items-center">
          <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-primary me-3">
            <i class="fas fa-arrow-left"></i>
          </a>
          <div>
            <h2 class="h3 mb-1">Team Members</h2>
            <p class="text-muted mb-0">Manage your business team</p>
          </div>
        </div>
        <a href="{% url 'accounts_app:team_member_add' %}" class="btn btn-primary">
          <i class="fas fa-plus me-2"></i>Add Team Member
        </a>
      </div>

      <!-- Team Members List -->
      {% if team_members %}
        <div class="row">
          {% for team_member in team_members %}
          <div class="col-md-6 col-lg-4 mb-4">
            <div class="card shadow-sm h-100">
              <div class="card-body text-center">
                {% if team_member.photo %}
                  <img src="{{ team_member.photo.url }}"
                       alt="{{ team_member.name }}"
                       class="rounded-circle mb-3"
                       style="width: 80px; height: 80px; object-fit: cover;">
                {% else %}
                  <div class="rounded-circle bg-light d-inline-flex align-items-center justify-content-center mb-3" 
                       style="width: 80px; height: 80px;">
                    <i class="fas fa-user fa-2x text-muted"></i>
                  </div>
                {% endif %}
                
                <h5 class="card-title mb-1">{{ team_member.name }}</h5>
                <p class="text-muted small mb-3">{{ team_member.position|default:"Team Member" }}</p>
                
                {% if team_member.is_active %}
                  <span class="badge bg-success mb-3">Active</span>
                {% else %}
                  <span class="badge bg-secondary mb-3">Inactive</span>
                {% endif %}
                
                <div class="d-flex gap-2 justify-content-center">
                  <a href="{% url 'accounts_app:team_member_edit' team_member.id %}" 
                     class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-edit me-1"></i>Edit
                  </a>
                  <button type="button" 
                          class="btn btn-outline-danger btn-sm" 
                          data-bs-toggle="modal" 
                          data-bs-target="#deleteModal{{ team_member.id }}">
                    <i class="fas fa-trash me-1"></i>Delete
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Delete Confirmation Modal -->
          <div class="modal fade" id="deleteModal{{ team_member.id }}" tabindex="-1">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Confirm Delete</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <p>Are you sure you want to remove <strong>{{ team_member.name }}</strong> from your team?</p>
                  <p class="text-muted small">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                  <form method="post" action="{% url 'accounts_app:team_member_delete' team_member.id %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                      <i class="fas fa-trash me-1"></i>Delete
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
      {% else %}
        <!-- Empty State -->
        <div class="text-center py-5">
          <div class="mb-4">
            <i class="fas fa-users fa-4x text-muted"></i>
          </div>
          <h4 class="text-muted mb-3">No Team Members Yet</h4>
          <p class="text-muted mb-4">Start building your team by adding your first team member.</p>
          <a href="{% url 'accounts_app:team_member_add' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Team Member
          </a>
        </div>
      {% endif %}

      <!-- Team Management Tips -->
      <div class="card mt-5 shadow-sm">
        <div class="card-header bg-light">
          <h6 class="mb-0">
            <i class="fas fa-lightbulb me-2 text-warning"></i>Team Management Tips
          </h6>
        </div>
        <div class="card-body">
          <ul class="mb-0 small text-muted">
            <li class="mb-1">Add professional photos to build customer trust</li>
            <li class="mb-1">Include specific titles and specializations</li>
            <li class="mb-1">Keep team information up to date</li>
            <li>Use the active/inactive status to manage availability</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
