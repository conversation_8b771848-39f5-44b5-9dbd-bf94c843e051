{% extends 'accounts_app/base_account.html' %}

{% block title %}Join <PERSON> as a Service Provider{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block account_extra_js %}
<!-- Password toggle functionality is handled in base template -->
{% endblock %}

{% block account_form_content %}
<div class="account-header">
  <div class="mb-3">
    <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-primary bg-opacity-10 mb-3" style="width: 64px; height: 64px;">
      <i class="fas fa-store text-primary fa-2x"></i>
    </div>
  </div>
  <h1 class="h3 mb-2">Join <PERSON>ish for Business</h1>
  <p class="text-muted">Create your business account and start reaching new customers</p>
</div>

<!-- Display messages -->
{% if messages %}
  {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show mb-4" role="alert">
      <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
      {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
  {% endfor %}
{% endif %}

<form method="post" novalidate>
  {% csrf_token %}

  {% if form.non_field_errors %}
  <div class="alert alert-danger mb-4">
    {% for error in form.non_field_errors %}
    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    {% endfor %}
  </div>
  {% endif %}

  <!-- Account Information Section -->
  <div class="mb-4">
    <h5 class="text-dark mb-3 border-bottom pb-2">
      <i class="fas fa-user-circle me-2"></i>Account Information
    </h5>

    <!-- Email field -->
    <div class="form-floating mb-3">
      {{ form.email|add_class:"form-control"|attr:"placeholder:<EMAIL>"|attr:"aria-describedby:email_help" }}
      <label for="{{ form.email.id_for_label }}">
        <i class="fas fa-envelope me-2"></i>{{ form.email.label }}
      </label>
      {% if form.email.errors %}
      <div class="invalid-feedback d-block" role="alert" aria-live="polite">
        {% for error in form.email.errors %}
        <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
        {% endfor %}
      </div>
      {% endif %}
      {% if form.email.help_text %}
      <div class="form-text" id="email_help">{{ form.email.help_text }}</div>
      {% endif %}
    </div>

    <!-- Password field -->
    <div class="mb-3">
      <label for="{{ form.password1.id_for_label }}" class="form-label fw-bold">
        <i class="fas fa-lock me-2"></i>{{ form.password1.label }}
      </label>
      <div class="input-group">
        {{ form.password1|add_class:"form-control"|attr:"placeholder:Create a strong password" }}
        <button type="button" class="btn toggle-password" data-target="#{{ form.password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
          <i class="fas fa-eye" aria-hidden="true"></i>
        </button>
      </div>
      {% if form.password1.errors %}
      <div class="invalid-feedback d-block" role="alert" aria-live="polite">
        {% for error in form.password1.errors %}
        <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
        {% endfor %}
      </div>
      {% endif %}
      {% if form.password1.help_text %}
      <div class="form-text" id="{{ form.password1.id_for_label }}_help">{{ form.password1.help_text }}</div>
      {% endif %}
    </div>

    <!-- Confirm Password field -->
    <div class="mb-3">
      <label for="{{ form.password2.id_for_label }}" class="form-label fw-bold">
        <i class="fas fa-lock me-2"></i>{{ form.password2.label }}
      </label>
      <div class="input-group">
        {{ form.password2|add_class:"form-control"|attr:"placeholder:Confirm your password"|attr:"aria-describedby:password2_help" }}
        <button type="button" class="btn toggle-password" data-target="#{{ form.password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
          <i class="fas fa-eye" aria-hidden="true"></i>
        </button>
      </div>
      {% if form.password2.errors %}
      <div class="invalid-feedback d-block" role="alert" aria-live="polite">
        {% for error in form.password2.errors %}
        <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
        {% endfor %}
      </div>
      {% endif %}
      {% if form.password2.help_text %}
      <div class="form-text" id="password2_help">{{ form.password2.help_text }}</div>
      {% endif %}
    </div>
  </div>

  <!-- Business Information Section -->
  <div class="mb-4">
    <h5 class="text-dark mb-3 border-bottom pb-2">
      <i class="fas fa-building me-2"></i>Business Information
    </h5>

    <!-- Business Name -->
    <div class="form-floating mb-3">
      {{ form.business_name|add_class:"form-control"|attr:"placeholder:Business Name" }}
      <label for="{{ form.business_name.id_for_label }}">
        <i class="fas fa-building me-2"></i>{{ form.business_name.label }}
      </label>
      {% if form.business_name.errors %}
      <div class="invalid-feedback d-block" role="alert" aria-live="polite">
        {% for error in form.business_name.errors %}
        <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
        {% endfor %}
      </div>
      {% endif %}
      {% if form.business_name.help_text %}
      <div class="form-text" id="{{ form.business_name.id_for_label }}_help">{{ form.business_name.help_text }}</div>
      {% endif %}
    </div>

    <!-- Business Phone -->
    <div class="form-floating mb-3">
      {{ form.business_phone_number|add_class:"form-control"|attr:"placeholder:Phone Number" }}
      <label for="{{ form.business_phone_number.id_for_label }}">
        <i class="fas fa-phone me-2"></i>{{ form.business_phone_number.label }}
      </label>
      {% if form.business_phone_number.errors %}
      <div class="invalid-feedback d-block">
        {% for error in form.business_phone_number.errors %}
        {{ error }}
        {% endfor %}
      </div>
      {% endif %}
      {% if form.business_phone_number.help_text %}
      <div class="form-text">{{ form.business_phone_number.help_text }}</div>
      {% endif %}
    </div>

    <!-- Contact Person Name -->
    <div class="form-floating mb-3">
      {{ form.contact_person_name|add_class:"form-control"|attr:"placeholder:Contact Person Name" }}
      <label for="{{ form.contact_person_name.id_for_label }}">
        <i class="fas fa-user-tie me-2"></i>{{ form.contact_person_name.label }}
      </label>
      {% if form.contact_person_name.errors %}
      <div class="invalid-feedback d-block">
        {% for error in form.contact_person_name.errors %}
        {{ error }}
        {% endfor %}
      </div>
      {% endif %}
      {% if form.contact_person_name.help_text %}
      <div class="form-text">{{ form.contact_person_name.help_text }}</div>
      {% endif %}
    </div>
  </div>

  <!-- Business Address Section -->
  <div class="mb-4">
    <h5 class="text-dark mb-3 border-bottom pb-2">
      <i class="fas fa-map-marker-alt me-2"></i>Business Address
    </h5>

    <!-- Street Address -->
    <div class="form-floating mb-3">
      {{ form.business_address|add_class:"form-control"|attr:"placeholder:Street Address" }}
      <label for="{{ form.business_address.id_for_label }}">
        <i class="fas fa-map-marker-alt me-2"></i>{{ form.business_address.label }}
      </label>
      {% if form.business_address.errors %}
      <div class="invalid-feedback d-block">
        {% for error in form.business_address.errors %}
        {{ error }}
        {% endfor %}
      </div>
      {% endif %}
    </div>

    <!-- City, State, ZIP Row -->
    <div class="row">
      <div class="col-md-4 mb-3">
        <div class="form-floating">
          {{ form.city|add_class:"form-control"|attr:"placeholder:City" }}
          <label for="{{ form.city.id_for_label }}">{{ form.city.label }}</label>
          {% if form.city.errors %}
          <div class="invalid-feedback d-block">
            {% for error in form.city.errors %}
            {{ error }}
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>
      <div class="col-md-4 mb-3">
        <div class="form-floating">
          {{ form.state|add_class:"form-select" }}
          <label for="{{ form.state.id_for_label }}">{{ form.state.label }}</label>
          {% if form.state.errors %}
          <div class="invalid-feedback d-block">
            {% for error in form.state.errors %}
            {{ error }}
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>
      <div class="col-md-4 mb-3">
        <div class="form-floating">
          {{ form.zip_code|add_class:"form-control"|attr:"placeholder:ZIP Code" }}
          <label for="{{ form.zip_code.id_for_label }}">{{ form.zip_code.label }}</label>
          {% if form.zip_code.errors %}
          <div class="invalid-feedback d-block">
            {% for error in form.zip_code.errors %}
            {{ error }}
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- EIN -->
    <div class="form-floating mb-3">
      {{ form.ein|add_class:"form-control"|attr:"placeholder:EIN" }}
      <label for="{{ form.ein.id_for_label }}">
        <i class="fas fa-id-card me-2"></i>{{ form.ein.label }}
      </label>
      {% if form.ein.errors %}
      <div class="invalid-feedback d-block">
        {% for error in form.ein.errors %}
        {{ error }}
        {% endfor %}
      </div>
      {% endif %}
      {% if form.ein.help_text %}
      <div class="form-text">{{ form.ein.help_text }}</div>
      {% endif %}
    </div>
  </div>

  <!-- Submit button -->
  <div class="d-grid mb-4">
    <button type="submit" class="btn btn-primary btn-lg">
      <i class="fas fa-user-plus me-2"></i>Create Business Account
    </button>
  </div>

  <!-- Login link -->
  <div class="text-center">
    <p class="text-muted mb-0">Already have an account?
      <a href="{% url 'accounts_app:service_provider_login' %}" class="text-decoration-none">Sign in here</a>
    </p>
  </div>
</form>
{% endblock %}
