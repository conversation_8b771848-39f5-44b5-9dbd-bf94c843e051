{% extends 'accounts_app/base_account.html' %}

{% block title %}Set New Password - CozyWish Business{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block account_extra_js %}
<!-- Password toggle functionality is handled in base template -->
{% endblock %}

{% block account_form_content %}
<div class="account-header">
  <div class="mb-3">
    <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-primary bg-opacity-10 mb-3" style="width: 64px; height: 64px;">
      <i class="fas fa-key text-primary fa-2x"></i>
    </div>
  </div>
  <h1 class="h3 mb-2">Set New Password</h1>
  <p class="text-muted">Enter your new password below to complete the reset process.</p>
</div>

<!-- Display messages -->
{% if messages %}
  {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show mb-4" role="alert">
      <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
      {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
  {% endfor %}
{% endif %}

{% if validlink %}
  <form method="post" novalidate>
    {% csrf_token %}

    <!-- New Password field -->
    <div class="mb-3">
      <label class="form-label fw-medium" for="{{ form.new_password1.id_for_label }}">
        <i class="fas fa-lock me-2"></i>{{ form.new_password1.label }}
      </label>
      <div class="input-group">
        <span class="input-group-text bg-light"><i class="fas fa-lock text-muted"></i></span>
        {{ form.new_password1|add_class:"form-control"|attr:"placeholder:Enter your new password" }}
        <button type="button" class="btn toggle-password" data-target="#{{ form.new_password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
          <i class="fas fa-eye" aria-hidden="true"></i>
        </button>
      </div>
      {% if form.new_password1.errors %}
      <div class="text-danger small mt-1">
        {% for error in form.new_password1.errors %}
        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
        {% endfor %}
      </div>
      {% endif %}
      {% if form.new_password1.help_text %}
      <small class="form-text text-muted">{{ form.new_password1.help_text }}</small>
      {% endif %}
    </div>

    <!-- Confirm Password field -->
    <div class="mb-4">
      <label class="form-label fw-medium" for="{{ form.new_password2.id_for_label }}">
        <i class="fas fa-lock me-2"></i>{{ form.new_password2.label }}
      </label>
      <div class="input-group">
        <span class="input-group-text bg-light"><i class="fas fa-lock text-muted"></i></span>
        {{ form.new_password2|add_class:"form-control"|attr:"placeholder:Confirm your new password" }}
        <button type="button" class="btn toggle-password" data-target="#{{ form.new_password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
          <i class="fas fa-eye" aria-hidden="true"></i>
        </button>
      </div>
      {% if form.new_password2.errors %}
      <div class="text-danger small mt-1">
        {% for error in form.new_password2.errors %}
        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
        {% endfor %}
      </div>
      {% endif %}
    </div>

    <!-- Form-level errors -->
    {% if form.non_field_errors %}
      <div class="alert alert-danger mb-4" role="alert">
        {% for error in form.non_field_errors %}
          <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
        {% endfor %}
      </div>
    {% endif %}

    <!-- Submit button -->
    <div class="d-grid mb-4">
      <button type="submit" class="btn btn-primary btn-lg">
        <i class="fas fa-save me-2"></i>Set New Password
      </button>
    </div>
  </form>
{% else %}
  <!-- Invalid link -->
  <div class="alert alert-danger mb-4" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>Invalid Reset Link</strong><br>
    This password reset link is invalid or has expired. Please request a new one.
  </div>

  <div class="d-grid gap-2 mb-4">
    <a href="{% url 'accounts_app:service_provider_password_reset' %}" class="btn btn-primary btn-lg">
      <i class="fas fa-redo me-2"></i>Request New Reset Link
    </a>
    <a href="{% url 'accounts_app:service_provider_login' %}" class="btn btn-outline-primary">
      <i class="fas fa-sign-in-alt me-2"></i>Back to Login
    </a>
  </div>
{% endif %}

<!-- Security note -->
<div class="text-center">
  <p class="small text-muted mb-0">
    <i class="fas fa-shield-alt me-1"></i>
    For your security, this link can only be used once and expires in 24 hours.
  </p>
</div>
{% endblock %}
