{% extends 'accounts_app/base_account.html' %}

{% block title %}Business Profile - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% endblock %}

{% block account_content %}
<div class="container-fluid py-4">
  <div class="row">
    <!-- Sidebar -->
    <div class="col-lg-3 mb-4">
      <div class="card">
        <div class="card-body text-center">
          {% if profile.logo %}
            <img src="{{ profile.logo.url }}" alt="Business Logo" class="rounded-circle mb-3" style="width: 100px; height: 100px; object-fit: cover;">
          {% else %}
            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVR4nGNoaGgAAAMEAYFL09IQAAAAAElFTkSuQmCC" alt="Business Logo" class="rounded-circle mb-3" style="width: 100px; height: 100px; object-fit: cover;">
          {% endif %}
          <h5 class="card-title">{{ profile.business_name }}</h5>
          <p class="text-muted small">{{ profile.user.email }}</p>

          <!-- Status badge -->
          {% if profile.is_public %}
            <span class="badge bg-success mb-3">
              <i class="fas fa-eye me-1"></i>Visible to Customers
            </span>
          {% else %}
            <span class="badge bg-warning mb-3">
              <i class="fas fa-eye-slash me-1"></i>Hidden from Customers
            </span>
          {% endif %}

          <!-- Quick actions -->
          <div class="d-grid gap-2">
            <a href="{% url 'accounts_app:service_provider_profile_edit' %}" class="btn btn-secondary btn-sm">
              <i class="fas fa-edit me-1"></i>Edit Profile
            </a>
            <a href="{% url 'accounts_app:service_provider_change_password' %}" class="btn btn-outline-secondary btn-sm">
              <i class="fas fa-key me-1"></i>Change Password
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <div class="col-lg-9">

      <!-- Business Information -->
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fas fa-building me-2"></i>Business Information
          </h5>
          <a href="{% url 'accounts_app:service_provider_profile_edit' %}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-edit me-1"></i>Edit
          </a>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6 mb-3">
              <strong>Business Name:</strong><br>
              <span class="text-muted">{{ profile.legal_name|default:"Not provided" }}</span>
            </div>
            <div class="col-md-6 mb-3">
              <strong>DBA Name:</strong><br>
              <span class="text-muted">{{ profile.display_name|default:"Not provided" }}</span>
            </div>
            <div class="col-12 mb-3">
              <strong>Description:</strong><br>
              <span class="text-muted">{{ profile.description|default:"No description provided" }}</span>
            </div>
            <div class="col-md-6 mb-3">
              <strong>Phone:</strong><br>
              <span class="text-muted">{{ profile.phone|default:"Not provided" }}</span>
            </div>
            <div class="col-md-6 mb-3">
              <strong>Contact Person:</strong><br>
              <span class="text-muted">{{ profile.contact_name|default:"Not provided" }}</span>
            </div>
            <div class="col-12 mb-3">
              <strong>Address:</strong><br>
              <span class="text-muted">{{ profile.get_full_address|default:"Address not provided" }}</span>
            </div>
            <div class="col-md-6 mb-3">
              <strong>EIN:</strong><br>
              <span class="text-muted">{{ profile.ein|default:"Not provided" }}</span>
            </div>
            <div class="col-md-6 mb-3">
              <strong>Website:</strong><br>
              {% if profile.website %}
                <a href="{{ profile.website }}" target="_blank" class="text-decoration-none">
                  {{ profile.website }} <i class="fas fa-external-link-alt"></i>
                </a>
              {% else %}
                <span class="text-muted">Not provided</span>
              {% endif %}
            </div>
          </div>

          <!-- Social Media Links -->
          {% if profile.instagram or profile.facebook %}
          <div class="mt-3 pt-3 border-top">
            <strong>Social Media:</strong><br>
            <div class="mt-2">
              {% if profile.instagram %}
                <a href="{{ profile.instagram }}" target="_blank" class="btn btn-outline-secondary btn-sm me-2">
                  <i class="fab fa-instagram me-1"></i>Instagram
                </a>
              {% endif %}
              {% if profile.facebook %}
                <a href="{{ profile.facebook }}" target="_blank" class="btn btn-outline-secondary btn-sm">
                  <i class="fab fa-facebook me-1"></i>Facebook
                </a>
              {% endif %}
            </div>
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Team Management -->
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>Team Members ({{ team_count }}/{{ max_team_members }})
          </h5>
          {% if team_count < max_team_members %}
            <a href="{% url 'accounts_app:team_member_add' %}" class="btn btn-success btn-sm">
              <i class="fas fa-plus me-1"></i>Add Member
            </a>
          {% endif %}
        </div>
        <div class="card-body">
          {% if team_members %}
            <div class="row">
              {% for member in team_members %}
                <div class="col-md-6 col-lg-4 mb-3">
                  <div class="card h-100">
                    <div class="card-body text-center">
                      {% if member.photo %}
                        <img src="{{ member.photo.url }}" alt="{{ member.name }}" class="rounded-circle mb-2" style="width: 60px; height: 60px; object-fit: cover;">
                      {% else %}
                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVR4nGNoaGgAAAMEAYFL09IQAAAAAElFTkSuQmCC" alt="{{ member.name }}" class="rounded-circle mb-2" style="width: 60px; height: 60px; object-fit: cover;">
                      {% endif %}
                      <h6 class="card-title mb-1">{{ member.name }}</h6>
                      <p class="text-muted small mb-2">{{ member.position }}</p>
                      
                      <!-- Status badge -->
                      {% if member.is_active %}
                        <span class="badge bg-success mb-2">Active</span>
                      {% else %}
                        <span class="badge bg-secondary mb-2">Inactive</span>
                      {% endif %}

                      <!-- Action buttons -->
                      <div class="btn-group-vertical w-100" role="group">
                        <a href="{% url 'accounts_app:team_member_edit' member.id %}" class="btn btn-outline-secondary btn-sm">
                          <i class="fas fa-edit me-1"></i>Edit
                        </a>
                        <form method="post" action="{% url 'accounts_app:team_member_toggle_status' member.id %}" class="d-inline">
                          {% csrf_token %}
                          <button type="submit" class="btn btn-outline-warning btn-sm w-100">
                            <i class="fas fa-{% if member.is_active %}eye-slash{% else %}eye{% endif %} me-1"></i>
                            {% if member.is_active %}Deactivate{% else %}Activate{% endif %}
                          </button>
                        </form>
                        <form method="post" action="{% url 'accounts_app:team_member_delete' member.id %}" class="d-inline" onsubmit="return confirm('Are you sure you want to remove this team member?');">
                          {% csrf_token %}
                          <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                            <i class="fas fa-trash me-1"></i>Remove
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-4">
              <i class="fas fa-users fa-3x text-muted mb-3"></i>
              <h6 class="text-muted">No team members added yet</h6>
              <p class="text-muted small">Add team members to showcase your staff and their expertise.</p>
              <a href="{% url 'accounts_app:team_member_add' %}" class="btn btn-secondary">
                <i class="fas fa-plus me-1"></i>Add Your First Team Member
              </a>
            </div>
          {% endif %}
        </div>
      </div>

      <!-- Account Actions -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-cog me-2"></i>Account Settings
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6 mb-3">
              <h6>Security</h6>
              <p class="text-muted small">Manage your account security settings.</p>
              <a href="{% url 'accounts_app:service_provider_change_password' %}" class="btn btn-outline-secondary">
                <i class="fas fa-key me-1"></i>Change Password
              </a>
            </div>
            <div class="col-md-6 mb-3">
              <h6>Account Status</h6>
              <p class="text-muted small">Temporarily deactivate your business account.</p>
              <form method="post" action="{% url 'accounts_app:service_provider_deactivate' %}" class="d-inline" onsubmit="return confirm('Are you sure you want to deactivate your account? Your data will be preserved.');">
                {% csrf_token %}
                <button type="submit" class="btn btn-outline-danger">
                  <i class="fas fa-user-times me-1"></i>Deactivate Account
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
