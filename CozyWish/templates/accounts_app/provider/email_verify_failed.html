{% extends 'accounts_app/base_account.html' %}

{% block title %}Email Verification Failed - CozyWish{% endblock %}

{% block account_extra_css %}
<style>
    /* Email verify failed - matching homepage black & white design */
    .account-card {
        max-width: 600px !important;
    }

    .error-instructions {
        background: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: left;
    }

    .error-instructions h6 {
        color: black;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .error-instructions ul {
        color: black;
        margin-bottom: 0;
        opacity: 0.8;
    }

    .error-instructions li {
        margin-bottom: 0.5rem;
        padding-left: 0.5rem;
    }
</style>
{% endblock %}

{% block account_form_content %}
<div class="account-header">
  <div class="mb-3">
    <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-danger bg-opacity-10 mb-3" style="width: 64px; height: 64px;">
      <i class="fas fa-exclamation-triangle text-danger fa-2x"></i>
    </div>
  </div>
  <h1 class="h3 mb-2" style="color: black;">Verification Failed</h1>
  <p class="text-muted mb-4">The verification link is invalid or has expired</p>
</div>

<!-- Display messages -->
{% if messages %}
  {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show mb-4" role="alert">
      <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
      {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
  {% endfor %}
{% endif %}

<div class="text-center mb-4">
  <p class="mb-4">The verification link is invalid or has expired. This can happen if the link is older than 24 hours or has already been used.</p>
</div>

<!-- Instructions -->
<div class="error-instructions">
  <h6><i class="fas fa-lightbulb me-2"></i>What to do next:</h6>
  <ul>
    <li>Try signing up again with the same email</li>
    <li>Check if you already have an account and try logging in</li>
    <li>Contact our support team for assistance</li>
  </ul>
</div>

<!-- Action buttons -->
<div class="d-grid gap-3 mb-4">
  <a href="{% url 'accounts_app:service_provider_signup' %}" class="btn btn-primary">
    <i class="fas fa-user-plus me-2"></i>Try Signing Up Again
  </a>
  <a href="{% url 'accounts_app:service_provider_login' %}" class="btn btn-outline-primary">
    <i class="fas fa-sign-in-alt me-2"></i>Try Logging In
  </a>
  <a href="{% url 'home' %}" class="btn btn-outline-secondary">
    <i class="fas fa-home me-2"></i>Return to Homepage
  </a>
</div>

<!-- Support contact -->
<div class="text-center">
  <p class="text-muted small mb-0">
    Still having trouble? Contact us at
    <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
  </p>
</div>
{% endblock %}
