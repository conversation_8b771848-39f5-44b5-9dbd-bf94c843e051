{% extends 'accounts_app/base_account.html' %}

{% block title %}Change Password - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block account_extra_js %}
<!-- Password toggle functionality is handled in base template -->
{% endblock %}

{% block account_content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-6">

      <!-- Header -->
      <div class="d-flex align-items-center mb-4">
        <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-primary me-3">
          <i class="fas fa-arrow-left"></i>
        </a>
        <div>
          <h2 class="h3 mb-1">Change Password</h2>
          <p class="text-muted mb-0">Update your business account password for security</p>
        </div>
      </div>

      <!-- Display messages -->
      {% if messages %}
        {% for message in messages %}
          <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        {% endfor %}
      {% endif %}

      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <h5 class="mb-0">
            <i class="fas fa-key me-2 text-primary"></i>Password Security
          </h5>
        </div>
        <div class="card-body p-4">
          <form method="post">
            {% csrf_token %}

            <!-- Current Password -->
            <div class="mb-3">
              <label class="form-label fw-medium" for="{{ form.old_password.id_for_label }}">{{ form.old_password.label }}</label>
              <div class="input-group">
                <span class="input-group-text bg-light"><i class="fas fa-lock text-muted"></i></span>
                {{ form.old_password|add_class:"form-control"|attr:"placeholder:Enter current password" }}
                <button type="button" class="btn toggle-password" data-target="#{{ form.old_password.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                  <i class="fas fa-eye" aria-hidden="true"></i>
                </button>
              </div>
              {% if form.old_password.errors %}
              <div class="text-danger small mt-1">
                {% for error in form.old_password.errors %}
                <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <!-- New Password -->
            <div class="mb-3">
              <label class="form-label fw-medium" for="{{ form.new_password1.id_for_label }}">{{ form.new_password1.label }}</label>
              <div class="input-group">
                <span class="input-group-text bg-light"><i class="fas fa-key text-muted"></i></span>
                {{ form.new_password1|add_class:"form-control"|attr:"placeholder:Enter new password" }}
                <button type="button" class="btn toggle-password" data-target="#{{ form.new_password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                  <i class="fas fa-eye" aria-hidden="true"></i>
                </button>
              </div>
              {% if form.new_password1.errors %}
              <div class="text-danger small mt-1">
                {% for error in form.new_password1.errors %}
                <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                {% endfor %}
              </div>
              {% endif %}
              {% if form.new_password1.help_text %}
              <small class="form-text text-muted">{{ form.new_password1.help_text }}</small>
              {% endif %}
            </div>

            <!-- Confirm New Password -->
            <div class="mb-4">
              <label class="form-label fw-medium" for="{{ form.new_password2.id_for_label }}">{{ form.new_password2.label }}</label>
              <div class="input-group">
                <span class="input-group-text bg-light"><i class="fas fa-key text-muted"></i></span>
                {{ form.new_password2|add_class:"form-control"|attr:"placeholder:Confirm new password" }}
                <button type="button" class="btn toggle-password" data-target="#{{ form.new_password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                  <i class="fas fa-eye" aria-hidden="true"></i>
                </button>
              </div>
              {% if form.new_password2.errors %}
              <div class="text-danger small mt-1">
                {% for error in form.new_password2.errors %}
                <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <!-- Form-level errors -->
            {% if form.non_field_errors %}
              <div class="alert alert-danger" role="alert">
                {% for error in form.non_field_errors %}
                  <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                {% endfor %}
              </div>
            {% endif %}

            <!-- Action buttons -->
            <div class="d-grid gap-2 d-md-flex justify-content-md-start">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>Change Password
              </button>
              <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-2"></i>Cancel
              </a>
            </div>

            <!-- Forgot Password Option -->
            <div class="text-center mt-4 pt-3 border-top">
              <p class="text-muted mb-2">Forgot your current password?</p>
              <a href="{% url 'accounts_app:service_provider_password_reset' %}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-key me-2"></i>Reset Password via Email
              </a>
            </div>
          </form>
        </div>
      </div>

      <!-- Security tips -->
      <div class="card mt-4 shadow-sm">
        <div class="card-header bg-light">
          <h6 class="mb-0">
            <i class="fas fa-shield-alt me-2 text-success"></i>Password Security Tips
          </h6>
        </div>
        <div class="card-body">
          <ul class="mb-0 small text-muted">
            <li class="mb-1">Use a combination of uppercase and lowercase letters, numbers, and symbols</li>
            <li class="mb-1">Make your password at least 8 characters long</li>
            <li class="mb-1">Avoid using personal information like names or birthdays</li>
            <li class="mb-1">Don't reuse passwords from other accounts</li>
            <li>Consider using a password manager for better security</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
