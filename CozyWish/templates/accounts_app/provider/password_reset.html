{% extends 'accounts_app/base_account.html' %}

{% block title %}Reset Password - CozyWish Business{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block account_form_content %}
<div class="account-header">
  <div class="mb-3">
    <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-primary bg-opacity-10 mb-3" style="width: 64px; height: 64px;">
      <i class="fas fa-key text-primary fa-2x"></i>
    </div>
  </div>
  <h1 class="h3 mb-2">Reset Business Password</h1>
  <p class="text-muted">We'll send password reset instructions to your business email address.</p>
</div>

<!-- Show helpful message if email is pre-populated -->
{% if request.GET.email %}
<div class="alert alert-info mb-4" role="alert">
  <i class="fas fa-info-circle me-2"></i>
  <strong>Email pre-filled:</strong> We've filled in your email address from the login form. You can change it if needed.
</div>
{% endif %}

<!-- Display messages -->
{% if messages %}
  {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show mb-4" role="alert">
      <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
      {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
  {% endfor %}
{% endif %}

<form method="post" novalidate>
  {% csrf_token %}

  {% if form.non_field_errors %}
  <div class="alert alert-danger mb-4">
    {% for error in form.non_field_errors %}
    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    {% endfor %}
  </div>
  {% endif %}

  <!-- Email field -->
  <div class="form-floating mb-4">
    {{ form.email|add_class:"form-control"|attr:"placeholder:<EMAIL>" }}
    <label for="{{ form.email.id_for_label }}">
      <i class="fas fa-envelope me-2"></i>Email Address
    </label>
    {% if form.email.errors %}
    <div class="invalid-feedback d-block">
      {% for error in form.email.errors %}
      {{ error }}
      {% endfor %}
    </div>
    {% endif %}
  </div>

  <!-- Submit button -->
  <div class="d-grid mb-4">
    <button type="submit" class="btn btn-primary btn-lg">
      <i class="fas fa-paper-plane me-2"></i>Send Reset Link
    </button>
  </div>

  <!-- Back to login link -->
  <div class="text-center mb-4">
    <p class="text-muted mb-0">
      Remember your password?
      <a href="{% url 'accounts_app:service_provider_login' %}" class="text-decoration-none">Sign in here</a>
    </p>
  </div>
</form>

<!-- Customer reset link -->
<div class="text-center">
  <p class="text-muted small mb-0">
    Looking for customer password reset?
    <a href="{% url 'accounts_app:customer_password_reset' %}" class="text-decoration-none">Click here</a>
  </p>
</div>
{% endblock %}
