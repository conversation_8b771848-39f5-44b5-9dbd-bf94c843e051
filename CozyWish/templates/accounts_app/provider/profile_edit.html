{% extends 'accounts_app/base_account.html' %}

{% block title %}Edit Business Profile - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const input = document.getElementById('{{ form.logo.id_for_label }}');
    if (input) {
        input.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('logo-preview');
                    if (preview) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    }
                };
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
});
</script>
{% endblock %}

{% block account_content %}
<div class="container py-4">
  <div class="row justify-content-center">
    <div class="col-lg-8">

      <!-- Header -->
      <div class="d-flex align-items-center mb-4">
        <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-secondary me-3">
          <i class="fas fa-arrow-left"></i>
        </a>
        <div>
          <h2 class="h3 mb-1">Edit Business Profile</h2>
          <p class="text-muted mb-0">Update your business information and settings</p>
        </div>
      </div>

      <!-- Display messages -->
      {% if messages %}
        {% for message in messages %}
          <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        {% endfor %}
      {% endif %}

      <form method="post" enctype="multipart/form-data">
        {% csrf_token %}

        <!-- Basic Business Information -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-building me-2"></i>Basic Information
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Legal Name -->
              <div class="col-md-6 mb-3">
                <label class="form-label" for="{{ form.legal_name.id_for_label }}">{{ form.legal_name.label }}</label>
                {{ form.legal_name|add_class:"form-control" }}
                {% if form.legal_name.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.legal_name.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
                {% if form.legal_name.help_text %}
                <small class="form-text text-muted">{{ form.legal_name.help_text }}</small>
                {% endif %}
              </div>

              <!-- Display Name -->
              <div class="col-md-6 mb-3">
                <label class="form-label" for="{{ form.display_name.id_for_label }}">{{ form.display_name.label }}</label>
                {{ form.display_name|add_class:"form-control" }}
                {% if form.display_name.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.display_name.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
                {% if form.display_name.help_text %}
                <small class="form-text text-muted">{{ form.display_name.help_text }}</small>
                {% endif %}
              </div>

              <!-- Description -->
              <div class="col-12 mb-3">
                <label class="form-label" for="{{ form.description.id_for_label }}">{{ form.description.label }}</label>
                {{ form.description|add_class:"form-control" }}
                {% if form.description.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.description.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
                {% if form.description.help_text %}
                <small class="form-text text-muted">{{ form.description.help_text }}</small>
                {% endif %}
              </div>

              <!-- Business Logo -->
              <div class="col-12 mb-3">
                <label class="form-label" for="{{ form.logo.id_for_label }}">{{ form.logo.label }}</label>
                {% if form.instance.logo %}
                  <div class="mb-2">
                    <img id="logo-preview" src="{{ form.instance.logo.url }}" alt="Current Logo" class="rounded" style="max-width: 150px; max-height: 150px;">
                    <p class="small text-muted mt-1">Current logo</p>
                  </div>
                {% else %}
                  <img id="logo-preview" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVR4nGNoaGgAAAMEAYFL09IQAAAAAElFTkSuQmCC" alt="Logo Preview" class="rounded mb-2" style="max-width: 150px; max-height: 150px; display: none;">
                {% endif %}
                {{ form.logo|add_class:"form-control" }}
                {% if form.logo.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.logo.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
                {% if form.logo.help_text %}
                <small class="form-text text-muted">{{ form.logo.help_text }}</small>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-phone me-2"></i>Contact Information
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Phone -->
              <div class="col-md-6 mb-3">
                <label class="form-label" for="{{ form.phone.id_for_label }}">{{ form.phone.label }}</label>
                {{ form.phone|add_class:"form-control" }}
                {% if form.phone.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.phone.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              <!-- Contact Name -->
              <div class="col-md-6 mb-3">
                <label class="form-label" for="{{ form.contact_name.id_for_label }}">{{ form.contact_name.label }}</label>
                {{ form.contact_name|add_class:"form-control" }}
                {% if form.contact_name.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.contact_name.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
                {% if form.contact_name.help_text %}
                <small class="form-text text-muted">{{ form.contact_name.help_text }}</small>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Address Information -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-map-marker-alt me-2"></i>Business Address
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Street Address -->
              <div class="col-12 mb-3">
                <label class="form-label" for="{{ form.address.id_for_label }}">{{ form.address.label }}</label>
                {{ form.address|add_class:"form-control" }}
                {% if form.address.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.address.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              <!-- City -->
              <div class="col-md-4 mb-3">
                <label class="form-label" for="{{ form.city.id_for_label }}">{{ form.city.label }}</label>
                {{ form.city|add_class:"form-control" }}
                {% if form.city.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.city.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              <!-- State -->
              <div class="col-md-4 mb-3">
                <label class="form-label" for="{{ form.state.id_for_label }}">{{ form.state.label }}</label>
                {{ form.state|add_class:"form-select" }}
                {% if form.state.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.state.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              <!-- ZIP Code -->
              <div class="col-md-4 mb-3">
                <label class="form-label" for="{{ form.zip_code.id_for_label }}">{{ form.zip_code.label }}</label>
                {{ form.zip_code|add_class:"form-control" }}
                {% if form.zip_code.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.zip_code.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              <!-- County -->
              <div class="col-md-6 mb-3">
                <label class="form-label" for="{{ form.county.id_for_label }}">{{ form.county.label }}</label>
                {{ form.county|add_class:"form-control" }}
                {% if form.county.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.county.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              <!-- EIN -->
              <div class="col-md-6 mb-3">
                <label class="form-label" for="{{ form.ein.id_for_label }}">{{ form.ein.label }}</label>
                {{ form.ein|add_class:"form-control" }}
                {% if form.ein.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.ein.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
                {% if form.ein.help_text %}
                <small class="form-text text-muted">{{ form.ein.help_text }}</small>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Online Presence -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-globe me-2"></i>Online Presence
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- Website -->
              <div class="col-12 mb-3">
                <label class="form-label" for="{{ form.website.id_for_label }}">{{ form.website.label }}</label>
                {{ form.website|add_class:"form-control" }}
                {% if form.website.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.website.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              <!-- Instagram -->
              <div class="col-md-6 mb-3">
                <label class="form-label" for="{{ form.instagram.id_for_label }}">{{ form.instagram.label }}</label>
                {{ form.instagram|add_class:"form-control" }}
                {% if form.instagram.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.instagram.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              <!-- Facebook -->
              <div class="col-md-6 mb-3">
                <label class="form-label" for="{{ form.facebook.id_for_label }}">{{ form.facebook.label }}</label>
                {{ form.facebook|add_class:"form-control" }}
                {% if form.facebook.errors %}
                <div class="text-danger small mt-1">
                  {% for error in form.facebook.errors %}
                  <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Visibility Settings -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-eye me-2"></i>Visibility Settings
            </h5>
          </div>
          <div class="card-body">
            <div class="form-check">
              {{ form.is_public|add_class:"form-check-input" }}
              <label class="form-check-label" for="{{ form.is_public.id_for_label }}">
                {{ form.is_public.label }}
              </label>
              {% if form.is_public.help_text %}
              <div class="form-text">{{ form.is_public.help_text }}</div>
              {% endif %}
              {% if form.is_public.errors %}
              <div class="text-danger small mt-1">
                {% for error in form.is_public.errors %}
                <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                {% endfor %}
              </div>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="d-flex gap-2 mb-4">
          <button type="submit" class="btn btn-secondary">
            <i class="fas fa-save me-2"></i>Save Changes
          </button>
          <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-secondary">
            <i class="fas fa-times me-2"></i>Cancel
          </a>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
