{% extends 'accounts_app/base_account.html' %}

{% block title %}{{ action }} Team Member - CozyWish{% endblock %}

{% block account_extra_css %}
{% load static %}
{% load widget_tweaks %}
<style>
    /* Team member form - matching homepage black & white design */
    .account-card {
        max-width: 700px !important;
    }

    .team-header {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid black;
    }

    .team-header h1 {
        font-size: 2rem;
        font-weight: 700;
        color: black;
        margin-bottom: 0.25rem;
        line-height: 1.2;
    }

    .team-header p {
        color: black;
        margin-bottom: 0;
        opacity: 0.7;
    }

    .back-btn {
        background: white;
        border: 2px solid black;
        color: black;
        border-radius: 0.5rem;
        padding: 0.5rem;
        margin-right: 1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
    }

    .back-btn:hover {
        background: #f8f9fa;
        color: black;
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .team-info-alert {
        background: white;
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
        color: black;
    }

    .team-tips-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-top: 2rem;
        padding: 1.5rem;
    }

    .team-tips-card h6 {
        color: black;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .team-tips-card ul {
        color: black;
        font-size: 0.875rem;
        margin-bottom: 0;
        opacity: 0.8;
    }

    .team-tips-card li {
        margin-bottom: 0.5rem;
    }

    .profile-picture-preview {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid black;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }

    /* Form labels matching homepage style */
    .form-label {
        color: black;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block account_form_content %}
<!-- Header -->
<div class="team-header">
  <a href="{% url 'accounts_app:service_provider_profile' %}" class="back-btn">
    <i class="fas fa-arrow-left"></i>
  </a>
  <div>
    <h1>{{ action }} Team Member</h1>
    <p>
      {% if action == 'Add' %}
        Add a new team member to your business
      {% else %}
        Update team member information
      {% endif %}
    </p>
  </div>
</div>

<!-- Team limit info for add -->
{% if action == 'Add' %}
  <div class="team-info-alert">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Team Limit:</strong> You can add up to {{ max_members }} team members.
    Currently you have {{ current_count }} member{{ current_count|pluralize }}.
  </div>
{% endif %}

<!-- Display messages -->
{% if messages %}
  {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show mb-4" role="alert">
      <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
      {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
  {% endfor %}
{% endif %}

<form method="post" enctype="multipart/form-data" novalidate>
  {% csrf_token %}

  {% if form.non_field_errors %}
  <div class="alert alert-danger mb-4">
    {% for error in form.non_field_errors %}
    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    {% endfor %}
  </div>
  {% endif %}

  <!-- Staff Name -->
  <div class="mb-3">
    <label class="form-label" for="{{ form.staff_name.id_for_label }}">{{ form.staff_name.label }}</label>
    {{ form.staff_name|add_class:"form-control"|attr:"placeholder:Enter team member name" }}
    {% if form.staff_name.errors %}
    <div class="invalid-feedback d-block">
      {% for error in form.staff_name.errors %}
      {{ error }}
      {% endfor %}
    </div>
    {% endif %}
  </div>

  <!-- Staff Position -->
  <div class="mb-3">
    <label class="form-label" for="{{ form.staff_position.id_for_label }}">{{ form.staff_position.label }}</label>
    {{ form.staff_position|add_class:"form-control"|attr:"placeholder:e.g., Licensed Massage Therapist" }}
    {% if form.staff_position.errors %}
    <div class="invalid-feedback d-block">
      {% for error in form.staff_position.errors %}
      {{ error }}
      {% endfor %}
    </div>
    {% endif %}
  </div>

  <!-- Staff Profile Picture -->
  <div class="mb-3">
    <label class="form-label" for="{{ form.staff_profile_picture.id_for_label }}">{{ form.staff_profile_picture.label }}</label>

    {% if action == 'Edit' and team_member.photo %}
      <div class="mb-3">
        <img src="{{ team_member.photo.url }}" alt="{{ team_member.name }}" class="profile-picture-preview">
        <p class="small text-muted">Current photo</p>
      </div>
    {% else %}
      <div class="mb-3">
        <div class="profile-picture-preview d-flex align-items-center justify-content-center bg-light">
          <i class="fas fa-user fa-2x text-muted"></i>
        </div>
        <p class="small text-muted">No photo uploaded</p>
      </div>
    {% endif %}

    {{ form.staff_profile_picture|add_class:"form-control" }}
    {% if form.staff_profile_picture.errors %}
    <div class="invalid-feedback d-block">
      {% for error in form.staff_profile_picture.errors %}
      {{ error }}
      {% endfor %}
    </div>
    {% endif %}
    {% if form.staff_profile_picture.help_text %}
    <div class="form-text">{{ form.staff_profile_picture.help_text }}</div>
    {% endif %}
  </div>

  <!-- Active Status -->
  <div class="mb-4">
    <div class="form-check">
      {{ form.is_active|add_class:"form-check-input" }}
      <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
        {{ form.is_active.label }}
      </label>
      {% if form.is_active.help_text %}
      <div class="form-text">{{ form.is_active.help_text }}</div>
      {% endif %}
      {% if form.is_active.errors %}
      <div class="invalid-feedback d-block">
        {% for error in form.is_active.errors %}
        {{ error }}
        {% endfor %}
      </div>
      {% endif %}
    </div>
  </div>

  <!-- Action buttons -->
  <div class="d-flex justify-content-between flex-wrap gap-3 pt-3 border-top">
    <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn btn-outline-secondary">
      <i class="fas fa-times me-2"></i>Cancel
    </a>
    <button type="submit" class="btn btn-primary">
      <i class="fas fa-{% if action == 'Add' %}plus{% else %}save{% endif %} me-2"></i>
      {% if action == 'Add' %}Add Team Member{% else %}Save Changes{% endif %}
    </button>
  </div>
</form>

<!-- Tips for team management -->
<div class="team-tips-card">
  <h6>
    <i class="fas fa-lightbulb me-2"></i>Tips for Team Management
  </h6>
  <ul>
    <li>Use clear, professional photos for better customer trust</li>
    <li>Include specific titles (e.g., "Licensed Massage Therapist" vs "Therapist")</li>
    <li>You can temporarily deactivate team members without removing them</li>
    <li>Team member information helps customers choose the right service provider</li>
  </ul>
</div>
{% endblock %}
