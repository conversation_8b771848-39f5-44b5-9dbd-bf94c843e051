{% extends 'accounts_app/base_account.html' %}

{% block title %}Password Reset Email Sent{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block account_form_content %}
<div class="account-header">
  <div class="account-icon">
    <i class="fas fa-envelope-open-text"></i>
  </div>
  <h1>Reset Link Sent!</h1>
  <p>We've emailed you instructions for resetting your password.</p>
</div>

<!-- Main message -->
<div class="text-center mb-4">
  {% if reset_email %}
  <div class="alert alert-success mb-3" role="alert">
    <i class="fas fa-envelope me-2"></i>
    <strong>Password reset link sent to:</strong><br>
    <span class="fw-bold">{{ reset_email }}</span>
  </div>
  <p class="mb-3 fw-bold">
    If this email address is associated with a CozyWish account, you should receive password reset instructions shortly.
  </p>
  {% else %}
  <p class="mb-3 fw-bold">
    If an account exists with the email address you provided, you should receive password reset instructions shortly.
  </p>
  {% endif %}
  <p class="mb-0 fw-bold">
    Please check your email inbox and spam folder.
  </p>
</div>

<!-- Instructions -->
<div class="alert alert-info mb-4" role="alert">
  <div class="d-flex align-items-start">
    <i class="fas fa-info-circle me-2 mt-1"></i>
    <div>
      <strong>Next Steps:</strong>
      <ol class="mb-0 mt-2 fw-bold">
        <li>Check your email inbox and spam folder</li>
        <li>Click the reset link in the email</li>
        <li>Create a new secure password</li>
        <li>Sign in with your new password</li>
      </ol>
    </div>
  </div>
</div>

<!-- Security note -->
<div class="alert alert-warning mb-4" role="alert">
  <i class="fas fa-clock me-2"></i>
  <strong>Security Notice:</strong> The reset link will expire in 24 hours for your protection.
</div>

<!-- Action buttons -->
<div class="d-grid gap-3 mb-4">
  <a href="{% url 'accounts_app:customer_login' %}" class="btn btn-primary btn-lg">
    <i class="fas fa-sign-in-alt me-2"></i>Back to Login
  </a>
  <a href="{% url 'home' %}" class="btn btn-outline-primary">
    <i class="fas fa-home me-2"></i>Return to Homepage
  </a>
</div>

<!-- Support contact -->
<div class="text-center">
  <p class="fw-bold mb-0">
    Didn't receive the email?
    <a href="{% url 'accounts_app:customer_password_reset' %}" class="fw-bold">Try again</a>
    or contact us at
    <a href="mailto:<EMAIL>" class="fw-bold"><EMAIL></a>
  </p>
</div>
{% endblock %}
