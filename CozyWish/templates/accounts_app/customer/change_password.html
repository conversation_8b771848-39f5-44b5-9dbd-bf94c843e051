{% extends 'accounts_app/base_account.html' %}
{% load widget_tweaks %}

{% block title %}Change Password{% endblock %}

{% block account_extra_css %}
<style>
    /* CozyWish Change Password - Black & White Design */
    .change-password-header {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .change-password-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background: white !important;
        border-bottom: 2px solid black !important;
        padding: 1.5rem 2rem !important;
    }

    .card-body {
        padding: 2rem !important;
    }

    .form-label {
        font-weight: 600;
        color: black;
        margin-bottom: 0.5rem;
    }

    .input-group-text {
        background: white !important;
        border: 2px solid black !important;
        border-right: none !important;
        color: black !important;
    }

    .input-group .form-control {
        border: 2px solid black !important;
        border-left: none !important;
        border-right: none !important;
        background: white;
        color: black;
    }

    .input-group .form-control:focus {
        border-color: black !important;
        box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1) !important;
        background: white;
    }

    .toggle-password {
        border: 2px solid black !important;
        border-left: none !important;
        background: white !important;
        color: black !important;
    }

    .toggle-password:hover {
        background: black !important;
        color: white !important;
    }

    .text-danger {
        color: #dc3545 !important;
        font-weight: 500;
    }

    .border-top {
        border-color: black !important;
        border-width: 2px !important;
    }

    .security-tips-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .security-tips-card .card-header {
        background: #f8f9fa !important;
        border-bottom: 2px solid black !important;
    }

    .security-tips-card ul li {
        color: black;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block account_extra_js %}
<!-- Password toggle functionality is handled in base template -->
{% endblock %}

{% block account_content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-8">

      <!-- Header -->
      <div class="change-password-header">
        <div class="d-flex align-items-center">
          <a href="{% url 'accounts_app:customer_profile' %}" class="btn btn-outline-primary me-4">
            <i class="fas fa-arrow-left me-2"></i>Back to Profile
          </a>
          <div>
            <h2 class="mb-2" style="font-size: 2rem; font-weight: 700; color: black;">Change Password</h2>
            <p class="mb-0" style="color: black; opacity: 0.8;">Update your account password for better security</p>
          </div>
        </div>
      </div>

      <div class="change-password-card">
        <div class="card-header">
          <h5 class="mb-0" style="font-size: 1.5rem; font-weight: 700; color: black;">
            <i class="fas fa-key me-3"></i>Password Security
          </h5>
        </div>
        <div class="card-body">
          <form method="post" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="alert alert-danger mb-4">
              {% for error in form.non_field_errors %}
              <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
              {% endfor %}
            </div>
            {% endif %}

            <!-- Current Password -->
            <div class="mb-4">
              <label class="form-label" for="{{ form.old_password.id_for_label }}">{{ form.old_password.label }}</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                {{ form.old_password|add_class:"form-control"|attr:"placeholder:Enter current password" }}
                <button type="button" class="btn toggle-password" data-target="#{{ form.old_password.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                  <i class="fas fa-eye" aria-hidden="true"></i>
                </button>
              </div>
              {% if form.old_password.errors %}
              <div class="text-danger mt-2">
                {% for error in form.old_password.errors %}
                <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <!-- New Password -->
            <div class="mb-4">
              <label class="form-label" for="{{ form.new_password1.id_for_label }}">{{ form.new_password1.label }}</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-key"></i></span>
                {{ form.new_password1|add_class:"form-control"|attr:"placeholder:Enter new password" }}
                <button type="button" class="btn toggle-password" data-target="#{{ form.new_password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                  <i class="fas fa-eye" aria-hidden="true"></i>
                </button>
              </div>
              {% if form.new_password1.errors %}
              <div class="text-danger mt-2">
                {% for error in form.new_password1.errors %}
                <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                {% endfor %}
              </div>
              {% endif %}
              {% if form.new_password1.help_text %}
              <div class="form-text mt-2" style="color: black; font-weight: 500;">{{ form.new_password1.help_text }}</div>
              {% endif %}
            </div>

            <!-- Confirm New Password -->
            <div class="mb-4">
              <label class="form-label" for="{{ form.new_password2.id_for_label }}">{{ form.new_password2.label }}</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-key"></i></span>
                {{ form.new_password2|add_class:"form-control"|attr:"placeholder:Confirm new password" }}
                <button type="button" class="btn toggle-password" data-target="#{{ form.new_password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                  <i class="fas fa-eye" aria-hidden="true"></i>
                </button>
              </div>
              {% if form.new_password2.errors %}
              <div class="text-danger mt-2">
                {% for error in form.new_password2.errors %}
                <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <!-- Form Actions -->
            <div class="d-grid gap-3 d-md-flex justify-content-md-start mb-4">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>Change Password
              </button>
              <a href="{% url 'accounts_app:customer_profile' %}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-2"></i>Cancel
              </a>
            </div>

            <!-- Forgot Password Option -->
            <div class="text-center pt-3 border-top">
              <p class="mb-3" style="color: black; font-weight: 600;">Forgot your current password?</p>
              <a href="{% url 'accounts_app:customer_password_reset' %}" class="btn btn-outline-primary">
                <i class="fas fa-key me-2"></i>Reset Password via Email
              </a>
            </div>
          </form>
        </div>
      </div>

      <!-- Security tips -->
      <div class="security-tips-card mt-4">
        <div class="card-header">
          <h6 class="mb-0" style="font-size: 1.25rem; font-weight: 700; color: black;">
            <i class="fas fa-shield-alt me-3"></i>Password Security Tips
          </h6>
        </div>
        <div class="card-body">
          <ul class="mb-0">
            <li>Use a combination of uppercase and lowercase letters, numbers, and symbols</li>
            <li>Make your password at least 8 characters long</li>
            <li>Avoid using personal information like names or birthdays</li>
            <li>Don't reuse passwords from other accounts</li>
            <li>Consider using a password manager for better security</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
