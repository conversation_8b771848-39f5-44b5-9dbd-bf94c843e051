{% extends 'accounts_app/base_account.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}My Profile{% endblock %}

{% block extra_css %}
<!-- External CSS for Customer Profile -->
<link rel="stylesheet" href="{% static 'css/customer_profile.css' %}">
{% endblock %}

{% block extra_js %}
<script>
/**
 * Customer Profile Page JavaScript
 * Handles profile picture upload, account deactivation, and animations
 */
document.addEventListener('DOMContentLoaded', function() {
    // Profile picture upload functionality
    const profilePictureEdit = document.getElementById('profile-picture-edit');
    const profilePictureInput = document.getElementById('profile-picture-input');
    const profilePictureForm = document.getElementById('profile-picture-form');

    if (profilePictureEdit && profilePictureInput && profilePictureForm) {
        profilePictureEdit.addEventListener('click', function() {
            profilePictureInput.click();
        });

        profilePictureInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // Validate file type
                const file = this.files[0];
                const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];

                if (!validTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG or PNG).');
                    return;
                }

                // Validate file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB.');
                    return;
                }

                // Show loading state
                const container = document.querySelector('.profile-picture-container');
                container.classList.add('profile-loading');

                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.querySelector('.profile-picture');
                    if (img) {
                        img.src = e.target.result;
                        // Auto-submit the form to save the image
                        setTimeout(() => {
                            profilePictureForm.submit();
                        }, 500);
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Account deactivation confirmation
    const deactivateBtn = document.getElementById('deactivate-account-btn');
    if (deactivateBtn) {
        deactivateBtn.addEventListener('click', function(e) {
            e.preventDefault();

            const confirmMessage =
                'Are you sure you want to deactivate your account?\n\n' +
                '• Your profile will be hidden from other users\n' +
                '• Your booking history will be preserved\n' +
                '• You can reactivate your account anytime by logging in\n\n' +
                'Click OK to proceed or Cancel to keep your account active.';

            if (confirm(confirmMessage)) {
                // Add loading state to button
                this.innerHTML = '<i class="fas fa-spinner fa-spin" aria-hidden="true"></i> Deactivating...';
                this.disabled = true;

                setTimeout(() => {
                    document.getElementById('deactivate-form').submit();
                }, 1000);
            }
        });
    }

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Apply fade-in animation to all profile cards
    document.querySelectorAll('.profile-fade-in').forEach(element => {
        observer.observe(element);
    });
});
</script>
{% endblock %}

{% block account_content %}
<div class="container-fluid py-4">
    <div class="container">
        <!-- Profile Header -->
        <div class="profile-header profile-fade-in">
            <div class="row align-items-center">
                <div class="col-md-3 text-center mb-3 mb-md-0">
                    <div class="profile-picture-container">
                        {% if profile.profile_picture %}
                            <img src="{{ profile.profile_picture.url }}" alt="Profile Picture" class="profile-picture">
                        {% else %}
                            <img src="https://via.placeholder.com/140x140/E5E5E5/999999?text=No+Image" alt="Profile Picture" class="profile-picture">
                        {% endif %}
                        <button type="button" id="profile-picture-edit" class="profile-picture-edit-btn" aria-label="Edit profile picture">
                            <i class="fas fa-camera"></i>
                        </button>
                    </div>
                    <!-- Hidden form for profile picture upload -->
                    <form id="profile-picture-form" method="post" enctype="multipart/form-data" action="{% url 'accounts_app:customer_profile_edit' %}" style="display: none;">
                        {% csrf_token %}
                        <input type="file" id="profile-picture-input" name="profile_picture" accept="image/*">
                    </form>
                </div>
                <div class="col-md-9">
                    <h1 class="profile-name">{{ profile.get_full_name|default:"Welcome!" }}</h1>
                    <p class="profile-email">{{ user.email }}</p>
                </div>
            </div>
        </div>

        <!-- Profile Information Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Profile Information</h2>
            <div class="row">
                <!-- Personal Details Card -->
                <div class="col-lg-6 mb-4">
                    <div class="profile-info-card profile-fade-in">
                        <div class="profile-card-header">
                            <h3 class="profile-card-title">
                                <i class="fas fa-user" aria-hidden="true"></i>
                                Personal Details
                            </h3>
                        </div>
                        <div class="profile-card-body">
                            <div class="profile-info-row">
                                <div class="profile-info-label">First Name:</div>
                                <div class="profile-info-value {% if not profile.first_name %}empty{% endif %}">
                                    {{ profile.first_name|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Last Name:</div>
                                <div class="profile-info-value {% if not profile.last_name %}empty{% endif %}">
                                    {{ profile.last_name|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Phone Number:</div>
                                <div class="profile-info-value {% if not profile.phone_number %}empty{% endif %}">
                                    {{ profile.phone_number|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Gender:</div>
                                <div class="profile-info-value {% if not profile.gender %}empty{% endif %}">
                                    {{ profile.get_gender_display|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Birth Date:</div>
                                <div class="profile-info-value {% if not profile.birth_month or not profile.birth_year %}empty{% endif %}">
                                    {% if profile.birth_month and profile.birth_year %}
                                        {{ profile.get_birth_month_display }} {{ profile.birth_year }}
                                    {% else %}
                                        Not provided
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Card -->
                <div class="col-lg-6 mb-4">
                    <div class="profile-info-card profile-fade-in">
                        <div class="profile-card-header">
                            <h3 class="profile-card-title">
                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                Contact Information
                            </h3>
                        </div>
                        <div class="profile-card-body">
                            <div class="profile-info-row">
                                <div class="profile-info-label">Address:</div>
                                <div class="profile-info-value {% if not profile.default_address %}empty{% endif %}">
                                    {{ profile.default_address|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">City:</div>
                                <div class="profile-info-value {% if not profile.city %}empty{% endif %}">
                                    {{ profile.city|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">ZIP Code:</div>
                                <div class="profile-info-value {% if not profile.zip_code %}empty{% endif %}">
                                    {{ profile.zip_code|default:"Not provided" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Information Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Account Information</h2>
            <div class="row">
                <div class="col-12">
                    <div class="profile-info-card profile-fade-in">
                        <div class="profile-card-header">
                            <h3 class="profile-card-title">
                                <i class="fas fa-info-circle" aria-hidden="true"></i>
                                Account Details
                            </h3>
                        </div>
                        <div class="profile-card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Email Address:</div>
                                        <div class="profile-info-value">{{ user.email }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Account Type:</div>
                                        <div class="profile-info-value">
                                            <span class="badge bg-primary">Customer</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Member Since:</div>
                                        <div class="profile-info-value">{{ user.date_joined|date:"F d, Y" }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Last Login:</div>
                                        <div class="profile-info-value">
                                            {{ user.last_login|date:"F d, Y g:i A"|default:"Never" }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Actions Section (Moved to Bottom) -->
        <div class="profile-actions-section profile-fade-in">
            <h2 class="actions-title">Account Management</h2>
            <div class="profile-actions">
                <a href="{% url 'accounts_app:customer_profile_edit' %}" class="profile-btn">
                    <i class="fas fa-edit" aria-hidden="true"></i>
                    Edit Profile
                </a>
                <a href="{% url 'accounts_app:customer_change_password' %}" class="profile-btn">
                    <i class="fas fa-key" aria-hidden="true"></i>
                    Change Password
                </a>
                <button type="button" id="deactivate-account-btn" class="profile-btn profile-btn-danger">
                    <i class="fas fa-user-times" aria-hidden="true"></i>
                    Deactivate Account
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden deactivate form -->
<form id="deactivate-form" method="post" action="{% url 'accounts_app:customer_deactivate' %}" style="display: none;">
    {% csrf_token %}
</form>
{% endblock %}
