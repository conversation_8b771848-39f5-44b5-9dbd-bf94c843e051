{% extends 'accounts_app/base_account.html' %}
{% load widget_tweaks %}

{% block title %}Edit Profile{% endblock %}

{% block account_extra_css %}
<style>
    /* CozyWish Profile Edit - Black & White Design */
    .profile-wrapper {
        background-color: white;
        min-height: 100vh;
        padding: 2rem 0;
    }

    .account-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .profile-edit-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 2px solid black;
    }

    .profile-edit-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        color: black;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .profile-edit-header p {
        color: black;
        margin-bottom: 0;
        font-size: 1.1rem;
        opacity: 0.8;
    }

    .section-header {
        font-size: 1.5rem;
        font-weight: 700;
        color: black;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid black;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-header i {
        color: black;
        font-size: 1.25rem;
    }

    .form-label {
        font-weight: 600;
        color: black;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid black;
        border-radius: 0.5rem;
        padding: 1rem;
        color: black;
        background-color: white;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: black;
        box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
        outline: none;
        background-color: white;
    }

    .profile-picture-preview {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid black;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
        background: black;
        border: 2px solid black;
        color: white;
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
    }

    .btn-primary:hover {
        background: white;
        color: black;
        border-color: black;
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .btn-outline-secondary {
        border: 2px solid black;
        color: black;
        background: white;
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
    }

    .btn-outline-secondary:hover {
        background: black;
        border-color: black;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .border-top {
        border-color: black !important;
        border-width: 2px !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Profile picture preview
    const profilePictureInput = document.getElementById('{{ form.profile_picture.id_for_label }}');
    if (profilePictureInput) {
        profilePictureInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('profile-picture-preview');
                    if (preview) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    }
                };
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
});
</script>
{% endblock %}

{% block account_content %}
<div class="container-fluid py-4">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="account-card">
                    <div class="account-card-body">
                        <div class="profile-edit-header">
                            <div class="account-icon">
                                <i class="fas fa-user-edit"></i>
                            </div>
                            <h1>Edit Profile</h1>
                            <p>Update your personal information and preferences</p>
                        </div>

<form method="post" enctype="multipart/form-data" novalidate>
  {% csrf_token %}

  {% if form.non_field_errors %}
  <div class="alert alert-danger mb-4">
    {% for error in form.non_field_errors %}
    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    {% endfor %}
  </div>
  {% endif %}

  <!-- Profile Picture Section -->
  <div class="mb-4">
    <h5 class="section-header">
      <i class="fas fa-camera"></i>Profile Picture
    </h5>
    <div class="d-flex align-items-center gap-3">
      <div>
        {% if form.instance.profile_picture %}
          <img id="profile-picture-preview" src="{{ form.instance.profile_picture.url }}" alt="Profile Picture" class="profile-picture-preview">
        {% else %}
          <div class="profile-picture-preview d-flex align-items-center justify-content-center bg-light">
            <i class="fas fa-user fa-3x text-muted"></i>
          </div>
        {% endif %}
      </div>
      <div class="flex-grow-1">
        {{ form.profile_picture|add_class:"form-control" }}
        {% if form.profile_picture.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.profile_picture.errors %}
          {{ error }}
          {% endfor %}
        </div>
        {% endif %}
        {% if form.profile_picture.help_text %}
        <div class="form-text">{{ form.profile_picture.help_text }}</div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Personal Information Section -->
  <div class="mb-4">
    <h5 class="section-header">
      <i class="fas fa-user"></i>Personal Information
    </h5>
    <div class="row">
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
        {{ form.first_name|add_class:"form-control" }}
        {% if form.first_name.errors %}
        <div class="invalid-feedback d-block" role="alert" aria-live="polite">
          {% for error in form.first_name.errors %}
          <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
        {{ form.last_name|add_class:"form-control" }}
        {% if form.last_name.errors %}
        <div class="invalid-feedback d-block" role="alert" aria-live="polite">
          {% for error in form.last_name.errors %}
          <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.phone_number.id_for_label }}">{{ form.phone_number.label }}</label>
        {{ form.phone_number|add_class:"form-control"|attr:"aria-describedby:phone_help" }}
        {% if form.phone_number.errors %}
        <div class="invalid-feedback d-block" role="alert" aria-live="polite">
          {% for error in form.phone_number.errors %}
          <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
        {% if form.phone_number.help_text %}
        <div class="form-text" id="phone_help">{{ form.phone_number.help_text }}</div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.gender.id_for_label }}">{{ form.gender.label }}</label>
        {{ form.gender|add_class:"form-select" }}
        {% if form.gender.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.gender.errors %}
          {{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.birth_month.id_for_label }}">{{ form.birth_month.label }}</label>
        {{ form.birth_month|add_class:"form-select" }}
        {% if form.birth_month.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.birth_month.errors %}
          {{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.birth_year.id_for_label }}">{{ form.birth_year.label }}</label>
        {{ form.birth_year|add_class:"form-select" }}
        {% if form.birth_year.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.birth_year.errors %}
          {{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Address Information Section -->
  <div class="mb-4">
    <h5 class="section-header">
      <i class="fas fa-map-marker-alt"></i>Address Information
    </h5>
    <div class="row">
      <div class="col-12 mb-3">
        <label class="form-label" for="{{ form.default_address.id_for_label }}">{{ form.default_address.label }}</label>
        {{ form.default_address|add_class:"form-control" }}
        {% if form.default_address.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.default_address.errors %}
          {{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.city.id_for_label }}">{{ form.city.label }}</label>
        {{ form.city|add_class:"form-control" }}
        {% if form.city.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.city.errors %}
          {{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.zip_code.id_for_label }}">{{ form.zip_code.label }}</label>
        {{ form.zip_code|add_class:"form-control" }}
        {% if form.zip_code.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.zip_code.errors %}
          {{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="d-flex justify-content-between flex-wrap gap-3 pt-3 border-top">
    <a href="{% url 'accounts_app:customer_profile' %}" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-2"></i>Cancel
    </a>
    <button type="submit" class="btn btn-primary">
      <i class="fas fa-save me-2"></i>Save Changes
    </button>
  </div>
</form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
