{% extends 'accounts_app/base_account.html' %}

{% block title %}Set New Password{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block account_extra_js %}
<!-- Password toggle functionality is handled in base template -->
{% endblock %}

{% block account_form_content %}
{% if validlink %}
  <div class="account-header">
    <div class="account-icon">
      <i class="fas fa-lock"></i>
    </div>
    <h1>Set New Password</h1>
    <p>Please enter your new password twice to confirm</p>
  </div>

  <form method="post" novalidate>
    {% csrf_token %}

    {% if form.non_field_errors %}
    <div class="alert alert-danger mb-4">
      {% for error in form.non_field_errors %}
      <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
      {% endfor %}
    </div>
    {% endif %}

    <!-- New Password field -->
    <div class="mb-4">
      <label for="{{ form.new_password1.id_for_label }}" class="form-label fw-bold">
        <i class="fas fa-lock me-2"></i>New Password
      </label>
      <div class="input-group">
        {{ form.new_password1|add_class:"form-control"|attr:"placeholder:Enter your new password" }}
        <button type="button" class="btn toggle-password" data-target="#{{ form.new_password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
          <i class="fas fa-eye" aria-hidden="true"></i>
        </button>
      </div>
      {% if form.new_password1.errors %}
      <div class="invalid-feedback">
        {% for error in form.new_password1.errors %}
        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
        {% endfor %}
      </div>
      {% endif %}
      {% if form.new_password1.help_text %}
      <div class="form-text">{{ form.new_password1.help_text }}</div>
      {% endif %}
    </div>

    <!-- Confirm Password field -->
    <div class="mb-4">
      <label for="{{ form.new_password2.id_for_label }}" class="form-label fw-bold">
        <i class="fas fa-lock me-2"></i>Confirm Password
      </label>
      <div class="input-group">
        {{ form.new_password2|add_class:"form-control"|attr:"placeholder:Confirm your new password" }}
        <button type="button" class="btn toggle-password" data-target="#{{ form.new_password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
          <i class="fas fa-eye" aria-hidden="true"></i>
        </button>
      </div>
      {% if form.new_password2.errors %}
      <div class="invalid-feedback">
        {% for error in form.new_password2.errors %}
        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
        {% endfor %}
      </div>
      {% endif %}
      {% if form.new_password2.help_text %}
      <div class="form-text">{{ form.new_password2.help_text }}</div>
      {% endif %}
    </div>

    <div class="d-grid mb-4">
      <button type="submit" class="btn btn-primary btn-lg">
        <i class="fas fa-check-circle me-2"></i>Set New Password
      </button>
    </div>
  </form>

{% else %}
  <div class="account-header">
    <div class="account-icon">
      <i class="fas fa-exclamation-triangle"></i>
    </div>
    <h1>Invalid Link</h1>
    <p class="mb-4">The password reset link was invalid, possibly because it has already been used. Please request a new password reset.</p>
  </div>

  <div class="d-grid mb-4">
    <a href="{% url 'accounts_app:customer_password_reset' %}" class="btn btn-primary btn-lg">
      <i class="fas fa-redo me-2"></i>Request New Reset Link
    </a>
  </div>
{% endif %}
{% endblock %}
