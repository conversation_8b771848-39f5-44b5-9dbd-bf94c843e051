{% extends 'accounts_app/base_account.html' %}
{% load widget_tweaks %}

{% block title %}Deactivate Account{% endblock %}

{% block account_extra_css %}
<style>
    /* CozyWish Deactivate Account - Black & White Design */
    .account-card {
        max-width: 600px !important;
    }

    .warning-info {
        background: #fff5f5;
        border: 2px solid #dc3545;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .warning-info h5 {
        color: #dc3545;
        font-weight: 700;
        margin-bottom: 1rem;
        font-size: 1.25rem;
    }

    .warning-info p {
        color: black;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .warning-info ul {
        color: black;
        margin-bottom: 0;
    }

    .warning-info li {
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .btn-warning {
        background: #dc3545;
        border: 2px solid #dc3545;
        color: white;
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
    }

    .btn-warning:hover {
        background: white;
        color: #dc3545;
        border-color: #dc3545;
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .form-label {
        font-weight: 600;
        color: black;
    }
</style>
{% endblock %}

{% block account_form_content %}
<div class="account-header">
  <div class="account-icon" style="border-color: #dc3545;">
    <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
  </div>
  <h1>Deactivate Account</h1>
  <p>This action will temporarily disable your account</p>
</div>

<div class="warning-info">
  <h5><i class="fas fa-info-circle me-2"></i>Important Information</h5>
  <p>This is a permanent action that cannot be undone. Deactivating your account will:</p>
  <ul>
    <li>You will lose access to your account</li>
    <li>Your bookings history will be preserved</li>
    <li>Hide your profile from other users</li>
    <li>Preserve your data securely</li>
    <li>Allow reactivation by contacting support</li>
  </ul>
</div>

<form method="post" novalidate>
  {% csrf_token %}

  {% if form.non_field_errors %}
  <div class="alert alert-danger mb-4">
    {% for error in form.non_field_errors %}
    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    {% endfor %}
  </div>
  {% endif %}

  <div class="mb-4">
    <label for="{{ form.confirm_email.id_for_label }}" class="form-label">
      Type your email to confirm deactivation
    </label>
    {{ form.confirm_email|add_class:"form-control"|attr:"placeholder:Enter your email to confirm" }}
    {% if form.confirm_email.errors %}
    <div class="invalid-feedback d-block">
      {% for error in form.confirm_email.errors %}
      {{ error }}
      {% endfor %}
    </div>
    {% endif %}
    {% if form.confirm_email.help_text %}
    <div class="form-text">{{ form.confirm_email.help_text }}</div>
    {% endif %}
  </div>

  <div class="d-flex justify-content-between flex-wrap gap-3">
    <a href="{% url 'accounts_app:customer_profile' %}" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-2"></i>Cancel
    </a>
    <button type="submit" class="btn btn-warning">
      <i class="fas fa-user-slash me-2"></i>Deactivate Account
    </button>
  </div>
</form>
{% endblock %}
