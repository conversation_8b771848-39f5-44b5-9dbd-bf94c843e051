{% extends 'accounts_app/base_account.html' %}

{% block title %}Sign Up with <PERSON><PERSON><PERSON>ish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block account_extra_js %}
<!-- Password toggle functionality is handled in base template -->
{% endblock %}

{% block account_form_content %}
<div class="account-header">
  <div class="account-icon">
    <i class="fas fa-user-plus"></i>
  </div>
  <h1>Join CozyWish</h1>
  <p>Create your account to discover amazing venues</p>
</div>

<form method="post" novalidate>
  {% csrf_token %}

  {% if form.non_field_errors %}
  <div class="alert alert-danger mb-4">
    {% for error in form.non_field_errors %}
    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    {% endfor %}
  </div>
  {% endif %}

  <!-- Email field -->
  <div class="mb-4">
    <label for="{{ form.email.id_for_label }}" class="form-label fw-bold">
      <i class="fas fa-envelope me-2"></i>{{ form.email.label }}
    </label>
    {% if form.email.errors %}
      {{ form.email|add_class:"form-control is-invalid"|attr:"placeholder:Enter your email address" }}
    {% else %}
      {{ form.email|add_class:"form-control"|attr:"placeholder:Enter your email address" }}
    {% endif %}
    {% if form.email.errors %}
    <div class="invalid-feedback d-block" role="alert" aria-live="polite">
      {% for error in form.email.errors %}
      <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
      {% endfor %}
    </div>
    {% endif %}
    {% if form.email.help_text %}
    <div class="form-text">{{ form.email.help_text }}</div>
    {% endif %}
  </div>

  <!-- Password field -->
  <div class="mb-4">
    <label for="{{ form.password1.id_for_label }}" class="form-label fw-bold">
      <i class="fas fa-lock me-2"></i>{{ form.password1.label }}
    </label>
    <div class="input-group">
      {% if form.password1.errors %}
        {{ form.password1|add_class:"form-control is-invalid"|attr:"placeholder:Create a strong password" }}
      {% else %}
        {{ form.password1|add_class:"form-control"|attr:"placeholder:Create a strong password" }}
      {% endif %}
      <button type="button" class="btn toggle-password" data-target="#{{ form.password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
        <i class="fas fa-eye" aria-hidden="true"></i>
      </button>
    </div>
    {% if form.password1.errors %}
    <div class="invalid-feedback d-block" role="alert" aria-live="polite">
      {% for error in form.password1.errors %}
      <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
      {% endfor %}
    </div>
    {% endif %}
    {% if form.password1.help_text %}
    <div class="form-text">{{ form.password1.help_text }}</div>
    {% endif %}
  </div>

  <!-- Confirm Password field -->
  <div class="mb-4">
    <label for="{{ form.password2.id_for_label }}" class="form-label fw-bold">
      <i class="fas fa-lock me-2"></i>{{ form.password2.label }}
    </label>
    <div class="input-group">
      {% if form.password2.errors %}
        {{ form.password2|add_class:"form-control is-invalid"|attr:"placeholder:Confirm your password" }}
      {% else %}
        {{ form.password2|add_class:"form-control"|attr:"placeholder:Confirm your password" }}
      {% endif %}
      <button type="button" class="btn toggle-password" data-target="#{{ form.password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
        <i class="fas fa-eye" aria-hidden="true"></i>
      </button>
    </div>
    {% if form.password2.errors %}
    <div class="invalid-feedback d-block" role="alert" aria-live="polite">
      {% for error in form.password2.errors %}
      <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
      {% endfor %}
    </div>
    {% endif %}
    {% if form.password2.help_text %}
    <div class="form-text">{{ form.password2.help_text }}</div>
    {% endif %}
  </div>

  <!-- Terms checkbox -->
  <div class="form-check mb-4">
    {{ form.agree_to_terms|add_class:"form-check-input" }}
    <label class="form-check-label fw-bold" for="{{ form.agree_to_terms.id_for_label }}">
      I agree to the <a href="#" class="fw-bold">Terms of Service</a> and <a href="#" class="fw-bold">Privacy Policy</a>
    </label>
    {% if form.agree_to_terms.errors %}
    <div class="invalid-feedback d-block" role="alert" aria-live="polite">
      {% for error in form.agree_to_terms.errors %}
      <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
      {% endfor %}
    </div>
    {% endif %}
  </div>

  <!-- Submit button -->
  <div class="d-grid mb-4">
    <button type="submit" class="btn btn-primary btn-lg">
      <i class="fas fa-user-plus me-2"></i>Create Account
    </button>
  </div>
</form>

<!-- Divider -->
<div class="position-relative my-4">
  <hr style="border-color: black;">
  <span class="position-absolute top-50 start-50 translate-middle bg-white px-3 fw-bold">
    or continue with
  </span>
</div>

<!-- Social signup buttons -->
<div class="d-flex justify-content-center gap-3 mb-3">
  <button type="button" class="btn btn-outline-secondary rounded-circle p-3" style="width: 50px; height: 50px;" disabled>
    <i class="fab fa-google"></i>
  </button>
  <button type="button" class="btn btn-outline-secondary rounded-circle p-3" style="width: 50px; height: 50px;" disabled>
    <i class="fab fa-apple"></i>
  </button>
  <button type="button" class="btn btn-outline-secondary rounded-circle p-3" style="width: 50px; height: 50px;" disabled>
    <i class="fab fa-facebook-f"></i>
  </button>
</div>
<p class="text-center fw-bold mb-4 fst-italic">Social signup coming soon</p>

<!-- Login and business links -->
<div class="text-center">
  <p class="fw-bold mb-3">Already have an account?</p>
  <div class="d-grid mb-4">
    <a href="{% url 'accounts_app:customer_login' %}" class="btn btn-outline-primary">
      <i class="fas fa-sign-in-alt me-2"></i>Sign In
    </a>
  </div>

  <p class="fw-bold mb-3">Are you a service provider?</p>
  <div class="d-grid">
    <a href="{% url 'accounts_app:for_business' %}" class="btn btn-outline-secondary">
      <i class="fas fa-store me-2"></i>For Business
    </a>
  </div>
</div>
{% endblock %}
