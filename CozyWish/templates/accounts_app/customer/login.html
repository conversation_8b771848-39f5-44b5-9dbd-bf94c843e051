{% extends 'accounts_app/base_account.html' %}

{% block title %}Log in to CozyWish{% endblock %}

{% load static %}
{% load widget_tweaks %}

{% block account_form_content %}
<div class="account-header">
  <div class="account-icon">
    <i class="fas fa-user"></i>
  </div>
  <h1>Welcome Back</h1>
  <p>Sign in to your CozyWish account</p>
</div>

<form method="post" novalidate>
  {% csrf_token %}

  {% if form.non_field_errors %}
  <div class="alert alert-danger mb-4">
    {% for error in form.non_field_errors %}
    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    {% endfor %}
  </div>
  {% endif %}

  <!-- Email field -->
  <div class="mb-4">
    <label for="{{ form.email.id_for_label }}" class="form-label fw-bold">
      <i class="fas fa-envelope me-2"></i>{{ form.email.label }}
    </label>
    {{ form.email|add_class:"form-control"|attr:"placeholder:Enter your email address" }}
    {% if form.email.errors %}
    <div class="invalid-feedback" role="alert" aria-live="polite">
      {% for error in form.email.errors %}
      <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
      {% endfor %}
    </div>
    {% endif %}
  </div>

  <!-- Password field -->
  <div class="mb-4">
    <label for="{{ form.password.id_for_label }}" class="form-label fw-bold">
      <i class="fas fa-lock me-2"></i>{{ form.password.label }}
    </label>
    <div class="input-group">
      {{ form.password|add_class:"form-control"|attr:"placeholder:Enter your password" }}
      <button type="button" class="btn toggle-password" data-target="#{{ form.password.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
        <i class="fas fa-eye" aria-hidden="true"></i>
      </button>
    </div>
    {% if form.password.errors %}
    <div class="invalid-feedback" role="alert" aria-live="polite">
      {% for error in form.password.errors %}
      <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
      {% endfor %}
    </div>
    {% endif %}
  </div>

  <!-- Remember me and forgot password -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div class="form-check">
      <input class="form-check-input" type="checkbox" id="remember" name="remember">
      <label class="form-check-label fw-bold" for="remember">
        Remember me
      </label>
    </div>
    <a href="{% url 'accounts_app:customer_password_reset' %}" class="fw-bold" id="forgot-password-link">
      Forgot password?
    </a>
  </div>

  <!-- Submit button -->
  <div class="d-grid mb-4">
    <button type="submit" class="btn btn-primary btn-lg">
      <i class="fas fa-sign-in-alt me-2"></i>Sign In
    </button>
  </div>
</form>

<!-- Divider -->
<div class="position-relative my-4">
  <hr style="border-color: black;">
  <span class="position-absolute top-50 start-50 translate-middle bg-white px-3 fw-bold">
    or continue with
  </span>
</div>

<!-- Social login buttons -->
<div class="d-flex justify-content-center gap-3 mb-3">
  <button type="button" class="btn btn-outline-secondary rounded-circle p-3" style="width: 50px; height: 50px;" disabled>
    <i class="fab fa-google"></i>
  </button>
  <button type="button" class="btn btn-outline-secondary rounded-circle p-3" style="width: 50px; height: 50px;" disabled>
    <i class="fab fa-apple"></i>
  </button>
  <button type="button" class="btn btn-outline-secondary rounded-circle p-3" style="width: 50px; height: 50px;" disabled>
    <i class="fab fa-facebook-f"></i>
  </button>
</div>
<p class="text-center fw-bold mb-4 fst-italic">Social login coming soon</p>

<!-- Sign up links -->
<div class="text-center">
  <p class="fw-bold mb-3">Don't have an account?</p>
  <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
    <a href="{% url 'accounts_app:customer_signup' %}" class="btn btn-outline-primary">
      <i class="fas fa-user-plus me-2"></i>Sign up as Customer
    </a>
    <a href="{% url 'accounts_app:for_business' %}" class="btn btn-outline-secondary">
      <i class="fas fa-store me-2"></i>For Business
    </a>
  </div>
</div>

{% endblock %}

{% block account_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pre-fill email in password reset form
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    const emailField = document.getElementById('{{ form.email.id_for_label }}');

    if (forgotPasswordLink && emailField) {
        forgotPasswordLink.addEventListener('click', function(e) {
            const emailValue = emailField.value.trim();
            if (emailValue) {
                e.preventDefault();
                const resetUrl = new URL(this.href);
                resetUrl.searchParams.set('email', emailValue);
                window.location.href = resetUrl.toString();
            }
        });
    }
});
</script>
{% endblock %}
