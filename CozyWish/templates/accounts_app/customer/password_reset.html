{% extends 'accounts_app/base_account.html' %}

{% block title %}Reset Password{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
{% endblock %}

{% block account_form_content %}
<div class="account-header">
  <div class="account-icon">
    <i class="fas fa-key"></i>
  </div>
  <h1>Reset Password</h1>
  <p>We'll send password reset instructions to your email address.</p>
</div>

<!-- Show helpful message if email is pre-populated -->
{% if request.GET.email %}
<div class="alert alert-info mb-4" role="alert">
  <i class="fas fa-info-circle me-2"></i>
  <strong>Email pre-filled:</strong> We've filled in your email address from the login form. You can change it if needed.
</div>
{% endif %}

<form method="post" novalidate>
  {% csrf_token %}

  {% if form.non_field_errors %}
  <div class="alert alert-danger mb-4">
    {% for error in form.non_field_errors %}
    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    {% endfor %}
  </div>
  {% endif %}

  {% for field in form %}
  <div class="mb-4">
    <label for="{{ field.id_for_label }}" class="form-label fw-bold">
      <i class="fas fa-envelope me-2"></i>{{ field.label }}
    </label>
    {{ field|add_class:"form-control"|attr:"placeholder:Enter your email address" }}
    {% if field.errors %}
    <div class="invalid-feedback">
      {% for error in field.errors %}
      <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
      {% endfor %}
    </div>
    {% endif %}
    {% if field.help_text %}
    <div class="form-text">{{ field.help_text }}</div>
    {% endif %}
  </div>
  {% endfor %}

  <div class="d-grid mb-4">
    <button type="submit" class="btn btn-primary btn-lg">
      <i class="fas fa-paper-plane me-2"></i>Send Reset Link
    </button>
  </div>
</form>

<div class="text-center">
  <a href="{% url 'accounts_app:customer_login' %}" class="fw-bold">
    <i class="fas fa-arrow-left me-2"></i>Back to Login
  </a>
</div>
{% endblock %}
