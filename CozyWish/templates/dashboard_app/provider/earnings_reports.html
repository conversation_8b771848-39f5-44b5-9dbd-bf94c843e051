{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Earnings Reports - Provider Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Earnings Reports{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <button type="button" class="btn btn-sm btn-outline-success" onclick="window.print()">
        <i class="fas fa-print"></i> Print Report
    </button>
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'dashboard_app:provider_dashboard' %}">Dashboard</a></li>
    <li class="breadcrumb-item active" aria-current="page">Earnings Reports</li>
  </ol>
</nav>
<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Select Date Range</h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3" aria-label="Earnings report filter">
                    <div class="col-md-3">
                        <label for="{{ form.period.id_for_label }}" class="form-label">Period</label>
                        {{ form.period }}
                    </div>
                    <div class="col-md-3">
                        <label for="{{ form.start_date.id_for_label }}" class="form-label">Start Date</label>
                        {{ form.start_date }}
                    </div>
                    <div class="col-md-3">
                        <label for="{{ form.end_date.id_for_label }}" class="form-label">End Date</label>
                        {{ form.end_date }}
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2" aria-label="Generate earnings report">
                            <i class="fas fa-search"></i> Generate Report
                        </button>
                        <a href="{% url 'dashboard_app:provider_earnings_reports' %}" class="btn btn-outline-secondary me-2" aria-label="Reset date range">
                            <i class="fas fa-refresh"></i> Reset
                        </a>
                        <a href="{% url 'dashboard_app:provider_earnings_export' %}?start_date={{ start_date }}&end_date={{ end_date }}" class="btn btn-outline-success spinner-button" aria-label="Export earnings CSV">
                            <i class="fas fa-file-csv"></i> {% trans "Export CSV" %}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Earnings Summary -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Earnings</h6>
                        <h3 class="mb-0">${{ total_earnings|floatformat:2 }}</h3>
                        <small>{{ start_date|date:"M d" }} - {{ end_date|date:"M d, Y" }}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Bookings</h6>
                        <h3 class="mb-0">{{ total_bookings }}</h3>
                        <small>Completed bookings</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clipboard-list fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Average Booking Value</h6>
                        <h3 class="mb-0">${{ avg_booking_value|floatformat:2 }}</h3>
                        <small>Per booking</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-bar fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Daily Earnings Trend</h5>
                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#daily-earnings-collapse" aria-expanded="true" aria-label="Toggle daily earnings">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
            <div id="daily-earnings-collapse" class="collapse show">
            <div class="card-body">
                {% if daily_earnings %}
                    <canvas id="earningsChart" width="400" height="200"></canvas>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">{% trans "No earnings data available for the selected period." %}</p>
                    </div>
                {% endif %}
            </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-spa me-2"></i>Top Services</h5>
                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#top-services-collapse" aria-expanded="true" aria-label="Toggle top services">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
            <div id="top-services-collapse" class="collapse show">
            <div class="card-body">
                {% if earnings_by_service %}
                    {% for service in earnings_by_service %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-1">{{ service.service__service_title|truncatechars:20 }}</h6>
                                <small class="text-muted">{{ service.count }} booking{{ service.count|pluralize }}</small>
                            </div>
                            <div class="text-end">
                                <strong class="text-success">${{ service.total|floatformat:2 }}</strong>
                            </div>
                        </div>
                        {% if not forloop.last %}<hr>{% endif %}
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-spa fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No service data available.</p>
                    </div>
                {% endif %}
            </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Breakdown -->
<div class="row">
    <div class="col-md-12">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Earnings by Service</h5>
                <div>
                    <span class="badge bg-primary me-2">{{ earnings_by_service|length }} service{{ earnings_by_service|length|pluralize }}</span>
                    <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#earnings-breakdown" aria-expanded="true" aria-label="Toggle earnings breakdown">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div id="earnings-breakdown" class="collapse show">
            <div class="card-body">
                {% if earnings_by_service %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th class="text-center">Bookings</th>
                                    <th class="text-end">Total Earnings</th>
                                    <th class="text-end">Avg per Booking</th>
                                    <th class="text-center">Performance</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for service in earnings_by_service %}
                                    <tr>
                                        <td>
                                            <strong>{{ service.service__service_title }}</strong>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info">{{ service.count }}</span>
                                        </td>
                                        <td class="text-end">
                                            <strong class="text-success">${{ service.total|floatformat:2 }}</strong>
                                        </td>
                                        <td class="text-end">
                                            ${{ service.total|div:service.count|floatformat:2 }}
                                        </td>
                                        <td class="text-center">
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-success" 
                                                     style="width: {% if earnings_by_service.0.total > 0 %}{{ service.total|mul:100|div:earnings_by_service.0.total }}{% else %}0{% endif %}%">
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-bar fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">{% trans "No earnings data found" %}</h5>
                        <p class="text-muted">{% trans "No completed bookings found for the selected date range." %}</p>
                        <a href="{% url 'dashboard_app:provider_earnings_reports' %}" class="btn btn-outline-primary">
                            <i class="fas fa-refresh"></i> {% trans "Try Different Date Range" %}
                        </a>
                    </div>
                {% endif %}
            </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block dashboard_js %}
{% if daily_earnings %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('earningsChart').getContext('2d');
    
    const dailyData = [
        {% for day in daily_earnings %}
        {
            date: '{{ day.scheduled_date|date:"M d" }}',
            earnings: {{ day.daily_total|default:0 }}
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dailyData.map(d => d.date),
            datasets: [{
                label: 'Daily Earnings ($)',
                data: dailyData.map(d => d.earnings),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toFixed(2);
                        }
                    }
                }
            }
        }
    });
});
</script>
{% endif %}
{% endblock %}
