{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}

{% block title %}My Bookings - CozyWish{% endblock %}

{% block dashboard_title %}My Bookings{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/customer_sidebar.html' %}
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'dashboard_app:customer_dashboard' %}">Dashboard</a></li>
    <li class="breadcrumb-item active" aria-current="page">Bookings</li>
  </ol>
</nav>
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">My Bookings</h1>
            <div class="d-flex gap-2">
                <a href="{% url 'dashboard_app:customer_dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Book New Service
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filter Cards -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card {% if status_filter == 'all' %}border-primary{% endif %}">
            <div class="card-body text-center">
                <a href="?status=all&date={{ date_filter }}" class="text-decoration-none">
                    <h5 class="card-title text-primary">{{ total_count }}</h5>
                    <p class="card-text small">All Bookings</p>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card {% if status_filter == 'pending' %}border-warning{% endif %}">
            <div class="card-body text-center">
                <a href="?status=pending&date={{ date_filter }}" class="text-decoration-none">
                    <h5 class="card-title text-warning">{{ pending_count }}</h5>
                    <p class="card-text small">Pending</p>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card {% if status_filter == 'confirmed' %}border-success{% endif %}">
            <div class="card-body text-center">
                <a href="?status=confirmed&date={{ date_filter }}" class="text-decoration-none">
                    <h5 class="card-title text-success">{{ confirmed_count }}</h5>
                    <p class="card-text small">Confirmed</p>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card {% if status_filter == 'completed' %}border-info{% endif %}">
            <div class="card-body text-center">
                <a href="?status=completed&date={{ date_filter }}" class="text-decoration-none">
                    <h5 class="card-title text-info">{{ completed_count }}</h5>
                    <p class="card-text small">Completed</p>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card {% if status_filter == 'cancelled' %}border-danger{% endif %}">
            <div class="card-body text-center">
                <a href="?status=cancelled&date={{ date_filter }}" class="text-decoration-none">
                    <h5 class="card-title text-danger">{{ cancelled_count }}</h5>
                    <p class="card-text small">Cancelled</p>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card">
            <div class="card-body text-center">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-filter"></i> Date Filter
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item {% if date_filter == 'all' %}active{% endif %}" href="?status={{ status_filter }}&date=all">All Dates</a></li>
                        <li><a class="dropdown-item {% if date_filter == 'upcoming' %}active{% endif %}" href="?status={{ status_filter }}&date=upcoming">Upcoming</a></li>
                        <li><a class="dropdown-item {% if date_filter == 'past' %}active{% endif %}" href="?status={{ status_filter }}&date=past">Past</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bookings List -->
<div class="row">
    <div class="col-12">
        {% include 'booking_cart_app/includes/booking_skeleton.html' %}
        <div id="booking-results" style="display:none;">
        {% if bookings %}
            {% for booking in bookings %}
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            {% if booking.venue.main_image %}
                                <img src="{{ booking.venue.main_image.url }}" alt="{{ booking.venue.venue_name }}" class="img-fluid rounded" style="height: 80px; width: 100%; object-fit: cover;">
                            {% else %}
                                <div class="rounded d-flex align-items-center justify-content-center" style="height: 80px; background-color: white; border: 2px solid black;">
                                    <i class="fas fa-spa fa-2x" style="color: black; opacity: 0.6;"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <h5 class="mb-1">{{ booking.venue.venue_name }}</h5>
                            <p class="mb-1 text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ booking.venue.city }}, {{ booking.venue.state }}
                            </p>
                            <small class="text-muted">
                                Booking ID: #{{ booking.booking_id|slice:":8" }}
                            </small>
                        </div>
                        <div class="col-md-2">
                            <p class="mb-1">
                                <i class="fas fa-calendar me-1"></i>
                                {{ booking.booking_date|date:"M d, Y" }}
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-clock me-1"></i>
                                {{ booking.booking_date|time:"g:i A" }}
                            </p>
                        </div>
                        <div class="col-md-2">
                            <span class="badge bg-{% if booking.status == 'completed' %}success{% elif booking.status == 'confirmed' %}info{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %} fs-6">
                                {{ booking.get_status_display }}
                            </span>
                        </div>
                        <div class="col-md-2 text-end">
                            <h5 class="mb-1">${{ booking.total_price }}</h5>
                            <div class="btn-group-vertical btn-group-sm">
                                <a href="{% url 'booking_cart_app:booking_detail' booking.booking_id %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                {% if booking.status == 'pending' or booking.status == 'confirmed' %}
                                <a href="{% url 'booking_cart_app:cancel_booking' booking.booking_id %}" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Bookings pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}&status={{ status_filter }}&date={{ date_filter }}">Previous</a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}&status={{ status_filter }}&date={{ date_filter }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}&status={{ status_filter }}&date={{ date_filter }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

        {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-calendar-times fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted">No bookings found</h4>
                    <p class="text-muted">
                        {% if status_filter != 'all' or date_filter != 'all' %}
                            No bookings match your current filters.
                        {% else %}
                            You haven't made any bookings yet.
                        {% endif %}
                    </p>
                    <div class="mt-4">
                        {% if status_filter != 'all' or date_filter != 'all' %}
                            <a href="{% url 'dashboard_app:customer_booking_status' %}" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-filter"></i> Clear Filters
                            </a>
                        {% endif %}
                        <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                            <i class="fas fa-search"></i> Find Venues to Book
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
        </div>
    </div>
</div>
{% endblock dashboard_content %}
{% block extra_js %}<script src="{% static 'js/bookings.js' %}"></script>{% endblock %}
