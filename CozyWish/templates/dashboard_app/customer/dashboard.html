{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}

{% block title %}Customer Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Customer Dashboard{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/customer_sidebar.html' %}
{% endblock %}

{% block dashboard_extra_css %}
<style>
    /* Customer dashboard specific styles - black & white theme */
    .breadcrumb {
        background-color: white;
        border: 2px solid black;
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
    }

    .breadcrumb-item a {
        color: black;
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item a:hover {
        color: rgba(0, 0, 0, 0.7);
    }

    .breadcrumb-item.active {
        color: rgba(0, 0, 0, 0.6);
    }

    /* Statistics cards */
    .stats-card {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .stats-card h4 {
        font-family: var(--font-heading);
        font-weight: 700;
        font-size: 2rem;
        color: black;
        margin-bottom: 0.5rem;
    }

    .stats-card p {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.6);
        margin-bottom: 0;
        font-weight: 500;
    }

    .stats-card i {
        color: black;
    }

    /* Quick action buttons */
    .quick-action-btn {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        color: black;
        text-decoration: none;
        transition: all 0.3s ease;
        padding: 2rem 1rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        min-height: 120px;
    }

    .quick-action-btn:hover {
        background-color: black;
        color: white;
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .quick-action-btn i {
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
  </ol>
</nav>
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Customer Dashboard</h1>
            <div class="d-flex gap-2">
                <a href="{% url 'dashboard_app:customer_profile_edit' %}" class="btn btn-outline-primary" id="customer_profile">
                    <i class="fas fa-user-edit"></i> Edit Profile
                </a>
                <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                    <i class="fas fa-search"></i> Find Venues
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4>{{ total_bookings }}</h4>
                    <p>Total Bookings</p>
                </div>
                <div>
                    <i class="fas fa-calendar-check fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4>{{ pending_bookings }}</h4>
                    <p>Pending Bookings</p>
                </div>
                <div>
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4>{{ confirmed_bookings }}</h4>
                    <p>Confirmed Bookings</p>
                </div>
                <div>
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4>{{ favorite_venues_count }}</h4>
                    <p>Favorite Venues</p>
                </div>
                <div>
                    <i class="fas fa-heart fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Upcoming Bookings -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Upcoming Bookings
                </h5>
                <a href="{% url 'dashboard_app:customer_booking_status' %}?date=upcoming" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                {% if upcoming_bookings %}
                    {% for booking in upcoming_bookings %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <h6 class="mb-1">{{ booking.venue.venue_name }}</h6>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ booking.booking_date|date:"M d, Y" }}
                            </small>
                            <br>
                            <span class="badge bg-primary">
                                {{ booking.get_status_display }}
                            </span>
                        </div>
                        <div class="text-end">
                            <strong>${{ booking.total_price }}</strong>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No upcoming bookings</p>
                        <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                            Book a Service
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Bookings -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    Recent Bookings
                </h5>
                <a href="{% url 'dashboard_app:customer_booking_status' %}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                {% if recent_bookings %}
                    {% for booking in recent_bookings %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <h6 class="mb-1">{{ booking.venue.venue_name }}</h6>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ booking.booking_date|date:"M d, Y" }}
                            </small>
                            <br>
                            <span class="badge bg-primary">
                                {{ booking.get_status_display }}
                            </span>
                        </div>
                        <div class="text-end">
                            <strong>${{ booking.total_price }}</strong>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent bookings</p>
                        <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                            Book a Service
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'dashboard_app:customer_booking_status' %}" class="quick-action-btn">
                            <i class="fas fa-list fa-2x"></i>
                            <span>View All Bookings</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'dashboard_app:customer_favorite_venues' %}" class="quick-action-btn">
                            <i class="fas fa-heart fa-2x"></i>
                            <span>Favorite Venues</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'venues_app:venue_list' %}" class="quick-action-btn">
                            <i class="fas fa-search fa-2x"></i>
                            <span>Find Venues</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'discount_app:featured_discounts' %}" class="quick-action-btn">
                            <i class="fas fa-tags fa-2x"></i>
                            <span>View Discounts</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh dashboard data every 5 minutes
setTimeout(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
