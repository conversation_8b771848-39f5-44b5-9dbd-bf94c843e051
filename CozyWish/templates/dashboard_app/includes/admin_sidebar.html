<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'admin_dashboard' %} active{% endif %}" href="{% url 'dashboard_app:admin_dashboard' %}">
        <i class="fas fa-tachometer-alt"></i> Dashboard
    </a>
</li>
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'admin_platform_overview' %} active{% endif %}" href="{% url 'dashboard_app:admin_platform_overview' %}">
        <i class="fas fa-chart-pie"></i> Platform Overview
    </a>
</li>
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'admin_user_statistics' %} active{% endif %}" href="{% url 'dashboard_app:admin_user_statistics' %}">
        <i class="fas fa-users"></i> User Statistics
    </a>
</li>
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'admin_booking_analytics' %} active{% endif %}" href="{% url 'dashboard_app:admin_booking_analytics' %}">
        <i class="fas fa-chart-bar"></i> Booking Analytics
    </a>
</li>
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'admin_revenue_tracking' %} active{% endif %}" href="{% url 'dashboard_app:admin_revenue_tracking' %}">
        <i class="fas fa-dollar-sign"></i> Revenue Tracking
    </a>
</li>
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'admin_system_health' %} active{% endif %}" href="{% url 'dashboard_app:admin_system_health' %}">
        <i class="fas fa-heartbeat"></i> System Health
    </a>
</li>
