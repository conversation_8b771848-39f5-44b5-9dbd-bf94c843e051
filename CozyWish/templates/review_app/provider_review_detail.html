{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}Review Details - {{ venue.venue_name }}{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'review_app:provider_venue_reviews' %}">Venue Reviews</a></li>
            <li class="breadcrumb-item active" aria-current="page">Review Details</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8">
            <!-- Review Details Card -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Customer Review</h5>
                    <span class="badge bg-{% if review.rating >= 4 %}success{% elif review.rating >= 3 %}warning{% else %}danger{% endif %}">
                        {{ review.rating }}/5 Stars
                    </span>
                </div>
                <div class="card-body">
                    <!-- Customer Info -->
                    <div class="mb-3">
                        <h6 class="text-muted">Customer</h6>
                        <p class="mb-1">{{ review.customer.get_full_name|default:review.customer.email }}</p>
                        <small class="text-muted">Reviewed on {{ review.created_at|date:"F j, Y" }}</small>
                    </div>

                    <!-- Rating Display -->
                    <div class="mb-3">
                        <h6 class="text-muted">Rating</h6>
                        <div class="mb-2">
                            {% for i in "12345" %}
                                {% if forloop.counter <= review.rating %}
                                    <i class="fas fa-star text-warning"></i>
                                {% else %}
                                    <i class="far fa-star text-muted"></i>
                                {% endif %}
                            {% endfor %}
                            <span class="ms-2">{{ review.rating }}/5</span>
                        </div>
                    </div>

                    <!-- Review Content -->
                    <div class="mb-3">
                        <h6 class="text-muted">Review</h6>
                        <p>{{ review.written_review }}</p>
                    </div>

                    <!-- Review Status -->
                    <div class="mb-3">
                        <h6 class="text-muted">Status</h6>
                        <div class="d-flex gap-2">
                            {% if review.is_approved %}
                                <span class="badge bg-success">Approved</span>
                            {% else %}
                                <span class="badge bg-warning">Pending Approval</span>
                            {% endif %}
                            
                            {% if review.is_flagged %}
                                <span class="badge bg-danger">Flagged</span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Your Response -->
                    {% if review.response %}
                        <div class="border-top pt-3">
                            <h6 class="text-muted">Your Response</h6>
                            <div class="bg-light p-3 rounded">
                                <p class="mb-2">{{ review.response.response_text }}</p>
                                <small class="text-muted">
                                    Posted on {{ review.response.created_at|date:"F j, Y" }}
                                    {% if review.response.updated_at != review.response.created_at %}
                                        • Updated on {{ review.response.updated_at|date:"F j, Y" }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                    {% else %}
                        <div class="border-top pt-3">
                            <p class="text-muted"><em>You haven't responded to this review yet.</em></p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex gap-2">
                {% if review.response %}
                    <a href="{% url 'review_app:provider_edit_response' response_id=review.response.id %}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Response
                    </a>
                {% else %}
                    <a href="{% url 'review_app:provider_respond_to_review' review_slug=review.slug %}" class="btn btn-success">
                        <i class="fas fa-reply"></i> Respond to Review
                    </a>
                {% endif %}
                <a href="{% url 'review_app:provider_venue_reviews' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to All Reviews
                </a>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Review Statistics Card -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">Review Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ venue.reviews.count }}</h4>
                            <small class="text-muted">Total Reviews</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">
                                {% if venue.average_rating %}
                                    {{ venue.average_rating|floatformat:1 }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </h4>
                            <small class="text-muted">Average Rating</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'review_app:provider_venue_reviews' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list"></i> All Reviews
                        </a>
                        <a href="{% url 'review_app:provider_review_summary' %}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-chart-bar"></i> Review Summary
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
