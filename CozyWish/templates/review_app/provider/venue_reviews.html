{% extends 'review_app/base_review.html' %}

{% block title %}Venue Reviews{% endblock %}

{% block extra_css %}
{% load static %}
<style>
.skeleton-loader {
    background-color: #eee;
    border-radius: 4px;
    animation: pulse 1.5s infinite;
}
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.4; }
    100% { opacity: 1; }
}
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: none;
    z-index: 50;
}
</style>
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-star me-2"></i>Venue Reviews
                </h2>
                <a href="{% url 'review_app:provider_review_summary' %}" class="btn btn-outline-primary">
                    <i class="fas fa-chart-bar me-2"></i>View Summary
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card sticky-top" style="top:70px; z-index:102;">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="venue" class="form-label">Venue</label>
                            <select name="venue" id="venue" class="form-select" aria-label="Filter by venue">
                                <option value="">All Venues</option>
                                {% for venue in venues %}
                                    <option value="{{ venue.id }}" {% if request.GET.venue == venue.id|stringformat:"s" %}selected{% endif %}>
                                        {{ venue.venue_name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="rating" class="form-label">Rating</label>
                            <select name="rating" id="rating" class="form-select" aria-label="Filter by rating">
                                <option value="">All Ratings</option>
                                <option value="5" {% if request.GET.rating == "5" %}selected{% endif %}>5 Stars</option>
                                <option value="4" {% if request.GET.rating == "4" %}selected{% endif %}>4 Stars</option>
                                <option value="3" {% if request.GET.rating == "3" %}selected{% endif %}>3 Stars</option>
                                <option value="2" {% if request.GET.rating == "2" %}selected{% endif %}>2 Stars</option>
                                <option value="1" {% if request.GET.rating == "1" %}selected{% endif %}>1 Star</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select" aria-label="Filter by status">
                                <option value="">All Reviews</option>
                                <option value="responded" {% if request.GET.status == "responded" %}selected{% endif %}>Responded</option>
                                <option value="not_responded" {% if request.GET.status == "not_responded" %}selected{% endif %}>Not Responded</option>
                                <option value="flagged" {% if request.GET.status == "flagged" %}selected{% endif %}>Flagged</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control"
                                   placeholder="Search reviews..." aria-label="Search reviews"
                                   value="{{ request.GET.search }}">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div id="reviews-skeleton">
        <div class="card skeleton-loader mb-3" style="height:120px;"></div>
        <div class="card skeleton-loader mb-3" style="height:120px;"></div>
        <div class="card skeleton-loader mb-3" style="height:120px;"></div>
    </div>

    {% if reviews %}
        <!-- Reviews List -->
        <div class="row">
            {% for review in reviews %}
            <div class="col-12 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row">
                            <!-- Customer Info -->
                            <div class="col-md-2">
                                {% if review.customer.customerprofile.profile_picture %}
                                    <img src="{{ review.customer.customerprofile.profile_picture.url }}" 
                                         alt="Customer" class="img-fluid rounded-circle" style="width: 60px; height: 60px;">
                                {% else %}
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-user fa-2x text-muted"></i>
                                    </div>
                                {% endif %}
                                <div class="mt-2 text-center">
                                    <small class="text-muted">
                                        {{ review.customer.customerprofile.get_full_name|default:"Anonymous" }}
                                    </small>
                                </div>
                            </div>
                            
                            <!-- Review Content -->
                            <div class="col-md-7">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0">{{ review.venue.venue_name }}</h6>
                                    <div class="text-warning">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.rating %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                
                                <p class="mb-2">{{ review.written_review }}</p>
                                
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ review.created_at|date:"F d, Y g:i A" }}
                                </small>
                                
                                <!-- Status Indicators -->
                                <div class="mt-2">
                                    {% if review.is_flagged %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-flag me-1"></i>Flagged
                                        </span>
                                    {% endif %}
                                    {% if not review.is_approved %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>Not Approved
                                        </span>
                                    {% endif %}
                                    {% if review.response %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-reply me-1"></i>Responded
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="col-md-3 text-end">
                                {% if not review.response %}
                                    <a href="{% url 'review_app:provider_respond_to_review' review.id %}" 
                                       class="btn btn-primary btn-sm w-100 mb-2">
                                        <i class="fas fa-reply me-1"></i>Respond
                                    </a>
                                {% else %}
                                    <a href="{% url 'review_app:provider_respond_to_review' review.id %}" 
                                       class="btn btn-outline-primary btn-sm w-100 mb-2">
                                        <i class="fas fa-edit me-1"></i>Edit Response
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Provider Response (if any) -->
                        {% if review.response %}
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-link p-0" data-bs-toggle="collapse" data-bs-target="#resp-{{ review.id }}" aria-expanded="false" aria-controls="resp-{{ review.id }}">
                                    <i class="fas fa-reply me-2"></i>{% trans 'View Response' %}
                                </button>
                                <div id="resp-{{ review.id }}" class="collapse mt-2">
                                    <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <p class="mb-1">{{ review.response.response_text }}</p>
                                            <small class="text-muted">
                                                Responded on {{ review.response.created_at|date:"F d, Y g:i A" }}
                                                {% if review.response.updated_at != review.response.created_at %}
                                                    <span class="ms-2">
                                                        <i class="fas fa-edit me-1"></i>
                                                        Updated {{ review.response.updated_at|date:"F d, Y" }}
                                                    </span>
                                                {% endif %}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if reviews.has_other_pages %}
        <nav aria-label="Reviews pagination" class="mt-4 sticky-top" style="top:70px; z-index:101;">
            <ul class="pagination justify-content-center">
                {% if reviews.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.venue %}&venue={{ request.GET.venue }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.previous_page_number }}{% if request.GET.venue %}&venue={{ request.GET.venue }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in reviews.paginator.page_range %}
                    {% if reviews.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > reviews.number|add:'-3' and num < reviews.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if request.GET.venue %}&venue={{ request.GET.venue }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if reviews.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.next_page_number }}{% if request.GET.venue %}&venue={{ request.GET.venue }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.paginator.num_pages }}{% if request.GET.venue %}&venue={{ request.GET.venue }}{% endif %}{% if request.GET.rating %}&rating={{ request.GET.rating }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-star fa-4x text-muted mb-3"></i>
                        <h4>No Reviews Found</h4>
                        <p class="text-muted mb-4">
                            {% if request.GET.venue or request.GET.rating or request.GET.status or request.GET.search %}
                                No reviews match your current filters. Try adjusting your search criteria.
                            {% else %}
                                Your venues haven't received any reviews yet. Encourage customers to share their experiences!
                            {% endif %}
                        </p>
                        {% if request.GET.venue or request.GET.rating or request.GET.status or request.GET.search %}
                            <a href="{% url 'review_app:provider_venue_reviews' %}" class="btn btn-primary">
                                <i class="fas fa-refresh me-2"></i>Clear Filters
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
<button id="backToTop" class="btn btn-primary back-to-top" aria-label="Back to top">
    <i class="fas fa-arrow-up"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('reviews-skeleton').style.display = 'none';
    var btn = document.getElementById('backToTop');
    window.addEventListener('scroll', function(){
        if (window.pageYOffset > 300) { btn.style.display = 'block'; }
        else { btn.style.display = 'none'; }
    });
    btn.addEventListener('click', function(){
        window.scrollTo({top:0, behavior:'smooth'});
    });
});
</script>
{% endblock %}
