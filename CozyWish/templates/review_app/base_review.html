{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Review App - Black & White Design */
        /* Matching homepage and accounts_app design with clean typography and spacing */

        /* CSS Variables for fonts */
        :root {
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Review wrapper - clean white background */
        .review-wrapper {
            background-color: white;
            min-height: 100vh;
            padding: 2rem 0;
            font-family: var(--font-primary);
        }

        /* Typography */
        .review-wrapper h1, .review-wrapper h2, .review-wrapper h3,
        .review-wrapper h4, .review-wrapper h5, .review-wrapper h6 {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            margin-bottom: 1rem;
        }

        .review-wrapper p, .review-wrapper span, .review-wrapper div {
            color: black;
        }

        /* Cards - clean white with black border */
        .review-wrapper .card {
            background: white;
            border: 2px solid black;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .review-wrapper .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        /* Buttons - black and white theme */
        .review-wrapper .btn {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid black;
            transition: all 0.3s ease;
        }

        .review-wrapper .btn-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .review-wrapper .btn-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .review-wrapper .btn-outline-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .review-wrapper .btn-outline-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .review-wrapper .btn-success {
            background-color: white;
            color: black;
            border-color: black;
        }

        .review-wrapper .btn-success:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .review-wrapper .btn-danger {
            background-color: white;
            color: black;
            border-color: black;
        }

        .review-wrapper .btn-danger:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .review-wrapper .btn-warning {
            background-color: white;
            color: black;
            border-color: black;
        }

        .review-wrapper .btn-warning:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Form elements */
        .review-wrapper .form-control, .review-wrapper .form-select {
            font-family: var(--font-primary);
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            background-color: white;
            color: black;
        }

        .review-wrapper .form-control:focus, .review-wrapper .form-select:focus {
            border-color: black;
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
            background-color: white;
            color: black;
        }

        /* Labels */
        .review-wrapper .form-label {
            font-family: var(--font-primary);
            font-weight: 500;
            color: black;
            margin-bottom: 0.5rem;
        }

        /* Star ratings */
        .review-wrapper .star-rating {
            color: black;
            font-size: 1.25rem;
        }

        .review-wrapper .star-rating .star {
            color: rgba(0, 0, 0, 0.2);
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .review-wrapper .star-rating .star.filled {
            color: black;
        }

        .review-wrapper .star-rating .star:hover {
            color: black;
        }

        /* Review cards */
        .review-item {
            border: 2px solid black;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            background-color: white;
            transition: all 0.3s ease;
        }

        .review-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .review-author {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            margin-bottom: 0.5rem;
        }

        .review-date {
            font-size: 0.875rem;
            color: rgba(0, 0, 0, 0.6);
            font-family: var(--font-primary);
        }

        .review-text {
            color: black;
            font-family: var(--font-primary);
            line-height: 1.6;
            margin: 1rem 0;
        }

        /* Badges */
        .review-wrapper .badge {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }

        .review-wrapper .badge.bg-success {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .review-wrapper .badge.bg-primary {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .review-wrapper .badge.bg-warning {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .review-wrapper .badge.bg-danger {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .review-wrapper .badge.bg-info {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        /* Pagination */
        .review-wrapper .pagination .page-link {
            color: black;
            border: 2px solid black;
            background-color: white;
            font-family: var(--font-primary);
            font-weight: 500;
        }

        .review-wrapper .pagination .page-link:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .review-wrapper .pagination .page-item.active .page-link {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Text colors */
        .review-wrapper .text-muted {
            color: rgba(0, 0, 0, 0.6) !important;
        }

        .review-wrapper .text-primary {
            color: black !important;
        }

        .review-wrapper .text-success {
            color: black !important;
        }

        .review-wrapper .text-danger {
            color: black !important;
        }

        .review-wrapper .text-warning {
            color: black !important;
        }

        .review-wrapper .text-info {
            color: black !important;
        }

        /* Breadcrumb styling */
        .review-wrapper .breadcrumb {
            background-color: white;
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
        }

        .review-wrapper .breadcrumb-item a {
            color: black;
            text-decoration: none;
            font-weight: 500;
        }

        .review-wrapper .breadcrumb-item a:hover {
            color: rgba(0, 0, 0, 0.7);
        }

        .review-wrapper .breadcrumb-item.active {
            color: rgba(0, 0, 0, 0.6);
        }

        /* Card header styling */
        .review-wrapper .card-header {
            background-color: white !important;
            color: black;
            border-bottom: 2px solid black;
            font-family: var(--font-heading);
            font-weight: 600;
        }

        .review-wrapper .card-header.bg-light {
            background-color: white !important;
            color: black;
            border-bottom: 2px solid black;
        }

        /* Alert styling */
        .review-wrapper .alert {
            border: 2px solid black;
            border-radius: 0.75rem;
            background-color: white;
            color: black;
        }

        .review-wrapper .alert-info {
            border-color: black;
        }

        .review-wrapper .alert-success {
            border-color: black;
        }

        .review-wrapper .alert-warning {
            border-color: black;
        }

        .review-wrapper .alert-danger {
            border-color: black;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .review-wrapper {
                padding: 1rem 0;
            }

            .review-wrapper .card {
                margin-bottom: 1.5rem;
            }

            .review-item {
                padding: 1rem;
                margin-bottom: 1rem;
            }
        }
    </style>
    {% block review_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="review-wrapper">
    <div class="d-flex flex-column min-vh-100">
        <main class="flex-grow-1 d-flex align-items-center justify-content-center p-3">
            <div class="card w-100" style="max-width: 800px;">
                <div class="card-body p-4">
                    {% block review_content %}{% endblock %}
                </div>
            </div>
        </main>
        {% block below_card %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
    {% block review_extra_js %}{% endblock %}
{% endblock %}
