{% extends 'review_app/base_review.html' %}

{% block title %}My Reviews{% endblock %}

{% block extra_css %}
{% load static %}
<style>
/* Customer Review History - Professional Black & White Design */
.review-history-card {
    background: white;
    border: 2px solid black;
    border-radius: 1rem;
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.review-history-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.venue-image {
    border-radius: 0.75rem;
    border: 2px solid black;
    object-fit: cover;
    height: 100px;
    width: 100%;
}

.venue-placeholder {
    background: #f8f9fa;
    border: 2px solid black;
    border-radius: 0.75rem;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.review-content h5 a {
    color: black;
    text-decoration: none;
    font-weight: 600;
}

.review-content h5 a:hover {
    color: black;
    opacity: 0.8;
}

.rating-display {
    font-size: 1.1rem;
}

.review-text {
    color: black;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.review-meta {
    color: black;
    opacity: 0.7;
    font-size: 0.9rem;
}

.status-badges .badge {
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

.provider-response-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.75rem;
    padding: 1rem;
}

.btn-group-vertical .btn {
    margin-bottom: 0.5rem;
    border: 2px solid black;
    font-weight: 500;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

.empty-state {
    background: white;
    border: 2px solid black;
    border-radius: 1rem;
    padding: 3rem 2rem;
    text-align: center;
}

.empty-state i {
    color: black;
    opacity: 0.3;
}

.empty-state h4 {
    color: black;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state p {
    color: black;
    opacity: 0.7;
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-star me-2"></i>My Reviews
                </h2>
                <span class="badge bg-primary fs-6">{{ reviews.paginator.count }} Review{{ reviews.paginator.count|pluralize }}</span>
            </div>
        </div>
    </div>

    {% if reviews.object_list %}
        <!-- Reviews List -->
        <div class="row">
            {% for review in reviews %}
            <div class="col-12">
                <div class="review-history-card">
                    <div class="card-body p-4">
                        <div class="row">
                            <!-- Venue Image -->
                            <div class="col-md-2">
                                {% if review.venue.main_image %}
                                    <img src="{{ review.venue.main_image.url }}" alt="{{ review.venue.venue_name }}"
                                         class="venue-image">
                                {% else %}
                                    <div class="venue-placeholder">
                                        <i class="fas fa-image fa-2x text-muted"></i>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- Review Content -->
                            <div class="col-md-7 review-content">
                                <h5 class="mb-2">
                                    <a href="{% url 'venues_app:venue_detail' review.venue.slug %}">
                                        {{ review.venue.venue_name }}
                                    </a>
                                </h5>
                                <p class="text-muted mb-3">{{ review.venue.service_provider.business_name }}</p>

                                <!-- Rating -->
                                <div class="d-flex align-items-center mb-3 rating-display">
                                    <div class="text-warning me-2">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.rating %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <span class="review-meta">{{ review.rating }} out of 5 stars</span>
                                </div>

                                <!-- Review Text -->
                                <div class="review-text">{{ review.written_review|truncatewords:30 }}</div>

                                <!-- Review Date -->
                                <div class="review-meta">
                                    <i class="fas fa-calendar me-1"></i>
                                    Reviewed on {{ review.created_at|date:"F d, Y" }}
                                    {% if review.updated_at != review.created_at %}
                                        <span class="ms-2">
                                            <i class="fas fa-edit me-1"></i>
                                            Updated {{ review.updated_at|date:"F d, Y" }}
                                        </span>
                                    {% endif %}
                                </div>

                                <!-- Status Indicators -->
                                <div class="status-badges mt-3">
                                    {% if review.is_flagged %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-flag me-1"></i>Flagged
                                        </span>
                                    {% endif %}
                                    {% if not review.is_approved %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>Not Approved
                                        </span>
                                    {% endif %}
                                    {% if review.response %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-reply me-1"></i>Provider Responded
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="col-md-3 text-end">
                                <div class="btn-group-vertical w-100" role="group">
                                    <a href="{% url 'review_app:edit_review' review.id %}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </a>
                                    <a href="{% url 'venues_app:venue_detail' review.venue.slug %}" 
                                       class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View Venue
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Provider Response (if any) -->
                        {% if review.response %}
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="provider-response-card">
                                    <h6 class="mb-2 fw-bold">
                                        <i class="fas fa-reply me-2"></i>Provider Response
                                    </h6>
                                    <p class="mb-2">{{ review.response.response_text }}</p>
                                    <div class="review-meta">
                                        {{ review.response.created_at|date:"F d, Y" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if reviews.has_other_pages %}
        <nav aria-label="Reviews pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if reviews.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.previous_page_number }}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in reviews.paginator.page_range %}
                    {% if reviews.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > reviews.number|add:'-3' and num < reviews.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if reviews.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.next_page_number }}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.paginator.num_pages }}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-star fa-4x mb-4"></i>
                    <h4>No Reviews Yet</h4>
                    <p>You haven't written any reviews yet. Share your experiences to help other customers!</p>
                    <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Find Venues to Review
                    </a>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
