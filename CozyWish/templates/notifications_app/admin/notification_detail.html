{% extends 'base.html' %}

{% block title %}Notification Detail - Admin{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'notifications_app:admin_notification_dashboard' %}">
                            <i class="fas fa-bell me-1"></i>Notification Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'notifications_app:admin_notification_list' %}">All Notifications</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Notification Detail</li>
                </ol>
            </nav>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">
                                {% if notification.read_status == 'unread' %}
                                    <span class="badge bg-primary me-2">New</span>
                                {% endif %}
                                <span class="badge bg-secondary me-2">{{ notification.get_notification_type_display }}</span>
                                {{ notification.title }}
                            </h5>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>{{ notification.created_at|date:"M d, Y, g:i a" }}
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">Message</h6>
                        <div class="p-3 bg-light rounded">
                            {{ notification.message|linebreaks }}
                        </div>
                    </div>

                    {% if notification.action_url %}
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Action URL</h6>
                            <a href="{{ notification.action_url }}" class="btn btn-outline-primary" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>{{ notification.action_url }}
                            </a>
                        </div>
                    {% endif %}

                    {% if notification.related_object_id and notification.related_object_type %}
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Related Information</h6>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                This notification is related to {{ notification.related_object_type }} #{{ notification.related_object_id }}
                            </div>
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6 class="text-muted mb-2">Status</h6>
                                {% if notification.read_status == 'read' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Read
                                    </span>
                                    {% if notification.read_at %}
                                        <small class="text-muted ms-2">on {{ notification.read_at|date:"M d, Y, g:i a" }}</small>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-exclamation-circle me-1"></i>Unread
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6 class="text-muted mb-2">Created</h6>
                                <span class="text-muted">{{ notification.created_at|date:"M d, Y, g:i a" }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Information Sidebar -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Recipient Information</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="mb-2">
                            <i class="fas fa-user-circle fa-3x text-muted"></i>
                        </div>
                        <h6 class="mb-1">{{ notification.user.get_full_name|default:"No Name Provided" }}</h6>
                        <p class="text-muted small mb-0">{{ notification.user.email }}</p>
                    </div>

                    <hr>

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="mb-2">
                                <strong>{{ notification.user.role|title }}</strong>
                            </div>
                            <small class="text-muted">Account Type</small>
                        </div>
                        <div class="col-6">
                            <div class="mb-2">
                                <strong>{{ notification.user.date_joined|date:"M Y" }}</strong>
                            </div>
                            <small class="text-muted">Member Since</small>
                        </div>
                    </div>

                    <hr>

                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Account Status</h6>
                        {% if notification.user.is_active %}
                            <span class="badge bg-success">Active</span>
                        {% else %}
                            <span class="badge bg-danger">Inactive</span>
                        {% endif %}
                        
                        {% if notification.user.last_login %}
                            <div class="mt-2">
                                <small class="text-muted">
                                    Last login: {{ notification.user.last_login|date:"M d, Y, g:i a" }}
                                </small>
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid">
                        <a href="#" class="btn btn-outline-primary btn-sm" onclick="alert('User profile view not implemented')">
                            <i class="fas fa-user me-1"></i>View User Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Notification Statistics -->
            <div class="card shadow-sm mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>User Notification Stats</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="mb-2">
                                <strong>{{ user_notification_stats.total|default:0 }}</strong>
                            </div>
                            <small class="text-muted">Total Notifications</small>
                        </div>
                        <div class="col-6">
                            <div class="mb-2">
                                <strong>{{ user_notification_stats.unread|default:0 }}</strong>
                            </div>
                            <small class="text-muted">Unread</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <a href="{% url 'notifications_app:admin_notification_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Notifications
                </a>
                <div class="d-flex gap-2">
                    <a href="{% url 'notifications_app:admin_notification_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide success messages after 3 seconds
    const alerts = document.querySelectorAll('.alert-success');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 3000);
    });
});
</script>
{% endblock %}
