{% load static %}

<!-- Professional Notification Dropdown for Navigation Bar -->
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationDropdown" role="button"
       data-bs-toggle="dropdown" aria-expanded="false" title="Notifications">
        <i class="fas fa-bell"></i>
        {% if unread_notifications_count > 0 %}
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                  id="navNotificationBadge">
                {{ unread_notifications_count }}
                <span class="visually-hidden">unread notifications</span>
            </span>
        {% endif %}
    </a>

    <div class="dropdown-menu dropdown-menu-end professional-notification-dropdown"
         aria-labelledby="notificationDropdown">

        <!-- Professional Header -->
        <div class="notification-dropdown-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="notification-icon-header">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">Notifications</h6>
                        <small class="text-muted">Stay updated with your activity</small>
                    </div>
                </div>
                {% if unread_notifications_count > 0 %}
                    <span class="unread-count-badge" id="dropdownUnreadBadge">
                        {{ unread_notifications_count }}
                    </span>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        {% if unread_notifications_count > 0 %}
        <div class="notification-quick-actions">
            <form method="post" action="{% url 'notifications_app:mark_all_notifications_read' %}"
                  class="d-inline mark-all-read-form">
                {% csrf_token %}
                <button type="submit" class="quick-action-btn">
                    <i class="fas fa-check-double"></i>
                    Mark all read
                </button>
            </form>
        </div>
        {% endif %}

        <!-- Notification List -->
        <div class="professional-notification-list" id="dropdownNotificationList">
            {% if recent_notifications %}
                {% for notification in recent_notifications %}
                    <a class="professional-notification-item {% if notification.read_status == 'unread' %}unread{% endif %}"
                       href="{% url 'notifications_app:notification_detail' notification.id %}">
                        <div class="notification-item-content">
                            <div class="notification-item-icon">
                                {% if notification.notification_type == 'booking' %}
                                    <i class="fas fa-calendar-check"></i>
                                {% elif notification.notification_type == 'payment' %}
                                    <i class="fas fa-credit-card"></i>
                                {% elif notification.notification_type == 'review' %}
                                    <i class="fas fa-star"></i>
                                {% elif notification.notification_type == 'announcement' %}
                                    <i class="fas fa-bullhorn"></i>
                                {% else %}
                                    <i class="fas fa-bell"></i>
                                {% endif %}
                            </div>
                            <div class="notification-item-details">
                                <div class="notification-item-title">
                                    {% if notification.read_status == 'unread' %}
                                        <span class="unread-indicator"></span>
                                    {% endif %}
                                    {{ notification.title|truncatechars:35 }}
                                </div>
                                <div class="notification-item-message">
                                    {{ notification.message|truncatechars:70 }}
                                </div>
                                <div class="notification-item-meta">
                                    <span class="notification-type-badge {{ notification.notification_type }}">
                                        {{ notification.get_notification_type_display }}
                                    </span>
                                    <span class="notification-time">
                                        {{ notification.created_at|timesince }} ago
                                    </span>
                                </div>
                            </div>
                        </div>
                    </a>
                {% endfor %}
            {% else %}
                <div class="empty-notifications">
                    <div class="empty-icon">
                        <i class="fas fa-bell-slash"></i>
                    </div>
                    <div class="empty-text">
                        <h6>No notifications</h6>
                        <p>You're all caught up!</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Professional Footer -->
        <div class="notification-dropdown-footer">
            <a href="{% url 'notifications_app:notification_list' %}" class="view-all-btn">
                <i class="fas fa-list me-2"></i>
                View All Notifications
            </a>
        </div>
    </div>
</li>

<style>
/* Enhanced Professional Notification Dropdown Styles */
.professional-notification-dropdown {
    width: 340px;
    max-width: 95vw;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 1rem;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
    background: white;
    padding: 0;
    overflow: hidden;
    animation: dropdownSlideIn 0.25s ease-out;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

@keyframes dropdownSlideIn {
    from {
        opacity: 0;
        transform: translateY(-12px) scale(0.94);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.notification-dropdown-header {
    background: rgba(0, 0, 0, 0.01);
    padding: 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    position: relative;
}

.notification-icon-header {
    background: white;
    border: 1.5px solid rgba(0, 0, 0, 0.12);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.875rem;
    font-size: 0.9375rem;
    color: black;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.notification-icon-header:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.notification-dropdown-header h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    color: black;
    margin: 0;
    font-size: 1rem;
    letter-spacing: -0.025em;
}

.notification-dropdown-header small {
    color: rgba(0, 0, 0, 0.6);
    font-size: 0.8125rem;
    font-weight: 500;
    letter-spacing: 0.025em;
}

.unread-count-badge {
    background: black;
    color: white;
    border-radius: 1.25rem;
    padding: 0.375rem 0.625rem;
    font-size: 0.75rem;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.025em;
}

.notification-quick-actions {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: rgba(0, 0, 0, 0.005);
}

.quick-action-btn {
    background: white;
    border: 1.5px solid rgba(0, 0, 0, 0.12);
    border-radius: 0.75rem;
    padding: 0.625rem 0.875rem;
    font-size: 0.8125rem;
    font-weight: 600;
    color: black;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
    letter-spacing: 0.025em;
}

.quick-action-btn:hover {
    background: rgba(0, 0, 0, 0.02);
    border-color: rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.professional-notification-list {
    max-height: 380px;
    overflow-y: auto;
    padding: 0.75rem 0;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.professional-notification-list::-webkit-scrollbar {
    width: 6px;
}

.professional-notification-list::-webkit-scrollbar-track {
    background: transparent;
}

.professional-notification-list::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.professional-notification-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

.professional-notification-item {
    display: block;
    padding: 1rem 1.25rem;
    text-decoration: none;
    color: inherit;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;
    position: relative;
    margin: 0.125rem 0.375rem;
    border-radius: 0.75rem;
}

.professional-notification-item:hover {
    background: rgba(0, 0, 0, 0.02);
    color: inherit;
    text-decoration: none;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

.professional-notification-item.unread {
    background: rgba(0, 0, 0, 0.015);
    border-left: 4px solid black;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.notification-item-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-item-icon {
    background: white;
    border: 1.5px solid rgba(0, 0, 0, 0.12);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8125rem;
    color: black;
    flex-shrink: 0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;
}

.professional-notification-item:hover .notification-item-icon {
    transform: scale(1.05);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

.notification-item-details {
    flex: 1;
    min-width: 0;
}

.notification-item-title {
    font-weight: 600;
    color: black;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    line-height: 1.3;
}

.unread-indicator {
    width: 6px;
    height: 6px;
    background: #dc3545;
    border-radius: 50%;
    flex-shrink: 0;
}

.notification-item-message {
    color: rgba(0, 0, 0, 0.6);
    font-size: 0.75rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
    font-weight: 400;
}

.notification-item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
}

.notification-type-badge {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.75rem;
    padding: 0.125rem 0.375rem;
    font-size: 0.625rem;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.7);
}

.notification-type-badge.booking { border-color: #28a745; color: #28a745; }
.notification-type-badge.payment { border-color: #007bff; color: #007bff; }
.notification-type-badge.review { border-color: #ffc107; color: #856404; }
.notification-type-badge.announcement { border-color: #dc3545; color: #dc3545; }
.notification-type-badge.system { border-color: #6c757d; color: #6c757d; }

.notification-time {
    font-size: 0.625rem;
    color: rgba(0, 0, 0, 0.5);
}

.empty-notifications {
    text-align: center;
    padding: 2rem 1rem;
}

.empty-icon {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.25rem;
    color: rgba(0, 0, 0, 0.4);
}

.empty-text h6 {
    font-weight: 600;
    color: black;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.empty-text p {
    color: rgba(0, 0, 0, 0.6);
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.4;
}

.notification-dropdown-footer {
    background: rgba(0, 0, 0, 0.005);
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    padding: 1rem 1.25rem;
}

.view-all-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0.75rem;
    background: white;
    border: 1.5px solid rgba(0, 0, 0, 0.12);
    border-radius: 0.75rem;
    color: black;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    letter-spacing: 0.025em;
}

.view-all-btn:hover {
    background: rgba(0, 0, 0, 0.02);
    border-color: rgba(0, 0, 0, 0.2);
    color: black;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .professional-notification-dropdown {
        width: 300px;
    }

    .notification-dropdown-header,
    .notification-quick-actions,
    .notification-dropdown-footer {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .professional-notification-item {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
}
</style>
