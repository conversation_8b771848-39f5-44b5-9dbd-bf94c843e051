{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Payment Analytics" %} - CozyWish Admin{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container-fluid py-5 admin-bg">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Payment Analytics</li>
                    </ol>
                </nav>

                <h1 class="page-title mb-3">
                    <i class="fas fa-chart-line me-2"></i>Payment Analytics
                </h1>
                <p class="text-muted">Comprehensive payment performance and insights</p>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value">${{ total_revenue|floatformat:2 }}</div>
                        <div class="metric-label">Total Revenue</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value">{{ transaction_stats.total_transactions }}</div>
                        <div class="metric-label">Total Transactions</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value">{{ refund_stats.refund_rate|floatformat:1 }}%</div>
                        <div class="metric-label">Refund Rate</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card analytics-card">
                    <div class="card-body text-center">
                        <div class="metric-value">${{ monthly_revenue|floatformat:2 }}</div>
                        <div class="metric-label">Monthly Revenue</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Breakdown -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card analytics-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Revenue Breakdown</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">Weekly Revenue:</div>
                            <div class="col-6 text-end">${{ weekly_revenue|floatformat:2 }}</div>
                        </div>
                        <div class="row">
                            <div class="col-6">Monthly Revenue:</div>
                            <div class="col-6 text-end">${{ monthly_revenue|floatformat:2 }}</div>
                        </div>
                        <div class="row">
                            <div class="col-6">Yearly Revenue:</div>
                            <div class="col-6 text-end">${{ yearly_revenue|floatformat:2 }}</div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6"><strong>Platform Fees:</strong></div>
                            <div class="col-6 text-end"><strong>${{ total_platform_fees|floatformat:2 }}</strong></div>
                        </div>
                        <div class="row">
                            <div class="col-6"><strong>Provider Payouts:</strong></div>
                            <div class="col-6 text-end"><strong>${{ total_provider_payouts|floatformat:2 }}</strong></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card analytics-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Transaction Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">Successful:</div>
                            <div class="col-6 text-end text-success">{{ transaction_stats.successful_transactions }}</div>
                        </div>
                        <div class="row">
                            <div class="col-6">Failed:</div>
                            <div class="col-6 text-end text-danger">{{ transaction_stats.failed_transactions }}</div>
                        </div>
                        <div class="row">
                            <div class="col-6">Pending:</div>
                            <div class="col-6 text-end text-warning">{{ transaction_stats.pending_transactions }}</div>
                        </div>
                        <div class="row">
                            <div class="col-6">Success Rate:</div>
                            <div class="col-6 text-end">{{ transaction_stats.success_rate|floatformat:1 }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Refund Analytics -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card analytics-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Refund Analytics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4>{{ refund_stats.total_refunds }}</h4>
                                    <small class="text-muted">Total Refunds</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4>${{ refund_stats.total_refunded_amount|floatformat:2 }}</h4>
                                    <small class="text-muted">Total Refunded</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4>{{ refund_stats.pending_refunds }}</h4>
                                    <small class="text-muted">Pending Refunds</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4>{{ refund_stats.refund_rate|floatformat:1 }}%</h4>
                                    <small class="text-muted">Refund Rate</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Providers -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card analytics-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Top Earning Providers</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Provider</th>
                                        <th>Total Earnings</th>
                                        <th>Transactions</th>
                                        <th>Platform Fee</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for provider in provider_earnings %}
                                    <tr>
                                        <td>Provider #{{ provider.provider }}</td>
                                        <td>${{ provider.total_earnings|floatformat:2 }}</td>
                                        <td>{{ provider.transaction_count }}</td>
                                        <td>${{ provider.platform_fee|floatformat:2 }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">No provider data available</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row">
            <div class="col-md-6">
                <div class="card analytics-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Recent Payments</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Payment ID</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in recent_payments %}
                                    <tr>
                                        <td>{{ payment.payment_id|truncatechars:8 }}</td>
                                        <td>${{ payment.amount_paid }}</td>
                                        <td>
                                            <span class="badge bg-{% if payment.payment_status == 'succeeded' %}success{% elif payment.payment_status == 'failed' %}danger{% else %}warning{% endif %}">
                                                {{ payment.get_payment_status_display }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="3" class="text-center text-muted">No recent payments</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card analytics-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Recent Refunds</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Refund ID</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for refund in recent_refunds %}
                                    <tr>
                                        <td>{{ refund.refund_request_id|truncatechars:8 }}</td>
                                        <td>${{ refund.requested_amount }}</td>
                                        <td>
                                            <span class="badge bg-{% if refund.request_status == 'processed' %}success{% elif refund.request_status == 'declined' %}danger{% else %}warning{% endif %}">
                                                {{ refund.get_request_status_display }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="3" class="text-center text-muted">No recent refunds</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Placeholder for charts and interactive analytics
    console.log('Payment Analytics loaded');
</script>
{% endblock %}
