{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Refund Detail" %} - Admin - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container-fluid py-5 admin-bg">
    <div class="refund-detail-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'payments_app:admin_refund_list' %}">Refund Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Refund Detail</li>
                    </ol>
                </nav>
                <h1 class="h2">{% trans "Refund Request Detail" %}</h1>
                <p class="text-muted">{% trans "Refund ID:" %} {{ refund_request.refund_request_id }}</p>
            </div>
        </div>

        <!-- Refund Request Information -->
        <div class="card refund-card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-undo me-2"></i>{% trans "Refund Request Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Refund ID:" %}</strong>
                            <div class="text-muted">{{ refund_request.refund_request_id }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Requested Amount:" %}</strong>
                            <div class="text-warning h5">${{ refund_request.requested_amount }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Request Date:" %}</strong>
                            <div>{{ refund_request.created_at|date:"F d, Y \a\t g:i A" }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Status:" %}</strong>
                            <div>
                                <span class="badge status-badge bg-{% if refund_request.request_status == 'processed' %}success{% elif refund_request.request_status == 'declined' %}danger{% elif refund_request.request_status == 'approved' %}info{% else %}warning{% endif %}">
                                    {{ refund_request.get_request_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Reason Category:" %}</strong>
                            <div>{{ refund_request.get_reason_category_display }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Customer:" %}</strong>
                            <div>{{ refund_request.customer.email }}</div>
                        </div>
                        {% if refund_request.processed_amount %}
                        <div class="info-row">
                            <strong>{% trans "Processed Amount:" %}</strong>
                            <div class="text-success h6">${{ refund_request.processed_amount }}</div>
                        </div>
                        {% endif %}
                        {% if refund_request.reviewed_by %}
                        <div class="info-row">
                            <strong>{% trans "Reviewed By:" %}</strong>
                            <div>{{ refund_request.reviewed_by.email }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Reason Description -->
                <div class="row mt-3">
                    <div class="col-12">
                        <strong>{% trans "Detailed Description:" %}</strong>
                        <div class="mt-2 p-3 bg-light rounded">
                            {{ refund_request.reason_description }}
                        </div>
                    </div>
                </div>

                <!-- Admin Notes -->
                {% if refund_request.admin_notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <strong>{% trans "Admin Notes:" %}</strong>
                        <div class="mt-2 p-3 bg-info bg-opacity-10 rounded">
                            {{ refund_request.admin_notes }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Related Payment Information -->
        <div class="card refund-card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>{% trans "Related Payment" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Payment ID:" %}</strong>
                            <div>
                                <a href="{% url 'payments_app:admin_payment_detail' refund_request.payment.payment_id %}" 
                                   class="text-decoration-none">
                                    {{ refund_request.payment.payment_id }}
                                </a>
                            </div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Original Amount:" %}</strong>
                            <div class="text-primary h6">${{ refund_request.payment.amount_paid }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Payment Status:" %}</strong>
                            <div>
                                <span class="badge bg-{% if refund_request.payment.payment_status == 'succeeded' %}success{% elif refund_request.payment.payment_status == 'failed' %}danger{% else %}warning{% endif %}">
                                    {{ refund_request.payment.get_payment_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Payment Date:" %}</strong>
                            <div>{{ refund_request.payment.payment_date|date:"F d, Y" }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Already Refunded:" %}</strong>
                            <div class="text-danger">${{ refund_request.payment.refunded_amount|default:0 }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Remaining Refundable:" %}</strong>
                            <div class="text-success">${{ refund_request.payment.remaining_refundable_amount }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Information -->
        <div class="card refund-card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>{% trans "Booking Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Booking ID:" %}</strong>
                            <div>{{ refund_request.payment.booking.booking_id }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Venue:" %}</strong>
                            <div>{{ refund_request.payment.booking.venue.venue_name }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Service:" %}</strong>
                            <div>{{ refund_request.payment.booking.service.service_title|default:"N/A" }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-row">
                            <strong>{% trans "Booking Date:" %}</strong>
                            <div>{{ refund_request.payment.booking.booking_date|date:"F d, Y" }}</div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Booking Status:" %}</strong>
                            <div>
                                <span class="badge bg-{% if refund_request.payment.booking.status == 'confirmed' %}success{% elif refund_request.payment.booking.status == 'cancelled' %}danger{% else %}warning{% endif %}">
                                    {{ refund_request.payment.booking.get_status_display }}
                                </span>
                            </div>
                        </div>
                        <div class="info-row">
                            <strong>{% trans "Provider:" %}</strong>
                            <div>{{ refund_request.payment.provider.email }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Actions -->
        {% if refund_request.request_status == 'pending' %}
        <div class="card refund-card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>{% trans "Admin Actions" %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="action-form">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="action" class="form-label">
                                    <strong>{% trans "Action" %}</strong>
                                </label>
                                <select name="action" id="action" class="form-select" required>
                                    <option value="">{% trans "Select an action" %}</option>
                                    <option value="approve">{% trans "Approve Refund" %}</option>
                                    <option value="decline">{% trans "Decline Refund" %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="processed_amount" class="form-label">
                                    <strong>{% trans "Approved Amount" %}</strong>
                                    <small class="text-muted">({% trans "if approving" %})</small>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="processed_amount" id="processed_amount" 
                                           class="form-control" step="0.01" 
                                           max="{{ refund_request.payment.remaining_refundable_amount }}"
                                           value="{{ refund_request.requested_amount }}">
                                </div>
                                <div class="form-text">
                                    {% trans "Maximum:" %} ${{ refund_request.payment.remaining_refundable_amount }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">
                            <strong>{% trans "Admin Notes" %}</strong>
                        </label>
                        <textarea name="admin_notes" id="admin_notes" class="form-control" rows="3" 
                                  placeholder="{% trans 'Add notes about your decision...' %}"></textarea>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'payments_app:admin_refund_list' %}" class="btn btn-secondary me-md-2">
                            {% trans "Back to List" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check me-2"></i>{% trans "Submit Decision" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        {% elif refund_request.request_status == 'approved' %}
        <!-- Process Approved Refund -->
        <div class="card refund-card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-check-circle me-2"></i>{% trans "Process Approved Refund" %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="action-form">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="process">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="processed_amount" class="form-label">
                                    <strong>{% trans "Amount to Process" %}</strong>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="processed_amount" id="processed_amount" 
                                           class="form-control" step="0.01" 
                                           value="{{ refund_request.requested_amount }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">
                                    <strong>{% trans "Refund Method" %}</strong>
                                </label>
                                <div class="form-control-plaintext">
                                    {% trans "Original payment method" %} ({{ refund_request.payment.get_payment_method_display }})
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Processing this refund will initiate the actual refund transaction. This action cannot be undone." %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'payments_app:admin_refund_list' %}" class="btn btn-secondary me-md-2">
                            {% trans "Back to List" %}
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-money-bill-wave me-2"></i>{% trans "Process Refund" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        {% else %}
        <!-- Completed Actions -->
        <div class="card refund-card">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>{% trans "Refund History" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>{% trans "Final Status:" %}</strong> 
                            <span class="badge bg-{% if refund_request.request_status == 'processed' %}success{% else %}danger{% endif %}">
                                {{ refund_request.get_request_status_display }}
                            </span>
                        </p>
                        {% if refund_request.reviewed_at %}
                        <p><strong>{% trans "Reviewed Date:" %}</strong> {{ refund_request.reviewed_at|date:"F d, Y \a\t g:i A" }}</p>
                        {% endif %}
                        {% if refund_request.processed_at %}
                        <p><strong>{% trans "Processed Date:" %}</strong> {{ refund_request.processed_at|date:"F d, Y \a\t g:i A" }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if refund_request.processed_amount %}
                        <p><strong>{% trans "Final Amount:" %}</strong> 
                            <span class="text-success h6">${{ refund_request.processed_amount }}</span>
                        </p>
                        {% endif %}
                        <div class="d-grid">
                            <a href="{% url 'payments_app:admin_refund_list' %}" class="btn btn-secondary">
                                {% trans "Back to List" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Auto-fill processed amount based on action
    document.getElementById('action')?.addEventListener('change', function() {
        const processedAmountField = document.getElementById('processed_amount');
        if (this.value === 'decline') {
            processedAmountField.value = '0.00';
            processedAmountField.disabled = true;
        } else {
            processedAmountField.disabled = false;
            processedAmountField.value = '{{ refund_request.requested_amount }}';
        }
    });
</script>
{% endblock %}
