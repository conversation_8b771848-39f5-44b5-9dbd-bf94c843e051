{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Refund Management" %} - Admin - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container-fluid py-5 admin-bg">
    <div class="refund-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Refund Management</li>
                    </ol>
                </nav>
                <h1 class="h2">{% trans "Refund Management" %}</h1>
                <p class="text-muted">{% trans "Review and manage customer refund requests" %}</p>
            </div>
        </div>

        <!-- Refund Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ refund_stats.total_refunds }}</h4>
                        <small>{% trans "Total Refunds" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ refund_stats.pending_refunds }}</h4>
                        <small>{% trans "Pending Review" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>${{ refund_stats.total_refunded_amount|floatformat:2 }}</h4>
                        <small>{% trans "Total Refunded" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ refund_stats.refund_rate|floatformat:1 }}%</h4>
                        <small>{% trans "Refund Rate" %}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="filter-card">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="{{ search_form.search_query.id_for_label }}" class="form-label">
                        {% trans "Search" %}
                    </label>
                    {{ search_form.search_query }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.status.id_for_label }}" class="form-label">
                        {% trans "Status" %}
                    </label>
                    {{ search_form.status }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.reason_category.id_for_label }}" class="form-label">
                        {% trans "Reason" %}
                    </label>
                    {{ search_form.reason_category }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.date_from.id_for_label }}" class="form-label">
                        {% trans "From" %}
                    </label>
                    {{ search_form.date_from }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.date_to.id_for_label }}" class="form-label">
                        {% trans "To" %}
                    </label>
                    {{ search_form.date_to }}
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>

            <!-- Active Filters -->
            {% if active_filters %}
            <div class="mt-3">
                <small class="text-muted">{% trans "Active filters:" %}</small>
                {% for filter in active_filters %}
                    <span class="badge bg-secondary me-1">{{ filter }}</span>
                {% endfor %}
                <a href="{% url 'payments_app:admin_refund_list' %}" class="btn btn-sm btn-outline-secondary ms-2">
                    {% trans "Clear All" %}
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Quick Actions -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="btn-group">
                    <a href="?status=pending" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-clock me-1"></i>{% trans "Pending Review" %}
                    </a>
                    <a href="?status=approved" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-check me-1"></i>{% trans "Approved" %}
                    </a>
                    <a href="?status=processed" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-money-bill-wave me-1"></i>{% trans "Processed" %}
                    </a>
                    <a href="?status=declined" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-times me-1"></i>{% trans "Declined" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Refund List -->
        <div class="row">
            {% for refund in refund_requests %}
            <div class="col-12">
                <div class="card refund-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Refund ID" %}</small>
                                <div class="fw-bold">{{ refund.refund_request_id|truncatechars:12 }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Date" %}</small>
                                <div>{{ refund.created_at|date:"M d, Y" }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Amount" %}</small>
                                <div class="fw-bold text-warning">${{ refund.requested_amount }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Status" %}</small>
                                <div>
                                    <span class="badge status-badge bg-{% if refund.request_status == 'processed' %}success{% elif refund.request_status == 'declined' %}danger{% elif refund.request_status == 'approved' %}info{% else %}warning{% endif %}">
                                        {{ refund.get_request_status_display }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Customer" %}</small>
                                <div>{{ refund.customer.email|truncatechars:20 }}</div>
                            </div>
                            <div class="col-md-2">
                                <div class="btn-group">
                                    <a href="{% url 'payments_app:admin_refund_detail' refund.refund_request_id %}"
                                       class="btn btn-sm btn-outline-primary">
                                        {% trans "Review" %}
                                    </a>
                                    {% if refund.request_status == 'pending' %}
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            {% trans "Quick Action" %}
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <form method="post" action="{% url 'payments_app:admin_refund_approve' refund.refund_request_id %}" class="d-inline">
                                                    {% csrf_token %}
                                                    <button type="submit" class="dropdown-item text-success">
                                                        <i class="fas fa-check me-2"></i>{% trans "Quick Approve" %}
                                                    </button>
                                                </form>
                                            </li>
                                            <li>
                                                <form method="post" action="{% url 'payments_app:admin_refund_decline' refund.refund_request_id %}" class="d-inline">
                                                    {% csrf_token %}
                                                    <button type="submit" class="dropdown-item text-danger">
                                                        <i class="fas fa-times me-2"></i>{% trans "Quick Decline" %}
                                                    </button>
                                                </form>
                                            </li>
                                        </ul>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Refund Details -->
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-tag me-1"></i>
                                    {% trans "Reason:" %} {{ refund.get_reason_category_display }}
                                    {% if refund.payment %}
                                        | <i class="fas fa-credit-card me-1"></i>{% trans "Payment:" %} {{ refund.payment.payment_id|truncatechars:12 }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>

                        <!-- Review Information -->
                        {% if refund.reviewed_by %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-user-check me-1"></i>
                                    {% trans "Reviewed by:" %} {{ refund.reviewed_by.email }}
                                    {% if refund.reviewed_at %}
                                        | {% trans "on" %} {{ refund.reviewed_at|date:"M d, Y" }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-undo fa-3x text-muted mb-3"></i>
                        <h5>{% trans "No refund requests found" %}</h5>
                        <p class="text-muted">{% trans "No refund requests match your search criteria." %}</p>
                        <a href="{% url 'payments_app:admin_refund_list' %}" class="btn btn-primary">
                            {% trans "View All Refunds" %}
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Refund list pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page=1">{% trans "First" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.paginator.num_pages }}">{% trans "Last" %}</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('select[name="status"], select[name="reason_category"]').forEach(function(select) {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Confirmation for quick actions
    document.querySelectorAll('form[action*="approve"], form[action*="decline"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const action = this.action.includes('approve') ? 'approve' : 'decline';
            if (!confirm(`Are you sure you want to ${action} this refund request?`)) {
                e.preventDefault();
            }
        });
    });
</script>
{% endblock %}
