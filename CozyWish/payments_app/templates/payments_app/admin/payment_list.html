{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Payment Management" %} - Admin - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
    <div class="payment-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_app:admin_dashboard' %}">Admin Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Payment Management</li>
                    </ol>
                </nav>
                <h1 class="h2">{% trans "Payment Management" %}</h1>
                <p class="text-muted">{% trans "Monitor and manage all payment transactions" %}</p>
            </div>
        </div>

        <!-- Payment Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ payment_stats.total_payments }}</h4>
                        <small>{% trans "Total Payments" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ payment_stats.successful_payments }}</h4>
                        <small>{% trans "Successful" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>${{ payment_stats.total_revenue|floatformat:2 }}</h4>
                        <small>{% trans "Total Revenue" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ payment_stats.pending_payments }}</h4>
                        <small>{% trans "Pending" %}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="filter-card">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="{{ search_form.search_query.id_for_label }}" class="form-label">
                        {% trans "Search" %}
                    </label>
                    {{ search_form.search_query }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.status.id_for_label }}" class="form-label">
                        {% trans "Status" %}
                    </label>
                    {{ search_form.status }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.payment_method.id_for_label }}" class="form-label">
                        {% trans "Method" %}
                    </label>
                    {{ search_form.payment_method }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.date_from.id_for_label }}" class="form-label">
                        {% trans "From" %}
                    </label>
                    {{ search_form.date_from }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.date_to.id_for_label }}" class="form-label">
                        {% trans "To" %}
                    </label>
                    {{ search_form.date_to }}
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>

            <!-- Active Filters -->
            {% if active_filters %}
            <div class="mt-3">
                <small class="text-muted">{% trans "Active filters:" %}</small>
                {% for filter in active_filters %}
                    <span class="badge bg-secondary me-1">{{ filter }}</span>
                {% endfor %}
                <a href="{% url 'payments_app:admin_payment_list' %}" class="btn btn-sm btn-outline-secondary ms-2">
                    {% trans "Clear All" %}
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Quick Actions -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="btn-group">
                    <a href="?status=succeeded" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-check me-1"></i>{% trans "Successful" %}
                    </a>
                    <a href="?status=pending" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-clock me-1"></i>{% trans "Pending" %}
                    </a>
                    <a href="?status=failed" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-times me-1"></i>{% trans "Failed" %}
                    </a>
                    <a href="{% url 'payments_app:admin_disputed_payments' %}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-exclamation-triangle me-1"></i>{% trans "Disputed" %}
                    </a>
                    <a href="{% url 'payments_app:admin_payment_analytics' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-chart-line me-1"></i>{% trans "Analytics" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Payment List -->
        <div class="row">
            {% for payment in payments %}
            <div class="col-12">
                <div class="card payment-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Payment ID" %}</small>
                                <div class="fw-bold">{{ payment.payment_id|truncatechars:12 }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Date" %}</small>
                                <div>{{ payment.payment_date|date:"M d, Y" }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Amount" %}</small>
                                <div class="fw-bold text-primary">${{ payment.amount_paid }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Status" %}</small>
                                <div>
                                    <span class="badge status-badge bg-{% if payment.payment_status == 'succeeded' %}success{% elif payment.payment_status == 'failed' %}danger{% elif payment.payment_status == 'pending' %}warning{% else %}secondary{% endif %}">
                                        {{ payment.get_payment_status_display }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Customer" %}</small>
                                <div>{{ payment.customer.email|truncatechars:20 }}</div>
                            </div>
                            <div class="col-md-2">
                                <div class="btn-group">
                                    <a href="{% url 'payments_app:admin_payment_detail' payment.payment_id %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        {% trans "View" %}
                                    </a>
                                    {% if payment.refund_requests.exists %}
                                    <a href="{% url 'payments_app:admin_refund_list' %}?payment={{ payment.payment_id }}" 
                                       class="btn btn-sm btn-outline-warning">
                                        {% trans "Refunds" %}
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Details -->
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-credit-card me-1"></i>
                                    {% trans "Method:" %} {{ payment.get_payment_method_display }}
                                    {% if payment.booking %}
                                        | <i class="fas fa-map-marker-alt me-1"></i>{{ payment.booking.venue.venue_name|truncatechars:30 }}
                                    {% endif %}
                                    {% if payment.provider %}
                                        | <i class="fas fa-user-tie me-1"></i>{{ payment.provider.email|truncatechars:25 }}
                                    {% endif %}
                                </small>
                            </div>
                        </div>

                        <!-- Refund Information -->
                        {% if payment.refunded_amount > 0 %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-warning">
                                    <i class="fas fa-undo me-1"></i>
                                    {% trans "Refunded:" %} ${{ payment.refunded_amount }}
                                    | {% trans "Remaining:" %} ${{ payment.remaining_refundable_amount }}
                                </small>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Failure Information -->
                        {% if payment.failure_reason %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-danger">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    {% trans "Failure:" %} {{ payment.failure_reason|truncatechars:50 }}
                                </small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5>{% trans "No payments found" %}</h5>
                        <p class="text-muted">{% trans "No payments match your search criteria." %}</p>
                        <a href="{% url 'payments_app:admin_payment_list' %}" class="btn btn-primary">
                            {% trans "View All Payments" %}
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Payment list pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page=1">{% trans "First" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.paginator.num_pages }}">{% trans "Last" %}</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}
    </div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('select[name="status"], select[name="payment_method"]').forEach(function(select) {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
{% endblock %}
