{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Request Refund" %} - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container py-5">
    <div class="refund-container">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="h2">{% trans "Request Refund" %}</h1>
            <p class="text-muted">{% trans "Submit a refund request for your payment" %}</p>
        </div>

        <!-- Payment Summary -->
        <div class="payment-summary">
            <h3>{% trans "Payment Summary" %}</h3>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>{% trans "Payment ID:" %}</strong> {{ payment.payment_id|truncatechars:16 }}</p>
                    <p><strong>{% trans "Amount Paid:" %}</strong> ${{ payment.amount_paid }}</p>
                    <p><strong>{% trans "Payment Date:" %}</strong> {{ payment.payment_date|date:"F d, Y" }}</p>
                    <p><strong>{% trans "Status:" %}</strong> 
                        <span class="badge bg-success">{{ payment.get_payment_status_display }}</span>
                    </p>
                </div>
                <div class="col-md-6">
                    <p><strong>{% trans "Venue:" %}</strong> {{ payment.booking.venue.venue_name }}</p>
                    <p><strong>{% trans "Service:" %}</strong> {{ payment.booking.service.service_title|default:"N/A" }}</p>
                    <p><strong>{% trans "Booking Date:" %}</strong> {{ payment.booking.booking_date|date:"F d, Y" }}</p>
                    <p><strong>{% trans "Refunded Amount:" %}</strong> ${{ payment.refunded_amount|default:0 }}</p>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <div class="alert alert-info">
                        <strong>{% trans "Available for Refund:" %}</strong> ${{ payment.remaining_refundable_amount }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Refund Policy -->
        <div class="refund-policy">
            <h5><i class="fas fa-info-circle me-2"></i>{% trans "Refund Policy" %}</h5>
            <ul class="mb-0">
                <li>{% trans "Refund requests are reviewed within 3-5 business days" %}</li>
                <li>{% trans "Full refunds are available up to 24 hours before the booking" %}</li>
                <li>{% trans "Partial refunds may be available depending on the circumstances" %}</li>
                <li>{% trans "Service provider policies may apply" %}</li>
            </ul>
        </div>

        <!-- Refund Request Form -->
        <div class="refund-form">
            <h3>{% trans "Refund Request Details" %}</h3>
            <form method="post">
                {% csrf_token %}
                
                <!-- Reason Category -->
                <div class="form-section">
                    <label for="{{ form.reason_category.id_for_label }}" class="form-label">
                        <strong>{% trans "Reason Category" %}</strong>
                        <span class="text-danger">*</span>
                    </label>
                    {{ form.reason_category }}
                    {% if form.reason_category.errors %}
                        <div class="text-danger mt-1">{{ form.reason_category.errors }}</div>
                    {% endif %}
                    <div class="form-text">{% trans "Please select the category that best describes your refund reason" %}</div>
                </div>

                <!-- Reason Description -->
                <div class="form-section">
                    <label for="{{ form.reason_description.id_for_label }}" class="form-label">
                        <strong>{% trans "Detailed Description" %}</strong>
                        <span class="text-danger">*</span>
                    </label>
                    {{ form.reason_description }}
                    {% if form.reason_description.errors %}
                        <div class="text-danger mt-1">{{ form.reason_description.errors }}</div>
                    {% endif %}
                    <div class="form-text">{% trans "Please provide a detailed explanation for your refund request. This will help us process your request faster." %}</div>
                </div>

                <!-- Requested Amount -->
                <div class="form-section">
                    <label for="{{ form.requested_amount.id_for_label }}" class="form-label">
                        <strong>{% trans "Requested Refund Amount" %}</strong>
                        <span class="text-danger">*</span>
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">$</span>
                        {{ form.requested_amount }}
                    </div>
                    {% if form.requested_amount.errors %}
                        <div class="text-danger mt-1">{{ form.requested_amount.errors }}</div>
                    {% endif %}
                    <div class="form-text">
                        {% trans "Maximum refundable amount:" %} ${{ payment.remaining_refundable_amount }}
                    </div>
                </div>

                <!-- Supporting Documentation -->
                {% if form.supporting_documentation %}
                <div class="form-section">
                    <label for="{{ form.supporting_documentation.id_for_label }}" class="form-label">
                        <strong>{% trans "Supporting Documentation" %}</strong>
                        <small class="text-muted">({% trans "Optional" %})</small>
                    </label>
                    {{ form.supporting_documentation }}
                    {% if form.supporting_documentation.errors %}
                        <div class="text-danger mt-1">{{ form.supporting_documentation.errors }}</div>
                    {% endif %}
                    <div class="form-text">{% trans "Upload any supporting documents (receipts, photos, etc.) that support your refund request" %}</div>
                </div>
                {% endif %}

                <!-- Terms Agreement -->
                <div class="form-section">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="terms_agreement" required>
                        <label class="form-check-label" for="terms_agreement">
                            {% trans "I understand that this refund request will be reviewed according to the refund policy and service provider terms" %}
                            <span class="text-danger">*</span>
                        </label>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="form-section">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'payments_app:payment_detail' payment.payment_id %}" 
                           class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-paper-plane me-2"></i>{% trans "Submit Refund Request" %}
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- What Happens Next -->
        <div class="mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>{% trans "What Happens Next?" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="mb-3">
                                <i class="fas fa-paper-plane fa-2x text-primary"></i>
                            </div>
                            <h6>{% trans "1. Request Submitted" %}</h6>
                            <p class="text-muted small">{% trans "Your refund request is submitted and you'll receive a confirmation email" %}</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="mb-3">
                                <i class="fas fa-search fa-2x text-info"></i>
                            </div>
                            <h6>{% trans "2. Review Process" %}</h6>
                            <p class="text-muted small">{% trans "Our team reviews your request within 3-5 business days" %}</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="mb-3">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                            <h6>{% trans "3. Decision & Processing" %}</h6>
                            <p class="text-muted small">{% trans "You'll be notified of the decision and refunds are processed within 5-7 business days" %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Auto-calculate maximum refund amount
    document.addEventListener('DOMContentLoaded', function() {
        const amountInput = document.querySelector('input[name="requested_amount"]');
        const maxAmount = {{ payment.remaining_refundable_amount }};
        
        if (amountInput) {
            amountInput.addEventListener('input', function() {
                const value = parseFloat(this.value);
                if (value > maxAmount) {
                    this.setCustomValidity('Amount exceeds maximum refundable amount of $' + maxAmount);
                } else {
                    this.setCustomValidity('');
                }
            });
        }
    });
</script>
{% endblock %}
