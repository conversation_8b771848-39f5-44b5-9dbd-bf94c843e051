{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Payment History" %} - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container py-5">
    <div class="payment-history-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h2">{% trans "Payment History" %}</h1>
                <p class="text-muted">{% trans "View and manage your payment transactions" %}</p>
            </div>
        </div>

        <!-- Payment Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ payment_stats.total_payments }}</h4>
                        <small>{% trans "Total Payments" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>${{ payment_stats.total_amount|floatformat:2 }}</h4>
                        <small>{% trans "Total Amount" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ payment_stats.successful_payments }}</h4>
                        <small>{% trans "Successful" %}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4>{{ payment_stats.pending_payments }}</h4>
                        <small>{% trans "Pending" %}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="filter-card">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="{{ search_form.search_query.id_for_label }}" class="form-label">
                        {% trans "Search" %}
                    </label>
                    {{ search_form.search_query }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.status.id_for_label }}" class="form-label">
                        {% trans "Status" %}
                    </label>
                    {{ search_form.status }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.payment_method.id_for_label }}" class="form-label">
                        {% trans "Method" %}
                    </label>
                    {{ search_form.payment_method }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.date_from.id_for_label }}" class="form-label">
                        {% trans "From" %}
                    </label>
                    {{ search_form.date_from }}
                </div>
                <div class="col-md-2">
                    <label for="{{ search_form.date_to.id_for_label }}" class="form-label">
                        {% trans "To" %}
                    </label>
                    {{ search_form.date_to }}
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>

            <!-- Active Filters -->
            {% if active_filters %}
            <div class="mt-3">
                <small class="text-muted">{% trans "Active filters:" %}</small>
                {% for filter in active_filters %}
                    <span class="badge bg-secondary me-1">{{ filter }}</span>
                {% endfor %}
                <a href="{% url 'payments_app:payment_history' %}" class="btn btn-sm btn-outline-secondary ms-2">
                    {% trans "Clear All" %}
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Export Options -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="btn-group">
                    <a href="?{{ request.GET.urlencode }}&format=csv" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-file-csv me-1"></i>{% trans "Export CSV" %}
                    </a>
                    <a href="?{{ request.GET.urlencode }}&format=pdf" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-file-pdf me-1"></i>{% trans "Export PDF" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Payment List -->
        <div class="row">
            {% for payment in payments %}
            <div class="col-12">
                <div class="card payment-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Payment ID" %}</small>
                                <div class="fw-bold">{{ payment.payment_id|truncatechars:12 }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Date" %}</small>
                                <div>{{ payment.payment_date|date:"M d, Y" }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Amount" %}</small>
                                <div class="fw-bold text-primary">${{ payment.amount_paid }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Status" %}</small>
                                <div>
                                    <span class="badge status-badge bg-{% if payment.payment_status == 'succeeded' %}success{% elif payment.payment_status == 'failed' %}danger{% elif payment.payment_status == 'pending' %}warning{% else %}secondary{% endif %}">
                                        {{ payment.get_payment_status_display }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">{% trans "Venue" %}</small>
                                <div>{{ payment.booking.venue.venue_name|truncatechars:20 }}</div>
                            </div>
                            <div class="col-md-2">
                                <div class="btn-group">
                                    <a href="{% url 'payments_app:payment_detail' payment.payment_id %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        {% trans "View" %}
                                    </a>
                                    {% if payment.is_refundable %}
                                    <a href="{% url 'payments_app:refund_request' payment.payment_id %}" 
                                       class="btn btn-sm btn-outline-warning">
                                        {% trans "Refund" %}
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Refund Status -->
                        {% if payment.refund_requests.exists %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-undo me-1"></i>
                                    {% trans "Refund Status:" %}
                                    {% for refund in payment.refund_requests.all %}
                                        <span class="badge bg-info">{{ refund.get_request_status_display }}</span>
                                    {% endfor %}
                                </small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5>{% trans "No payments found" %}</h5>
                        <p class="text-muted">{% trans "You haven't made any payments yet or no payments match your search criteria." %}</p>
                        <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">
                            {% trans "Browse Venues" %}
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Payment history pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page=1">{% trans "First" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ page_obj.paginator.num_pages }}">{% trans "Last" %}</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('select[name="status"], select[name="payment_method"]').forEach(function(select) {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
{% endblock %}
