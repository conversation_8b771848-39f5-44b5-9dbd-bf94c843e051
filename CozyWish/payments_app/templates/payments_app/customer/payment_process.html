{% extends 'payments_app/base_payments.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Processing Payment" %} - CozyWish{% endblock %}

{% block payments_extra_css %}{% endblock %}

{% block payments_content %}
<div class="container py-5">
    <div class="process-container">
        <div class="process-card">
            {% if payment.payment_status == 'succeeded' %}
                <!-- Payment Successful -->
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2 class="text-success mb-3">{% trans "Payment Successful!" %}</h2>
                <p class="text-muted mb-4">
                    {% trans "Your payment has been processed successfully. You will receive a confirmation email shortly." %}
                </p>
            {% elif payment.payment_status == 'pending' %}
                <!-- Payment Processing -->
                <div class="spinner"></div>
                <h2 class="mb-3">{% trans "Processing Your Payment..." %}</h2>
                <p class="text-muted mb-4">
                    {% trans "Please wait while we process your payment. This may take a few moments." %}
                </p>
                <script>
                    // Auto-refresh to check payment status
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                </script>
            {% elif payment.payment_status == 'failed' %}
                <!-- Payment Failed -->
                <div class="text-danger mb-3">
                    <i class="fas fa-times-circle fa-4x"></i>
                </div>
                <h2 class="text-danger mb-3">{% trans "Payment Failed" %}</h2>
                <p class="text-muted mb-4">
                    {% trans "Unfortunately, your payment could not be processed. Please try again or contact support." %}
                </p>
                {% if payment.failure_reason %}
                <div class="alert alert-danger">
                    <strong>{% trans "Reason:" %}</strong> {{ payment.failure_reason }}
                </div>
                {% endif %}
            {% else %}
                <!-- Unknown Status -->
                <div class="text-warning mb-3">
                    <i class="fas fa-exclamation-triangle fa-4x"></i>
                </div>
                <h2 class="text-warning mb-3">{% trans "Payment Status Unknown" %}</h2>
                <p class="text-muted mb-4">
                    {% trans "We're checking the status of your payment. Please wait a moment." %}
                </p>
            {% endif %}

            <!-- Payment Details -->
            <div class="payment-details">
                <h5>{% trans "Payment Details" %}</h5>
                <div class="row text-start">
                    <div class="col-6">
                        <strong>{% trans "Payment ID:" %}</strong>
                    </div>
                    <div class="col-6">
                        {{ payment.payment_id|truncatechars:16 }}
                    </div>
                </div>
                <div class="row text-start">
                    <div class="col-6">
                        <strong>{% trans "Amount:" %}</strong>
                    </div>
                    <div class="col-6">
                        ${{ payment.amount_paid }}
                    </div>
                </div>
                <div class="row text-start">
                    <div class="col-6">
                        <strong>{% trans "Date:" %}</strong>
                    </div>
                    <div class="col-6">
                        {{ payment.payment_date|date:"M d, Y g:i A" }}
                    </div>
                </div>
                <div class="row text-start">
                    <div class="col-6">
                        <strong>{% trans "Status:" %}</strong>
                    </div>
                    <div class="col-6">
                        <span class="badge bg-{% if payment.payment_status == 'succeeded' %}success{% elif payment.payment_status == 'failed' %}danger{% elif payment.payment_status == 'pending' %}warning{% else %}secondary{% endif %}">
                            {{ payment.get_payment_status_display }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Booking Information -->
            {% if payment.booking %}
            <div class="payment-details">
                <h5>{% trans "Booking Information" %}</h5>
                <div class="row text-start">
                    <div class="col-6">
                        <strong>{% trans "Venue:" %}</strong>
                    </div>
                    <div class="col-6">
                        {{ payment.booking.venue.venue_name }}
                    </div>
                </div>
                <div class="row text-start">
                    <div class="col-6">
                        <strong>{% trans "Date:" %}</strong>
                    </div>
                    <div class="col-6">
                        {{ payment.booking.booking_date|date:"M d, Y" }}
                    </div>
                </div>
                <div class="row text-start">
                    <div class="col-6">
                        <strong>{% trans "Time:" %}</strong>
                    </div>
                    <div class="col-6">
                        {{ payment.booking.booking_time|time:"g:i A" }}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="mt-4">
                {% if payment.payment_status == 'succeeded' %}
                    <a href="{% url 'payments_app:payment_success' payment.payment_id %}" 
                       class="btn btn-success btn-lg me-2">
                        <i class="fas fa-check me-2"></i>{% trans "View Confirmation" %}
                    </a>
                    <a href="{% url 'payments_app:payment_history' %}" 
                       class="btn btn-outline-primary">
                        {% trans "Payment History" %}
                    </a>
                {% elif payment.payment_status == 'failed' %}
                    <a href="{% url 'payments_app:checkout' payment.booking.booking_id %}" 
                       class="btn btn-primary btn-lg me-2">
                        <i class="fas fa-redo me-2"></i>{% trans "Try Again" %}
                    </a>
                    <a href="{% url 'venues_app:home' %}" 
                       class="btn btn-outline-secondary">
                        {% trans "Back to Home" %}
                    </a>
                {% elif payment.payment_status == 'pending' %}
                    <button class="btn btn-secondary" disabled>
                        <i class="fas fa-spinner fa-spin me-2"></i>{% trans "Processing..." %}
                    </button>
                {% else %}
                    <a href="{% url 'payments_app:payment_history' %}" 
                       class="btn btn-primary">
                        {% trans "View Payment History" %}
                    </a>
                {% endif %}
            </div>

            <!-- Help Text -->
            <div class="mt-4">
                <small class="text-muted">
                    {% if payment.payment_status == 'succeeded' %}
                        {% trans "A confirmation email has been sent to your registered email address." %}
                    {% elif payment.payment_status == 'failed' %}
                        {% trans "If you continue to experience issues, please contact our support team." %}
                    {% elif payment.payment_status == 'pending' %}
                        {% trans "Please do not close this page while your payment is being processed." %}
                    {% endif %}
                </small>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="text-center mt-4">
            <small class="text-muted">
                <i class="fas fa-lock me-1"></i>
                {% trans "Your payment information is secure and encrypted" %}
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block payments_extra_js %}
<script>
    // Auto-redirect on successful payment after 5 seconds
    {% if payment.payment_status == 'succeeded' %}
    setTimeout(function() {
        window.location.href = "{% url 'payments_app:payment_success' payment.payment_id %}";
    }, 5000);
    {% endif %}

    // Auto-refresh for pending payments
    {% if payment.payment_status == 'pending' %}
    let refreshCount = 0;
    const maxRefreshes = 20; // Maximum 1 minute of refreshing
    
    function checkPaymentStatus() {
        refreshCount++;
        if (refreshCount < maxRefreshes) {
            setTimeout(function() {
                location.reload();
            }, 3000);
        } else {
            // Stop auto-refresh and show manual refresh option
            document.querySelector('.spinner').style.display = 'none';
            document.querySelector('h2').textContent = 'Payment is taking longer than expected';
            document.querySelector('p').innerHTML = 'Please <a href="#" onclick="location.reload()">refresh the page</a> to check the status or contact support if the issue persists.';
        }
    }
    
    checkPaymentStatus();
    {% endif %}
</script>
{% endblock %}
