"""
Management command to seed payments_app with realistic test data.
Creates payments and refund requests linked to bookings.
"""

import random
import uuid
from decimal import Decimal
from datetime import timedelta

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model

from payments_app.models import Payment, RefundRequest
from booking_cart_app.models import Booking

User = get_user_model()


class Command(BaseCommand):
    """Seed payments_app with realistic test data."""
    
    help = 'Seed payments_app with payments and refund requests'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing payment data before seeding',
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS('🌱 Starting payments_app data seeding...')
        )
        
        if options['clear']:
            self.clear_existing_data()

        with transaction.atomic():
            self.create_payments()
            self.create_refund_requests()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Payments app data seeding completed successfully!')
        )

    def clear_existing_data(self):
        """Clear existing payment data."""
        self.stdout.write('🧹 Clearing existing payment data...')

        try:
            RefundRequest.objects.all().delete()
            Payment.objects.all().delete()
            self.stdout.write('   ✅ Existing payment data cleared')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'   ⚠️ Warning during data clearing: {str(e)}')
            )

    def create_payments(self):
        """Create payments for bookings."""
        self.stdout.write('💳 Creating payments...')

        # Get bookings that should have payments (confirmed, completed, some cancelled)
        bookings = list(Booking.objects.filter(
            status__in=[Booking.CONFIRMED, Booking.COMPLETED, Booking.CANCELLED]
        ))

        if not bookings:
            self.stdout.write('   ⚠️ No suitable bookings found, skipping payment creation')
            return

        payment_statuses = [
            (Payment.SUCCEEDED, 0.80),        # 80% successful
            (Payment.FAILED, 0.10),           # 10% failed
            (Payment.PROCESSING, 0.05),       # 5% processing
            (Payment.REFUNDED, 0.03),         # 3% refunded
            (Payment.PARTIALLY_REFUNDED, 0.02), # 2% partially refunded
        ]

        created_count = 0
        skipped_count = 0

        for booking in bookings:
            # Check if booking already has a successful payment (due to unique constraint)
            existing_successful_payment = Payment.objects.filter(
                booking=booking,
                payment_status=Payment.SUCCEEDED
            ).exists()

            # Not all bookings have payments (some might be pay-on-arrival)
            if random.random() < 0.85:  # 85% of bookings have payments

                # Determine payment status
                status_rand = random.random()
                cumulative_prob = 0
                payment_status = Payment.SUCCEEDED

                for status, prob in payment_statuses:
                    cumulative_prob += prob
                    if status_rand <= cumulative_prob:
                        payment_status = status
                        break

                # If we're trying to create a successful payment but one already exists, skip or change status
                if payment_status == Payment.SUCCEEDED and existing_successful_payment:
                    # Change to a different status or skip
                    if random.random() < 0.5:
                        payment_status = Payment.FAILED
                    else:
                        skipped_count += 1
                        continue

                # Calculate payment date (usually same day or day after booking)
                payment_date = booking.booking_date + timedelta(
                    hours=random.randint(0, 24)
                )

                try:
                    # Create payment
                    payment = Payment.objects.create(
                        booking=booking,
                        customer=booking.customer,
                        provider=booking.venue.service_provider.user,
                        amount_paid=booking.total_price,
                        payment_method=random.choice([
                            Payment.CREDIT_CARD, Payment.DEBIT_CARD, Payment.STRIPE
                        ]),
                        stripe_payment_intent_id=f'pi_{uuid.uuid4().hex[:24]}',
                        stripe_charge_id=f'ch_{uuid.uuid4().hex[:24]}',
                        payment_status=payment_status,
                        payment_date=payment_date,
                    )
                    created_count += 1
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'   ⚠️ Failed to create payment for booking {booking.slug}: {str(e)}')
                    )
                    skipped_count += 1
                    continue

                    # Set completed date for successful payments
                    if payment_status == Payment.SUCCEEDED:
                        payment.completed_date = payment_date + timedelta(minutes=random.randint(1, 5))
                        payment.save()

                    # Add failure reason for failed payments
                    elif payment_status == Payment.FAILED:
                        payment.failure_reason = random.choice([
                            'Insufficient funds',
                            'Card declined',
                            'Expired card',
                            'Invalid card number',
                            'Bank authorization failed',
                            'Network timeout',
                        ])
                        payment.save()

                    # Handle refunded payments
                    elif payment_status in [Payment.REFUNDED, Payment.PARTIALLY_REFUNDED]:
                        if payment_status == Payment.REFUNDED:
                            payment.refunded_amount = payment.amount_paid
                        else:
                            payment.refunded_amount = payment.amount_paid * Decimal('0.5')
                        payment.save()

                    status_emoji = {
                        Payment.SUCCEEDED: '✅',
                        Payment.FAILED: '❌',
                        Payment.PROCESSING: '⏳',
                        Payment.REFUNDED: '💰',
                        Payment.PARTIALLY_REFUNDED: '💸',
                    }

                    self.stdout.write(
                        f'   {status_emoji.get(payment_status, "💳")} Created {payment_status} payment: '
                        f'${payment.amount_paid} for booking {booking.slug}'
                    )

        self.stdout.write(f'   📊 Payment creation summary: {created_count} created, {skipped_count} skipped')

    def create_refund_requests(self):
        """Create refund requests for some payments."""
        self.stdout.write('💰 Creating refund requests...')
        
        # Get payments that could have refund requests
        payments = list(Payment.objects.filter(
            payment_status__in=[Payment.SUCCEEDED, Payment.PARTIALLY_REFUNDED]
        ))
        
        if not payments:
            self.stdout.write('   ⚠️ No suitable payments found, skipping refund request creation')
            return
        
        # Create refund requests for 10-15% of successful payments
        num_requests = max(1, len(payments) // 8)
        selected_payments = random.sample(payments, min(num_requests, len(payments)))
        
        refund_statuses = [
            (RefundRequest.PENDING, 0.30),    # 30% pending
            (RefundRequest.APPROVED, 0.40),   # 40% approved
            (RefundRequest.DECLINED, 0.20),   # 20% declined
            (RefundRequest.PROCESSED, 0.10),  # 10% processed
        ]
        
        refund_reasons = [
            (RefundRequest.SERVICE_NOT_PROVIDED, 'Service was not provided as scheduled'),
            (RefundRequest.POOR_SERVICE_QUALITY, 'Service quality was below expectations'),
            (RefundRequest.BOOKING_CANCELLED, 'Had to cancel due to emergency'),
            (RefundRequest.TECHNICAL_ISSUE, 'Website booking error'),
            (RefundRequest.OTHER, 'Personal circumstances changed'),
        ]
        
        for payment in selected_payments:
            # Determine refund status
            status_rand = random.random()
            cumulative_prob = 0
            request_status = RefundRequest.PENDING
            
            for status, prob in refund_statuses:
                cumulative_prob += prob
                if status_rand <= cumulative_prob:
                    request_status = status
                    break
            
            # Select reason
            reason_category, reason_description = random.choice(refund_reasons)
            
            # Calculate requested amount (usually full amount or partial)
            if random.random() < 0.7:  # 70% request full refund
                requested_amount = payment.remaining_refundable_amount
            else:  # 30% request partial refund
                requested_amount = payment.remaining_refundable_amount * Decimal('0.5')
            
            # Create refund request
            refund_request = RefundRequest.objects.create(
                payment=payment,
                customer=payment.customer,
                reason_category=reason_category,
                reason_description=reason_description,
                requested_amount=requested_amount,
                request_status=request_status,
            )
            
            # Handle reviewed/processed requests
            if request_status in [RefundRequest.APPROVED, RefundRequest.DECLINED, RefundRequest.PROCESSED]:
                admin_user = User.objects.filter(is_superuser=True).first()
                refund_request.reviewed_by = admin_user
                refund_request.reviewed_at = timezone.now() - timedelta(days=random.randint(1, 7))
                
                if request_status == RefundRequest.DECLINED:
                    refund_request.admin_notes = random.choice([
                        'Service was provided as scheduled',
                        'No evidence of service quality issues',
                        'Cancellation was outside policy window',
                        'Request does not meet refund criteria',
                    ])
                elif request_status == RefundRequest.APPROVED:
                    refund_request.admin_notes = 'Refund approved after review'
                elif request_status == RefundRequest.PROCESSED:
                    refund_request.admin_notes = 'Refund processed successfully'
                    refund_request.processed_amount = requested_amount
                    
                    # Update payment refund amount
                    payment.refunded_amount += requested_amount
                    if payment.refunded_amount >= payment.amount_paid:
                        payment.payment_status = Payment.REFUNDED
                    else:
                        payment.payment_status = Payment.PARTIALLY_REFUNDED
                    payment.save()
                
                refund_request.save()
            
            status_emoji = {
                RefundRequest.PENDING: '⏳',
                RefundRequest.APPROVED: '✅',
                RefundRequest.DECLINED: '❌',
                RefundRequest.PROCESSED: '💰',
            }
            
            self.stdout.write(
                f'   {status_emoji.get(request_status, "💰")} Created {request_status} refund request: '
                f'${requested_amount} for payment {payment.payment_id}'
            )
