"""
Django signals for payments_app.

This module contains signal handlers for payment and refund request events,
including notification sending and booking status updates.
"""

# Standard library imports
import logging

# Django imports
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.apps import apps

# Local imports
from .models import Payment, RefundRequest

# Set up logging
logger = logging.getLogger(__name__)


def send_notification(user, title, message, notification_type='info', **kwargs):
    """
    Send notification to user if notifications app is available.
    
    Args:
        user: User to send notification to
        title: Notification title
        message: Notification message
        notification_type: Type of notification ('info', 'success', 'warning', 'error')
        **kwargs: Additional parameters for notification
    """
    try:
        # Check if notifications app is installed
        if apps.is_installed('notifications_app'):
            from notifications_app.utils import send_notification as send_notif
            send_notif(
                user=user,
                title=title,
                message=message,
                notification_type=notification_type,
                **kwargs
            )
            logger.info(f"Payment success notification sent for payment {kwargs.get('payment_id', 'unknown')}")
    except Exception as e:
        logger.error(f"Failed to send notification: {e}")


@receiver(post_save, sender=Payment)
def payment_status_changed(sender, instance, created, **kwargs):
    """
    Handle payment status changes.
    
    When payment status changes to succeeded:
    - Update booking status to confirmed
    - Send notifications to customer and provider
    """
    if not created:  # Only handle updates, not creation
        try:
            # Update booking status when payment succeeds
            if instance.payment_status == Payment.SUCCEEDED:
                booking = instance.booking
                if booking.status != 'confirmed':
                    booking.status = 'confirmed'
                    booking.save(update_fields=['status'])
                    logger.info(f"Booking {booking.id} status updated to confirmed after payment success")
                
                # Send success notifications
                send_notification(
                    user=instance.customer,
                    title='Payment Successful',
                    message=f'Your payment of ${instance.amount_paid} for booking at {instance.booking.venue.venue_name} has been processed successfully.',
                    notification_type='success',
                    payment_id=str(instance.payment_id)
                )
                
                send_notification(
                    user=instance.provider,
                    title='Payment Received',
                    message=f'You have received a payment of ${instance.amount_paid} for booking at {instance.booking.venue.venue_name}.',
                    notification_type='success',
                    payment_id=str(instance.payment_id)
                )
                
        except Exception as e:
            logger.error(f"Error in payment status change signal: {e}")


@receiver(post_save, sender=RefundRequest)
def refund_request_created(sender, instance, created, **kwargs):
    """
    Handle refund request creation and status changes.
    
    When refund request is created:
    - Send notification to provider
    
    When refund request status changes:
    - Send appropriate notifications
    """
    try:
        if created:
            # Notify provider about new refund request
            send_notification(
                user=instance.payment.provider,
                title='Refund Request Received',
                message=f'A customer has requested a refund of ${instance.requested_amount} for booking at {instance.payment.booking.venue.venue_name}.',
                notification_type='info',
                refund_request_id=str(instance.refund_request_id)
            )
            logger.info(f"Refund request notification sent for request {instance.refund_request_id}")
            
        else:
            # Handle status changes
            if instance.request_status == RefundRequest.APPROVED:
                send_notification(
                    user=instance.customer,
                    title='Refund Approved',
                    message=f'Your refund request of ${instance.requested_amount} has been approved and will be processed shortly.',
                    notification_type='success',
                    refund_request_id=str(instance.refund_request_id)
                )
                logger.info(f"Refund approval notification sent for request {instance.refund_request_id}")
                
            elif instance.request_status == RefundRequest.DECLINED:
                send_notification(
                    user=instance.customer,
                    title='Refund Declined',
                    message=f'Your refund request of ${instance.requested_amount} has been declined. {instance.admin_notes}',
                    notification_type='warning',
                    refund_request_id=str(instance.refund_request_id)
                )
                logger.info(f"Refund decline notification sent for request {instance.refund_request_id}")
                
    except Exception as e:
        logger.error(f"Error in refund request signal: {e}")
