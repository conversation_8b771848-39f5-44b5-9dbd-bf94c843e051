"""URL configuration for the payments_app Django application."""

# --- Third-Party Imports ---
from django.urls import path

# --- Local App Imports ---
from . import views


app_name = 'payments_app'

urlpatterns = [
    # Customer Payment URLs
    
    # Stripe Checkout
    path(
        'checkout/<uuid:booking_id>/',
        views.StripeCheckoutView.as_view(),
        name='checkout'
    ),
    
    # Payment Processing (placeholder for Stripe integration)
    path(
        'process/<uuid:payment_id>/',
        views.PaymentProcessView.as_view(),
        name='payment_process'
    ),

    # Payment Success
    path(
        'success/<uuid:payment_id>/',
        views.PaymentSuccessView.as_view(),
        name='payment_success'
    ),

    # Payment Cancel
    path(
        'cancel/<uuid:booking_id>/',
        views.PaymentCancelView.as_view(),
        name='payment_cancel'
    ),
    
    # Payment History and Details
    path(
        'history/',
        views.PaymentHistoryView.as_view(),
        name='payment_history'
    ),
    
    path(
        'detail/<uuid:payment_id>/',
        views.PaymentDetailView.as_view(),
        name='payment_detail'
    ),
    
    # Payment Receipt
    path(
        'receipt/<uuid:payment_id>/',
        views.payment_receipt_view,
        name='payment_receipt'
    ),

    # Stripe Webhook
    path(
        'webhook/stripe/',
        views.stripe_webhook_view,
        name='stripe_webhook'
    ),
    
    # Refund Request URLs
    
    # Request Refund
    path(
        'refund/request/<uuid:payment_id>/',
        views.RefundRequestView.as_view(),
        name='refund_request'
    ),
    
    # Refund Confirmation
    path(
        'refund/confirmation/<uuid:refund_request_id>/',
        views.RefundConfirmationView.as_view(),
        name='refund_confirmation'
    ),
    
    # Refund History
    path(
        'refund/history/',
        views.RefundHistoryView.as_view(),
        name='refund_history'
    ),

    # Refund Detail
    path(
        'refund/detail/<uuid:refund_id>/',
        views.RefundDetailView.as_view(),
        name='refund_detail'
    ),

    # ============================================================================
    # PROVIDER PAYMENT URLs
    # ============================================================================

    # Provider Earnings Overview
    path(
        'provider/earnings/',
        views.ProviderEarningsOverviewView.as_view(),
        name='provider_earnings'
    ),

    # Provider Payment History
    path(
        'provider/history/',
        views.ProviderPaymentHistoryView.as_view(),
        name='provider_payment_history'
    ),

    # Provider Payment Detail
    path(
        'provider/detail/<uuid:payment_id>/',
        views.ProviderPaymentDetailView.as_view(),
        name='provider_payment_detail'
    ),

    # Provider Payout History (Stripe payouts)
    path(
        'provider/payouts/',
        views.ProviderPayoutHistoryView.as_view(),
        name='provider_payout_history'
    ),

    # ============================================================================
    # ADMIN URLs
    # ============================================================================

    # Admin Payment List
    path(
        'admin/payments/',
        views.AdminPaymentListView.as_view(),
        name='admin_payment_list'
    ),

    # Admin Payment Detail
    path(
        'admin/payments/<uuid:payment_id>/',
        views.AdminPaymentDetailView.as_view(),
        name='admin_payment_detail'
    ),

    # Admin Refund Management
    path(
        'admin/refunds/',
        views.AdminRefundManagementView.as_view(),
        name='admin_refund_list'
    ),

    # Admin Refund Detail
    path(
        'admin/refunds/<uuid:refund_id>/',
        views.AdminRefundDetailView.as_view(),
        name='admin_refund_detail'
    ),

    # Admin Refund Actions
    path(
        'admin/refunds/<uuid:refund_id>/approve/',
        views.admin_approve_refund,
        name='admin_refund_approve'
    ),

    path(
        'admin/refunds/<uuid:refund_id>/decline/',
        views.admin_decline_refund,
        name='admin_refund_decline'
    ),

    # Admin Disputed Payments
    path(
        'admin/disputed-payments/',
        views.AdminDisputedPaymentsView.as_view(),
        name='admin_disputed_payments'
    ),

    # Admin Payment Analytics
    path(
        'admin/analytics/',
        views.AdminPaymentAnalyticsView.as_view(),
        name='admin_payment_analytics'
    ),
]
