"""Admin configuration for the payments_app Django application."""

# --- Third-Party Imports ---
from django.contrib import admin
from django.db import transaction
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .models import Payment, RefundRequest


# --- Payment Admin ---


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """Admin configuration for Payment model."""
    
    list_display = [
        'payment_id_short',
        'customer_email',
        'provider_email',
        'booking_link',
        'amount_paid',
        'payment_method',
        'payment_status',
        'payment_date',
        'refunded_amount',
    ]
    
    list_filter = [
        'payment_status',
        'payment_method',
        'payment_date',
        'created_at',
    ]
    
    search_fields = [
        'payment_id',
        'customer__email',
        'provider__email',
        'booking__booking_id',
        'stripe_payment_intent_id',
        'stripe_charge_id',
    ]
    
    readonly_fields = [
        'payment_id',
        'payment_date',
        'completed_date',
        'created_at',
        'updated_at',
    ]
    
    fieldsets = (
        (_('Payment Information'), {
            'fields': (
                'payment_id',
                'booking',
                'customer',
                'provider',
                'amount_paid',
                'payment_method',
                'payment_status',
            )
        }),
        (_('Stripe Information'), {
            'fields': (
                'stripe_payment_intent_id',
                'stripe_charge_id',
            ),
            'classes': ('collapse',),
        }),
        (_('Refund Information'), {
            'fields': (
                'refunded_amount',
            )
        }),
        (_('Status Information'), {
            'fields': (
                'payment_date',
                'completed_date',
                'failure_reason',
            )
        }),
        (_('Metadata'), {
            'fields': (
                'created_at',
                'updated_at',
            ),
            'classes': ('collapse',),
        }),
    )
    
    ordering = ['-payment_date']
    date_hierarchy = 'payment_date'
    
    def payment_id_short(self, obj):
        """Display shortened payment ID."""
        return str(obj.payment_id)[:8] + '...'
    payment_id_short.short_description = _('Payment ID')
    
    def customer_email(self, obj):
        """Display customer email with link to customer admin."""
        if obj.customer:
            url = reverse('admin:accounts_app_customuser_change', args=[obj.customer.pk])
            return format_html('<a href="{}">{}</a>', url, obj.customer.email)
        return '-'
    customer_email.short_description = _('Customer')
    
    def provider_email(self, obj):
        """Display provider email with link to provider admin."""
        if obj.provider:
            url = reverse('admin:accounts_app_customuser_change', args=[obj.provider.pk])
            return format_html('<a href="{}">{}</a>', url, obj.provider.email)
        return '-'
    provider_email.short_description = _('Provider')
    
    def booking_link(self, obj):
        """Display booking ID with link to booking admin."""
        if obj.booking:
            url = reverse('admin:booking_cart_app_booking_change', args=[obj.booking.pk])
            booking_id_short = str(obj.booking.booking_id)[:8] + '...'
            return format_html('<a href="{}">{}</a>', url, booking_id_short)
        return '-'
    booking_link.short_description = _('Booking')
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related(
            'customer', 'provider', 'booking'
        )


# --- Refund Request Admin ---


@admin.register(RefundRequest)
class RefundRequestAdmin(admin.ModelAdmin):
    """Admin configuration for RefundRequest model."""
    
    list_display = [
        'refund_request_id_short',
        'customer_email',
        'payment_link',
        'reason_category',
        'requested_amount',
        'processed_amount',
        'request_status',
        'created_at',
        'reviewed_by_email',
    ]
    
    list_filter = [
        'request_status',
        'reason_category',
        'created_at',
        'reviewed_at',
    ]
    
    search_fields = [
        'refund_request_id',
        'customer__email',
        'payment__payment_id',
        'reason_description',
    ]
    
    readonly_fields = [
        'refund_request_id',
        'created_at',
        'updated_at',
    ]
    
    fieldsets = (
        (_('Refund Request Information'), {
            'fields': (
                'refund_request_id',
                'payment',
                'customer',
                'reason_category',
                'reason_description',
                'requested_amount',
                'request_status',
            )
        }),
        (_('Admin Review'), {
            'fields': (
                'admin_notes',
                'reviewed_by',
                'reviewed_at',
                'processed_amount',
            )
        }),
        (_('Metadata'), {
            'fields': (
                'created_at',
                'updated_at',
            ),
            'classes': ('collapse',),
        }),
    )
    
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    actions = ['approve_refund_requests', 'decline_refund_requests']
    
    def refund_request_id_short(self, obj):
        """Display shortened refund request ID."""
        return str(obj.refund_request_id)[:8] + '...'
    refund_request_id_short.short_description = _('Refund Request ID')
    
    def customer_email(self, obj):
        """Display customer email with link to customer admin."""
        if obj.customer:
            url = reverse('admin:accounts_app_customuser_change', args=[obj.customer.pk])
            return format_html('<a href="{}">{}</a>', url, obj.customer.email)
        return '-'
    customer_email.short_description = _('Customer')
    
    def payment_link(self, obj):
        """Display payment ID with link to payment admin."""
        if obj.payment:
            url = reverse('admin:payments_app_payment_change', args=[obj.payment.pk])
            payment_id_short = str(obj.payment.payment_id)[:8] + '...'
            return format_html('<a href="{}">{}</a>', url, payment_id_short)
        return '-'
    payment_link.short_description = _('Payment')
    
    def reviewed_by_email(self, obj):
        """Display reviewer email with link to user admin."""
        if obj.reviewed_by:
            url = reverse('admin:accounts_app_customuser_change', args=[obj.reviewed_by.pk])
            return format_html('<a href="{}">{}</a>', url, obj.reviewed_by.email)
        return '-'
    reviewed_by_email.short_description = _('Reviewed By')
    
    def approve_refund_requests(self, request, queryset):
        """Admin action to approve selected refund requests."""
        pending_requests = queryset.filter(request_status=RefundRequest.PENDING)
        count = 0

        with transaction.atomic():
            for refund_request in pending_requests:
                try:
                    refund_request.approve(request.user, 'Approved via admin action')
                    count += 1
                except Exception:
                    pass  # Skip invalid requests
        
        self.message_user(
            request,
            _(f'{count} refund request(s) approved successfully.')
        )
    approve_refund_requests.short_description = _('Approve selected refund requests')
    
    def decline_refund_requests(self, request, queryset):
        """Admin action to decline selected refund requests."""
        pending_requests = queryset.filter(request_status=RefundRequest.PENDING)
        count = 0

        with transaction.atomic():
            for refund_request in pending_requests:
                try:
                    refund_request.decline(request.user, 'Declined via admin action')
                    count += 1
                except Exception:
                    pass  # Skip invalid requests
        
        self.message_user(
            request,
            _(f'{count} refund request(s) declined successfully.')
        )
    decline_refund_requests.short_description = _('Decline selected refund requests')
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related(
            'customer', 'payment', 'reviewed_by'
        )
