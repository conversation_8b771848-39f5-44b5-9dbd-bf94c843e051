"""
Unit tests for payments_app utility functions.

This module contains comprehensive unit tests for all utility functions in the payments_app,
including payment processing, validation, and helper functions following the same patterns as accounts_app.
"""

# Standard library imports
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch, Mock
from io import BytesIO

# Django imports
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError

# Local imports
from payments_app.models import Payment, RefundRequest
from booking_cart_app.models import Booking, Venue
from venues_app.models import Category
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


class PaymentUtilsBaseTest(TestCase):
    """Base test class for payment utility tests."""
    
    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )
        
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='service_provider'
        )
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Test Business',
            business_phone_number='+**********',
            contact_person_name='Test Contact',
            business_address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        # Create category and venue
        self.category = Category.objects.create(
            name='Test Category',
            description='Test category description'
        )
        
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Venue',
            short_description='Test venue description',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test St',
            operating_hours='9AM-6PM',
            tags='spa, massage, wellness',
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        
        # Create booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('100.00'),
            status='pending'
        )


class PaymentUtilsTest(PaymentUtilsBaseTest):
    """Test payment utility functions."""
    
    def test_payment_model_creation(self):
        """Test basic payment model creation for utility testing."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_method=Payment.STRIPE,
            payment_status=Payment.SUCCEEDED
        )
        
        self.assertEqual(payment.booking, self.booking)
        self.assertEqual(payment.customer, self.customer)
        self.assertEqual(payment.provider, self.provider)
        self.assertEqual(payment.amount_paid, Decimal('100.00'))
    
    def test_refund_request_creation(self):
        """Test basic refund request creation for utility testing."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED
        )
        
        refund_request = RefundRequest.objects.create(
            payment=payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description='Service was not provided as expected',
            requested_amount=Decimal('50.00')
        )
        
        self.assertEqual(refund_request.payment, payment)
        self.assertEqual(refund_request.customer, self.customer)
        self.assertEqual(refund_request.requested_amount, Decimal('50.00'))
    
    def test_payment_status_transitions(self):
        """Test payment status transitions."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.PENDING
        )
        
        # Test transition to succeeded
        payment.payment_status = Payment.SUCCEEDED
        payment.save()
        self.assertEqual(payment.payment_status, Payment.SUCCEEDED)
        
        # Test transition to failed
        payment.payment_status = Payment.FAILED
        payment.save()
        self.assertEqual(payment.payment_status, Payment.FAILED)
    
    def test_refund_amount_calculations(self):
        """Test refund amount calculations."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED,
            refunded_amount=Decimal('30.00')
        )
        
        # Test remaining refundable amount
        self.assertEqual(payment.remaining_refundable_amount, Decimal('70.00'))
        
        # Test is_fully_refunded
        self.assertFalse(payment.is_fully_refunded)
        
        # Test full refund
        payment.refunded_amount = Decimal('100.00')
        payment.save()
        self.assertTrue(payment.is_fully_refunded)
        self.assertEqual(payment.remaining_refundable_amount, Decimal('0.00'))
    
    def test_payment_refundability(self):
        """Test payment refundability logic."""
        # Test succeeded payment is refundable
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED
        )
        self.assertTrue(payment.is_refundable)
        
        # Test failed payment is not refundable
        payment.payment_status = Payment.FAILED
        payment.save()
        self.assertFalse(payment.is_refundable)
        
        # Test fully refunded payment is not refundable
        payment.payment_status = Payment.SUCCEEDED
        payment.refunded_amount = Decimal('100.00')
        payment.save()
        self.assertFalse(payment.is_refundable)
    
    def test_payment_process_refund(self):
        """Test payment process_refund method."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED
        )
        
        # Test partial refund
        payment.process_refund(Decimal('30.00'), 'Partial refund test')
        self.assertEqual(payment.refunded_amount, Decimal('30.00'))
        self.assertEqual(payment.payment_status, Payment.PARTIALLY_REFUNDED)
        
        # Test completing the refund
        payment.process_refund(Decimal('70.00'), 'Complete refund test')
        self.assertEqual(payment.refunded_amount, Decimal('100.00'))
        self.assertEqual(payment.payment_status, Payment.REFUNDED)
    
    def test_refund_request_approval_workflow(self):
        """Test refund request approval workflow."""
        admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='admin',
            is_staff=True
        )
        
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED
        )
        
        refund_request = RefundRequest.objects.create(
            payment=payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description='Service was not provided as expected',
            requested_amount=Decimal('50.00')
        )
        
        # Test approval
        refund_request.approve(admin_user, 'Approved for testing')
        self.assertEqual(refund_request.request_status, RefundRequest.APPROVED)
        self.assertEqual(refund_request.reviewed_by, admin_user)
        self.assertIsNotNone(refund_request.reviewed_at)
        
        # Test processing
        refund_request.process_refund(Decimal('50.00'))
        self.assertEqual(refund_request.request_status, RefundRequest.PROCESSED)
        self.assertEqual(refund_request.processed_amount, Decimal('50.00'))
        
        # Verify payment was updated
        payment.refresh_from_db()
        self.assertEqual(payment.refunded_amount, Decimal('50.00'))
        self.assertEqual(payment.payment_status, Payment.PARTIALLY_REFUNDED)


class PaymentUtilsEdgeCasesTest(PaymentUtilsBaseTest):
    """Test edge cases and error conditions for payment utilities."""
    
    def test_invalid_refund_amount(self):
        """Test validation of invalid refund amounts."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED
        )
        
        # Test refund amount exceeding payment amount
        with self.assertRaises(ValidationError):
            payment.process_refund(Decimal('150.00'))
        
        # Test negative refund amount
        with self.assertRaises(ValidationError):
            payment.process_refund(Decimal('-10.00'))
    
    def test_refund_non_refundable_payment(self):
        """Test attempting to refund non-refundable payment."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.FAILED
        )
        
        with self.assertRaises(ValidationError):
            payment.process_refund(Decimal('50.00'))
    
    def test_duplicate_refund_request_validation(self):
        """Test validation for duplicate refund requests."""
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED
        )
        
        # Create first refund request
        RefundRequest.objects.create(
            payment=payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description='Service was not provided',
            requested_amount=Decimal('50.00')
        )
        
        # Test that we can create another refund request for the same payment
        # (This should be allowed as customers might request multiple partial refunds)
        refund_request2 = RefundRequest.objects.create(
            payment=payment,
            customer=self.customer,
            reason_category=RefundRequest.POOR_SERVICE_QUALITY,
            reason_description='Poor service quality',
            requested_amount=Decimal('30.00')
        )
        
        self.assertIsNotNone(refund_request2)
        self.assertEqual(RefundRequest.objects.filter(payment=payment).count(), 2)
