"""
Unit tests for payments_app Django signals.

This module contains comprehensive unit tests for all Django signals in the payments_app,
including payment status updates and notification triggers following the same patterns as accounts_app.
"""

# Standard library imports
from decimal import Decimal
from unittest.mock import patch, Mock

# Django imports
from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.db.models.signals import post_save
from django.utils import timezone

# Local imports
from payments_app.models import Payment, RefundRequest
from booking_cart_app.models import Booking, Venue
from venues_app.models import Category
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


class PaymentSignalsBaseTest(TestCase):
    """Base test class for payment signals tests."""
    
    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )
        
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='service_provider'
        )
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Test Business',
            business_phone_number='+**********',
            contact_person_name='Test Contact',
            business_address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        # Create category and venue
        self.category = Category.objects.create(
            name='Test Category',
            description='Test category description'
        )
        
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Venue',
            short_description='Test venue description',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test St',
            operating_hours='9AM-6PM',
            tags='spa, massage, wellness',
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        
        # Create booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('100.00'),
            status='pending'
        )


class PaymentStatusSignalTest(PaymentSignalsBaseTest):
    """Test payment status change signals."""
    
    @patch('payments_app.signals.send_notification')
    def test_payment_succeeded_signal(self, mock_send_notification):
        """Test signal when payment status changes to succeeded."""
        # Create payment with pending status
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.PENDING
        )
        
        # Change status to succeeded
        payment.payment_status = Payment.SUCCEEDED
        payment.save()
        
        # Verify booking status was updated
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, 'confirmed')
        
        # Verify notifications were sent (if notifications app is enabled)
        if mock_send_notification.called:
            # Check customer notification
            customer_call = mock_send_notification.call_args_list[0]
            self.assertEqual(customer_call[1]['user'], self.customer)
            self.assertIn('Payment Successful', customer_call[1]['title'])
            
            # Check provider notification
            provider_call = mock_send_notification.call_args_list[1]
            self.assertEqual(provider_call[1]['user'], self.provider)
            self.assertIn('Payment Received', provider_call[1]['title'])
    
    def test_payment_failed_signal(self):
        """Test signal when payment status changes to failed."""
        # Create payment with pending status
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.PENDING
        )
        
        # Change status to failed
        payment.payment_status = Payment.FAILED
        payment.save()
        
        # Verify booking status remains unchanged
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, 'pending')
    
    def test_payment_refunded_signal(self):
        """Test signal when payment status changes to refunded."""
        # Create payment with succeeded status
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED
        )
        
        # Change status to refunded
        payment.payment_status = Payment.REFUNDED
        payment.refunded_amount = Decimal('100.00')
        payment.save()
        
        # Verify payment is fully refunded
        self.assertTrue(payment.is_fully_refunded)
        self.assertEqual(payment.refunded_amount, Decimal('100.00'))
    
    def test_payment_partially_refunded_signal(self):
        """Test signal when payment status changes to partially refunded."""
        # Create payment with succeeded status
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED
        )
        
        # Change status to partially refunded
        payment.payment_status = Payment.PARTIALLY_REFUNDED
        payment.refunded_amount = Decimal('50.00')
        payment.save()
        
        # Verify partial refund
        self.assertFalse(payment.is_fully_refunded)
        self.assertEqual(payment.remaining_refundable_amount, Decimal('50.00'))


class RefundRequestSignalTest(PaymentSignalsBaseTest):
    """Test refund request signals."""
    
    def setUp(self):
        """Set up additional test data for refund tests."""
        super().setUp()
        
        # Create payment
        self.payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED
        )
    
    @patch('payments_app.signals.send_notification')
    def test_refund_request_created_signal(self, mock_send_notification):
        """Test signal when refund request is created."""
        # Create refund request
        refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description='Service was not provided as expected',
            requested_amount=Decimal('50.00')
        )
        
        # Verify refund request was created
        self.assertEqual(refund_request.request_status, RefundRequest.PENDING)
        
        # Verify notifications were sent (if notifications app is enabled)
        if mock_send_notification.called:
            # Check that provider was notified
            call_args = mock_send_notification.call_args_list[0]
            self.assertEqual(call_args[1]['user'], self.provider)
            self.assertIn('Refund Request', call_args[1]['title'])
    
    @patch('payments_app.signals.send_notification')
    def test_refund_request_approved_signal(self, mock_send_notification):
        """Test signal when refund request is approved."""
        admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='admin',
            is_staff=True
        )
        
        # Create refund request
        refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description='Service was not provided as expected',
            requested_amount=Decimal('50.00')
        )
        
        # Approve refund request
        refund_request.approve(admin_user, 'Approved for testing')
        
        # Verify refund request was approved
        self.assertEqual(refund_request.request_status, RefundRequest.APPROVED)
        self.assertEqual(refund_request.reviewed_by, admin_user)
        
        # Verify notifications were sent (if notifications app is enabled)
        if mock_send_notification.called:
            # Check that customer was notified
            customer_notification = None
            for call in mock_send_notification.call_args_list:
                if call[1]['user'] == self.customer:
                    customer_notification = call
                    break
            
            if customer_notification:
                self.assertIn('Refund Approved', customer_notification[1]['title'])
    
    @patch('payments_app.signals.send_notification')
    def test_refund_request_declined_signal(self, mock_send_notification):
        """Test signal when refund request is declined."""
        admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='admin',
            is_staff=True
        )
        
        # Create refund request
        refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description='Service was not provided as expected',
            requested_amount=Decimal('50.00')
        )
        
        # Decline refund request
        refund_request.decline(admin_user, 'Declined for testing')
        
        # Verify refund request was declined
        self.assertEqual(refund_request.request_status, RefundRequest.DECLINED)
        self.assertEqual(refund_request.reviewed_by, admin_user)
        
        # Verify notifications were sent (if notifications app is enabled)
        if mock_send_notification.called:
            # Check that customer was notified
            customer_notification = None
            for call in mock_send_notification.call_args_list:
                if call[1]['user'] == self.customer:
                    customer_notification = call
                    break
            
            if customer_notification:
                self.assertIn('Refund Declined', customer_notification[1]['title'])


class SignalIntegrationTest(PaymentSignalsBaseTest):
    """Test signal integration and edge cases."""
    
    def test_signal_disconnection_and_reconnection(self):
        """Test that signals can be disconnected and reconnected."""
        # Create payment and change status
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.PENDING
        )

        payment.payment_status = Payment.SUCCEEDED
        payment.save()

        # Verify payment status was updated
        self.assertEqual(payment.payment_status, Payment.SUCCEEDED)
    
    @override_settings(INSTALLED_APPS=[
        app for app in __import__('django.conf', fromlist=['settings']).settings.INSTALLED_APPS 
        if app != 'notifications_app'
    ])
    def test_signals_without_notifications_app(self):
        """Test that signals work correctly when notifications app is not installed."""
        # Create payment and change status
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.PENDING
        )
        
        # Change status to succeeded
        payment.payment_status = Payment.SUCCEEDED
        payment.save()
        
        # Verify booking status was still updated
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, 'confirmed')
    
    def test_multiple_status_changes(self):
        """Test multiple rapid status changes."""
        # Create payment
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.PENDING
        )
        
        # Multiple status changes
        payment.payment_status = Payment.PROCESSING
        payment.save()
        
        payment.payment_status = Payment.SUCCEEDED
        payment.save()
        
        payment.payment_status = Payment.PARTIALLY_REFUNDED
        payment.refunded_amount = Decimal('30.00')
        payment.save()
        
        # Verify final state
        self.assertEqual(payment.payment_status, Payment.PARTIALLY_REFUNDED)
        self.assertEqual(payment.refunded_amount, Decimal('30.00'))
        
        # Verify booking status was updated
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, 'confirmed')
    
    def test_signal_with_bulk_operations(self):
        """Test that signals work correctly with bulk operations."""
        # Create multiple payments
        payments = []
        for i in range(3):
            booking = Booking.objects.create(
                customer=self.customer,
                venue=self.venue,
                total_price=Decimal('100.00'),
                status='pending'
            )
            
            payment = Payment.objects.create(
                booking=booking,
                customer=self.customer,
                provider=self.provider,
                amount_paid=Decimal('100.00'),
                payment_status=Payment.PENDING
            )
            payments.append(payment)
        
        # Bulk update status
        Payment.objects.filter(id__in=[p.id for p in payments]).update(
            payment_status=Payment.SUCCEEDED
        )
        
        # Note: Bulk updates don't trigger post_save signals
        # This is expected Django behavior
        for payment in payments:
            payment.refresh_from_db()
            self.assertEqual(payment.payment_status, Payment.SUCCEEDED)
            
            # Booking status won't be updated due to bulk update
            payment.booking.refresh_from_db()
            self.assertEqual(payment.booking.status, 'pending')
    
    def test_signal_error_handling(self):
        """Test signal error handling."""
        # Create payment
        payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.PENDING
        )
        
        # Mock an error in the signal handler
        with patch('payments_app.signals.send_notification', side_effect=Exception('Notification error')):
            # Change status - should not raise exception even if notification fails
            payment.payment_status = Payment.SUCCEEDED
            payment.save()
            
            # Verify payment status was still updated
            self.assertEqual(payment.payment_status, Payment.SUCCEEDED)
            
            # Verify booking status was still updated
            self.booking.refresh_from_db()
            self.assertEqual(self.booking.status, 'confirmed')
