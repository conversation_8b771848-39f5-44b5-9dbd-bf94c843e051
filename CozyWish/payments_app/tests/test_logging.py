"""
Unit tests for payments_app logging functionality.

This module contains comprehensive unit tests for all logging functions in the payments_app,
including payment events, refund events, and security logging following the same patterns as accounts_app.
"""

# Standard library imports
from decimal import Decimal
from unittest.mock import patch, Mock, call
import logging

# Django imports
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.utils import timezone

# Local imports
from payments_app.logging_utils import (
    log_payment_initiated, log_payment_completed, log_payment_failed,
    log_refund_requested, log_refund_approved, log_refund_declined,
    log_payment_error, performance_monitor,
    log_provider_earnings_viewed, log_provider_payment_history_viewed,
    log_provider_payment_detail_viewed, log_provider_payout_history_viewed,
    log_customer_payment_history_viewed, log_customer_refund_history_viewed,
    log_admin_payment_analytics_viewed
)
from payments_app.models import Payment, RefundRequest
from booking_cart_app.models import Booking, Venue
from venues_app.models import Category
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


class PaymentLoggingBaseTest(TestCase):
    """Base test class for payment logging tests."""
    
    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        
        # Create users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )
        
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='service_provider'
        )
        
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='admin',
            is_staff=True
        )
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Test Business',
            business_phone_number='+**********',
            contact_person_name='Test Contact',
            business_address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        # Create category and venue
        self.category = Category.objects.create(
            name='Test Category',
            description='Test category description'
        )
        
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Venue',
            short_description='Test venue description',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test St',
            operating_hours='9AM-6PM',
            tags='spa, massage, wellness',
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        
        # Create booking
        self.booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('100.00'),
            status='pending'
        )
        
        # Create payment
        self.payment = Payment.objects.create(
            booking=self.booking,
            customer=self.customer,
            provider=self.provider,
            amount_paid=Decimal('100.00'),
            payment_status=Payment.SUCCEEDED
        )
        
        # Create mock request
        self.request = self.factory.get('/')
        self.request.user = self.customer


class PaymentEventLoggingTest(PaymentLoggingBaseTest):
    """Test payment event logging functions."""
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_payment_initiated(self, mock_log_activity):
        """Test logging of payment initiation."""
        log_payment_initiated(
            user=self.customer,
            payment=self.payment,
            request=self.request
        )

        # Verify logging function was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'payment_initiated')
        self.assertEqual(call_args[1]['user'], self.customer)

    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_payment_completed(self, mock_log_activity):
        """Test logging of payment completion."""
        log_payment_completed(
            user=self.customer,
            payment=self.payment,
            request=self.request
        )

        # Verify logging function was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'payment_completed')
        self.assertEqual(call_args[1]['user'], self.customer)
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_payment_failed(self, mock_log_activity):
        """Test logging of payment failure."""
        failure_reason = 'Card declined'

        log_payment_failed(
            user=self.customer,
            payment=self.payment,
            failure_reason=failure_reason,
            request=self.request
        )

        # Verify logging function was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'payment_failed')
        self.assertEqual(call_args[1]['user'], self.customer)
    
    @patch('payments_app.logging_utils.log_error')
    def test_log_payment_error(self, mock_log_error):
        """Test logging of payment errors."""
        error_message = 'Stripe API error'
        error_type = 'stripe_api_error'

        log_payment_error(
            error_type=error_type,
            error_message=error_message,
            user=self.customer,
            request=self.request
        )

        # Verify log_error was called
        mock_log_error.assert_called_once()
        call_args = mock_log_error.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['error_type'], error_type)
        self.assertEqual(call_args[1]['error_message'], error_message)
        self.assertEqual(call_args[1]['user'], self.customer)


class RefundEventLoggingTest(PaymentLoggingBaseTest):
    """Test refund event logging functions."""
    
    def setUp(self):
        """Set up additional test data for refund tests."""
        super().setUp()
        
        # Create refund request
        self.refund_request = RefundRequest.objects.create(
            payment=self.payment,
            customer=self.customer,
            reason_category=RefundRequest.SERVICE_NOT_PROVIDED,
            reason_description='Service was not provided as expected',
            requested_amount=Decimal('50.00')
        )
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_refund_requested(self, mock_log_activity):
        """Test logging of refund request."""
        log_refund_requested(
            user=self.customer,
            refund_request=self.refund_request,
            request=self.request
        )

        # Verify log_user_activity was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'refund_requested')
        self.assertEqual(call_args[1]['user'], self.customer)
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_refund_approved(self, mock_log_activity):
        """Test logging of refund approval."""
        log_refund_approved(
            refund_request=self.refund_request,
            user=self.admin_user,
            request=self.request
        )

        # Verify log_user_activity was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'refund_approved')
        self.assertEqual(call_args[1]['user'], self.admin_user)
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_refund_declined(self, mock_log_activity):
        """Test logging of refund decline."""
        log_refund_declined(
            refund_request=self.refund_request,
            user=self.admin_user,
            request=self.request
        )

        # Verify log_user_activity was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'refund_declined')
        self.assertEqual(call_args[1]['user'], self.admin_user)


class ViewAccessLoggingTest(PaymentLoggingBaseTest):
    """Test view access logging functions."""
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_customer_payment_history_viewed(self, mock_log_activity):
        """Test logging of customer payment history access."""
        log_customer_payment_history_viewed(
            customer_user=self.customer,
            request=self.request
        )

        # Verify log_user_activity was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'customer_payment_history_viewed')
        self.assertEqual(call_args[1]['user'], self.customer)
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_customer_refund_history_viewed(self, mock_log_activity):
        """Test logging of customer refund history access."""
        log_customer_refund_history_viewed(
            customer_user=self.customer,
            request=self.request
        )

        # Verify log_user_activity was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'customer_refund_history_viewed')
        self.assertEqual(call_args[1]['user'], self.customer)
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_provider_earnings_viewed(self, mock_log_activity):
        """Test logging of provider earnings access."""
        self.request.user = self.provider

        log_provider_earnings_viewed(
            user=self.provider,
            request=self.request
        )

        # Verify log_user_activity was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'provider_earnings_viewed')
        self.assertEqual(call_args[1]['user'], self.provider)
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_provider_payment_history_viewed(self, mock_log_activity):
        """Test logging of provider payment history access."""
        self.request.user = self.provider

        log_provider_payment_history_viewed(
            user=self.provider,
            request=self.request
        )

        # Verify log_user_activity was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'provider_payment_history_viewed')
        self.assertEqual(call_args[1]['user'], self.provider)
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_log_admin_payment_analytics_viewed(self, mock_log_activity):
        """Test logging of admin payment analytics access."""
        self.request.user = self.admin_user

        log_admin_payment_analytics_viewed(
            admin_user=self.admin_user,
            request=self.request
        )

        # Verify log_user_activity was called
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'admin_payment_analytics_viewed')
        self.assertEqual(call_args[1]['user'], self.admin_user)


class PerformanceMonitoringTest(PaymentLoggingBaseTest):
    """Test performance monitoring functionality."""
    
    @patch('payments_app.logging_utils.log_performance')
    def test_performance_monitor_decorator(self, mock_log_performance):
        """Test the performance monitor decorator."""

        @performance_monitor('test_operation')
        def test_function():
            """Test function for performance monitoring."""
            return "test result"

        # Call the decorated function
        result = test_function()

        # Verify function executed correctly
        self.assertEqual(result, "test result")

        # Verify performance was logged
        mock_log_performance.assert_called_once()
        call_args = mock_log_performance.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['operation'], 'test_operation')
    
    @patch('payments_app.logging_utils.log_performance')
    def test_performance_monitor_with_exception(self, mock_log_performance):
        """Test performance monitor with exception handling."""

        @performance_monitor('failing_operation')
        def failing_function():
            """Test function that raises an exception."""
            raise ValueError("Test error")

        # Call the decorated function and expect exception
        with self.assertRaises(ValueError):
            failing_function()

        # Verify performance was still logged
        mock_log_performance.assert_called_once()
        call_args = mock_log_performance.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['operation'], 'failing_operation')


class LoggingIntegrationTest(PaymentLoggingBaseTest):
    """Test logging integration and edge cases."""
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_logging_with_none_request(self, mock_log_activity):
        """Test logging functions handle None request gracefully."""
        log_payment_initiated(
            user=self.customer,
            payment=self.payment,
            request=None
        )

        # Verify log_user_activity was called without errors
        mock_log_activity.assert_called_once()
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'payment_initiated')
    
    @patch('payments_app.logging_utils.log_error')
    def test_logging_with_anonymous_user(self, mock_log_error):
        """Test logging functions handle anonymous users."""
        from django.contrib.auth.models import AnonymousUser

        anonymous_request = self.factory.get('/')
        anonymous_request.user = AnonymousUser()

        log_payment_error(
            error_type='test_error',
            error_message='Test error',
            user=None,
            request=anonymous_request
        )

        # Verify log_error was called
        mock_log_error.assert_called_once()
        call_args = mock_log_error.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['error_type'], 'test_error')
        self.assertEqual(call_args[1]['error_message'], 'Test error')
    
    @patch('payments_app.logging_utils.log_user_activity')
    def test_logging_structured_data(self, mock_log_activity):
        """Test that logging includes structured data."""
        log_payment_completed(
            user=self.customer,
            payment=self.payment,
            request=self.request
        )

        # Verify log_user_activity was called with structured data
        mock_log_activity.assert_called_once()

        # Check that the call includes details
        call_args = mock_log_activity.call_args
        self.assertEqual(call_args[1]['app_name'], 'payments_app')
        self.assertEqual(call_args[1]['activity_type'], 'payment_completed')
        self.assertEqual(call_args[1]['user'], self.customer)

        # Check details structure
        details = call_args[1]['details']
        self.assertIn('payment_id', details)
        self.assertIn('amount', details)
        self.assertIn('payment_status', details)
