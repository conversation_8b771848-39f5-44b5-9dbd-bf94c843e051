"""
Tests package for payments_app.

This package contains all unit and integration tests for the payments_app.
All test modules are imported here to ensure proper test discovery.
"""

# Import all test modules to ensure they are discovered by <PERSON><PERSON><PERSON>'s test runner
from .test_models import *
from .test_forms import *
from .test_views import *
from .test_utils import *
from .test_urls import *
from .test_logging import *
from .test_integration import *
# Note: test_signals will be imported once signals.py is implemented
# from .test_signals import *