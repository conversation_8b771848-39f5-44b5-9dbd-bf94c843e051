"""Customer-related forms for payments_app."""

# --- Standard Library Imports ---
from decimal import Decimal

# --- Third-Party Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from accounts_app.forms import AccessibleFormMixin
from ..models import Payment, RefundRequest
from .common import RefundAmountValidationMixin


# --- Checkout and Refund Forms ---


class StripeCheckoutForm(AccessibleFormMixin, forms.Form):
    """Form for Stripe checkout process (placeholder)."""

    payment_method = forms.ChoiceField(
        label=_('Payment Method'),
        choices=[('stripe', _('Credit/Debit Card (Stripe)'))],
        initial='stripe',
        widget=forms.Select(attrs={'class': 'form-select', 'readonly': True})
    )

    billing_name = forms.CharField(
        label=_('Full Name'),
        max_length=100,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter your full name', 'required': True})
    )
    billing_email = forms.EmailField(
        label=_('Email Address'),
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Enter your email address', 'required': True})
    )
    billing_phone = forms.CharField(
        label=_('Phone Number'),
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter your phone number', 'type': 'tel'})
    )
    billing_address = forms.CharField(
        label=_('Address'),
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter your address'})
    )
    billing_city = forms.CharField(
        label=_('City'),
        max_length=100,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter your city'})
    )
    billing_state = forms.CharField(
        label=_('State'),
        max_length=50,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter your state'})
    )
    billing_zip_code = forms.CharField(
        label=_('ZIP Code'),
        max_length=10,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter your ZIP code'})
    )
    accept_terms = forms.BooleanField(
        label=_('I accept the Terms and Conditions and Privacy Policy'),
        required=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input', 'aria-label': _('I accept the Terms and Conditions and Privacy Policy')})
    )
    save_payment_method = forms.BooleanField(
        label=_('Save payment method for future purchases'),
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input', 'aria-label': _('Save payment method for future purchases')})
    )

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.booking = kwargs.pop('booking', None)
        super().__init__(*args, **kwargs)

        if self.user and hasattr(self.user, 'customerprofile'):
            profile = self.user.customerprofile
            self.fields['billing_name'].initial = f"{profile.first_name} {profile.last_name}".strip()
            self.fields['billing_email'].initial = self.user.email
            self.fields['billing_phone'].initial = profile.phone_number
            self.fields['billing_address'].initial = profile.address
            self.fields['billing_city'].initial = profile.city

    def clean_billing_email(self):
        billing_email = self.cleaned_data.get('billing_email')
        if self.user and billing_email != self.user.email:
            raise ValidationError(_('Billing email must match your account email.'))
        return billing_email


class RefundRequestForm(RefundAmountValidationMixin, AccessibleFormMixin, forms.ModelForm):
    """Form for customers to request refunds."""

    class Meta:
        model = RefundRequest
        fields = ['reason_category', 'reason_description', 'requested_amount']
        widgets = {
            'reason_category': forms.Select(attrs={'class': 'form-select', 'required': True}),
            'reason_description': forms.Textarea(attrs={
                'class': 'form-control', 'rows': 4,
                'placeholder': 'Please provide a detailed explanation for your refund request...',
                'maxlength': 1000, 'required': True
            }),
            'requested_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01', 'placeholder': '0.00', 'required': True})
        }
        labels = {
            'reason_category': _('Reason Category'),
            'reason_description': _('Detailed Explanation'),
            'requested_amount': _('Refund Amount ($)')
        }
        help_texts = {
            'reason_category': _('Select the category that best describes your refund reason'),
            'reason_description': _('Provide a detailed explanation to help us process your request'),
            'requested_amount': _('Enter the amount you would like refunded (maximum: full payment amount)')
        }

    def __init__(self, *args, **kwargs):
        self.payment = kwargs.pop('payment', None)
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        if self.payment:
            max_amount = self.payment.remaining_refundable_amount
            self.fields['requested_amount'].widget.attrs['max'] = str(max_amount)
            self.fields['requested_amount'].initial = max_amount
            self.fields['requested_amount'].help_text = _(
                f'Enter the amount you would like refunded (maximum: ${max_amount})'
            )

    def clean_requested_amount(self):
        amount = self.cleaned_data.get('requested_amount')
        return self.validate_refund_amount(amount, self.payment)

    def clean_reason_description(self):
        reason_description = self.cleaned_data.get('reason_description')
        if not reason_description or len(reason_description.strip()) < 10:
            raise ValidationError(_('Please provide a detailed explanation of at least 10 characters.'))
        return reason_description.strip()

    def save(self, commit=True):
        refund_request = super().save(commit=False)
        if self.payment:
            refund_request.payment = self.payment
            refund_request.customer = self.payment.customer
        if commit:
            refund_request.save()
        return refund_request


class PaymentSearchForm(AccessibleFormMixin, forms.Form):
    """Form for searching and filtering payment history."""

    STATUS_CHOICES = [('', _('All Statuses'))] + Payment.STATUS_CHOICES
    PAYMENT_METHOD_CHOICES = [('', _('All Payment Methods'))] + Payment.PAYMENT_METHOD_CHOICES

    search_query = forms.CharField(
        label=_('Search'),
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Search by booking ID, payment ID, or amount...', 'aria-label': _('Search')})
    )
    status = forms.ChoiceField(
        label=_('Status'),
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select', 'aria-label': _('Status')})
    )
    payment_method = forms.ChoiceField(
        label=_('Payment Method'),
        choices=PAYMENT_METHOD_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select', 'aria-label': _('Payment Method')})
    )
    date_from = forms.DateField(
        label=_('From Date'),
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date', 'aria-label': _('From Date')})
    )
    date_to = forms.DateField(
        label=_('To Date'),
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date', 'aria-label': _('To Date')})
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_('From date cannot be later than to date.'))
        return cleaned_data


class RefundSearchForm(AccessibleFormMixin, forms.Form):
    """Form for searching and filtering refund requests."""

    STATUS_CHOICES = [('', _('All Statuses'))] + RefundRequest.STATUS_CHOICES
    REASON_CHOICES = [('', _('All Reasons'))] + RefundRequest.REASON_CHOICES

    search_query = forms.CharField(
        label=_('Search'),
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Search by refund ID, payment ID, or reason...'})
    )
    status = forms.ChoiceField(
        label=_('Status'),
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    reason_category = forms.ChoiceField(
        label=_('Reason Category'),
        choices=REASON_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_from = forms.DateField(
        label=_('From Date'),
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        label=_('To Date'),
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_('From date cannot be later than to date.'))
        return cleaned_data
