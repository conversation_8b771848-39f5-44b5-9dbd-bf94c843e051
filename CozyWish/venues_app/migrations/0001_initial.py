# Generated by Django 5.2.3 on 2025-06-16 08:36

import django.contrib.postgres.search
import django.core.validators
import django.db.models.deletion
import venues_app.models
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category_name', models.CharField(help_text='Category name (e.g., Spa, Massage, Salon)', max_length=100, verbose_name='category name')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated)', max_length=120, null=True, unique=True, verbose_name='slug')),
                ('category_description', models.TextField(blank=True, help_text='Optional description of the category', verbose_name='description')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is active and visible', verbose_name='active status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'ordering': ['category_name'],
                'indexes': [models.Index(fields=['category_name'], name='venues_app__categor_7e4253_idx'), models.Index(fields=['is_active'], name='venues_app__is_acti_72ab66_idx')],
            },
        ),
        migrations.CreateModel(
            name='USCity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city', models.CharField(help_text='City name', max_length=100, verbose_name='city')),
                ('state_id', models.CharField(help_text='State abbreviation (e.g., NY, CA)', max_length=2, verbose_name='state abbreviation')),
                ('state_name', models.CharField(help_text='Full state name', max_length=100, verbose_name='state name')),
                ('county_name', models.CharField(help_text='County name', max_length=100, verbose_name='county name')),
                ('latitude', models.DecimalField(decimal_places=7, help_text='Latitude coordinate', max_digits=10, verbose_name='latitude')),
                ('longitude', models.DecimalField(decimal_places=7, help_text='Longitude coordinate', max_digits=10, verbose_name='longitude')),
                ('zip_codes', models.TextField(help_text='Space-separated list of ZIP codes', verbose_name='ZIP codes')),
                ('city_id', models.CharField(help_text='Unique identifier for the city', max_length=20, unique=True, verbose_name='city ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'verbose_name': 'US City',
                'verbose_name_plural': 'US Cities',
                'ordering': ['state_name', 'city'],
                'indexes': [models.Index(fields=['city'], name='venues_app__city_f629c9_idx'), models.Index(fields=['state_name'], name='venues_app__state_n_569810_idx'), models.Index(fields=['county_name'], name='venues_app__county__cc5321_idx'), models.Index(fields=['state_id'], name='venues_app__state_i_cc2426_idx')],
            },
        ),
        migrations.CreateModel(
            name='Venue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('venue_name', models.CharField(help_text='Name of the venue/business', max_length=255, verbose_name='venue name')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated)', max_length=255, null=True, unique=True, verbose_name='slug')),
                ('short_description', models.TextField(help_text='Brief description of the venue (max 500 characters)', max_length=500, verbose_name='description')),
                ('state', models.CharField(choices=[('AL', 'Alabama'), ('AK', 'Alaska'), ('AZ', 'Arizona'), ('AR', 'Arkansas'), ('CA', 'California'), ('CO', 'Colorado'), ('CT', 'Connecticut'), ('DE', 'Delaware'), ('FL', 'Florida'), ('GA', 'Georgia'), ('HI', 'Hawaii'), ('ID', 'Idaho'), ('IL', 'Illinois'), ('IN', 'Indiana'), ('IA', 'Iowa'), ('KS', 'Kansas'), ('KY', 'Kentucky'), ('LA', 'Louisiana'), ('ME', 'Maine'), ('MD', 'Maryland'), ('MA', 'Massachusetts'), ('MI', 'Michigan'), ('MN', 'Minnesota'), ('MS', 'Mississippi'), ('MO', 'Missouri'), ('MT', 'Montana'), ('NE', 'Nebraska'), ('NV', 'Nevada'), ('NH', 'New Hampshire'), ('NJ', 'New Jersey'), ('NM', 'New Mexico'), ('NY', 'New York'), ('NC', 'North Carolina'), ('ND', 'North Dakota'), ('OH', 'Ohio'), ('OK', 'Oklahoma'), ('OR', 'Oregon'), ('PA', 'Pennsylvania'), ('RI', 'Rhode Island'), ('SC', 'South Carolina'), ('SD', 'South Dakota'), ('TN', 'Tennessee'), ('TX', 'Texas'), ('UT', 'Utah'), ('VT', 'Vermont'), ('VA', 'Virginia'), ('WA', 'Washington'), ('WV', 'West Virginia'), ('WI', 'Wisconsin'), ('WY', 'Wyoming')], help_text='State where venue is located', max_length=2, verbose_name='state')),
                ('county', models.CharField(help_text='County where the venue is located', max_length=100, verbose_name='county')),
                ('city', models.CharField(help_text='City where the venue is located', max_length=100, verbose_name='city')),
                ('street_number', models.CharField(help_text='Street number', max_length=20, verbose_name='street number')),
                ('street_name', models.CharField(help_text='Street name', max_length=255, verbose_name='street name')),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, help_text='Latitude coordinate for mapping', max_digits=10, null=True, verbose_name='latitude')),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, help_text='Longitude coordinate for mapping', max_digits=11, null=True, verbose_name='longitude')),
                ('main_image', models.ImageField(blank=True, help_text='Main featured image for the venue', null=True, upload_to=venues_app.models.get_venue_main_image_path, verbose_name='main image')),
                ('operating_hours', models.TextField(blank=True, help_text='Operating hours (e.g., 9AM-5PM daily schedule)', max_length=500, verbose_name='operating hours')),
                ('opening_notes', models.TextField(blank=True, help_text='Custom notes about opening times', max_length=300, verbose_name='opening notes')),
                ('tags', models.CharField(blank=True, db_index=True, help_text='Keywords for search optimization (comma-separated)', max_length=255, verbose_name='tags')),
                ('search_vector', django.contrib.postgres.search.SearchVectorField(blank=True, editable=False, help_text='Search optimization field', null=True)),
                ('approval_status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', help_text='Admin approval status', max_length=20, verbose_name='approval status')),
                ('visibility', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='active', help_text='Visibility to customers', max_length=20, verbose_name='visibility')),
                ('admin_notes', models.TextField(blank=True, help_text='Internal notes about approval/rejection', verbose_name='admin notes')),
                ('status_log', models.JSONField(default=list, editable=False, help_text='Audit log of status changes')),
                ('is_deleted', models.BooleanField(default=False, help_text='Mark as deleted instead of actual deletion', verbose_name='deleted status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('approved_at', models.DateTimeField(blank=True, help_text='When the venue was approved', null=True, verbose_name='approved at')),
                ('last_modified_at', models.DateTimeField(auto_now=True, help_text='When the venue was last modified', verbose_name='last modified at')),
                ('last_modified_by', models.ForeignKey(blank=True, help_text='User who last modified this venue', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_venues', to=settings.AUTH_USER_MODEL, verbose_name='last modified by')),
                ('service_provider', models.OneToOneField(help_text='Service provider who owns this venue', on_delete=django.db.models.deletion.CASCADE, related_name='venue', to='accounts_app.serviceproviderprofile', verbose_name='service provider')),
                ('us_city', models.ForeignKey(blank=True, help_text='Standardized location data', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='venues', to='venues_app.uscity', verbose_name='US city')),
            ],
            options={
                'verbose_name': 'Venue',
                'verbose_name_plural': 'Venues',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VenueCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='category_venues', to='venues_app.category', verbose_name='category')),
                ('venue', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='venue_categories', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Venue Category',
                'verbose_name_plural': 'Venue Categories',
                'unique_together': {('venue', 'category')},
            },
        ),
        migrations.AddField(
            model_name='venue',
            name='categories',
            field=models.ManyToManyField(blank=True, help_text='Categories this venue belongs to', related_name='venues', through='venues_app.VenueCategory', to='venues_app.category', verbose_name='categories'),
        ),
        migrations.CreateModel(
            name='VenueFAQ',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.CharField(help_text='Frequently asked question', max_length=255, verbose_name='question')),
                ('answer', models.TextField(help_text='Answer to the question (max 500 characters)', max_length=500, verbose_name='answer')),
                ('order', models.PositiveIntegerField(default=1, help_text='Display order of the FAQ (1-5)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='display order')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this FAQ is visible', verbose_name='active status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('venue', models.ForeignKey(help_text='Venue this FAQ belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='faqs', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Venue FAQ',
                'verbose_name_plural': 'Venue FAQs',
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='VenueImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(help_text='Venue image file', upload_to=venues_app.models.get_venue_gallery_image_path, verbose_name='image')),
                ('order', models.PositiveIntegerField(default=1, help_text='Display order of the image (1-7)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(7)], verbose_name='display order')),
                ('caption', models.CharField(blank=True, help_text='Optional caption for the image', max_length=255, verbose_name='caption')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this image is visible', verbose_name='active status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('venue', models.ForeignKey(help_text='Venue this image belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='images', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Venue Image',
                'verbose_name_plural': 'Venue Images',
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_title', models.CharField(help_text='Title/name of the service', max_length=255, verbose_name='service title')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated)', max_length=255, null=True, unique=True, verbose_name='slug')),
                ('short_description', models.TextField(blank=True, help_text='Brief description of the service', max_length=500, verbose_name='description')),
                ('price_min', models.DecimalField(decimal_places=2, help_text='Minimum price for this service', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='minimum price')),
                ('price_max', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum price for variable pricing', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='maximum price')),
                ('duration_minutes', models.PositiveIntegerField(default=60, help_text='Duration of the service in minutes', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(1440)], verbose_name='duration (minutes)')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this service is bookable', verbose_name='active status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('venue', models.ForeignKey(help_text='Venue where this service is offered', on_delete=django.db.models.deletion.CASCADE, related_name='services', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Service',
                'verbose_name_plural': 'Services',
                'ordering': ['service_title'],
                'indexes': [models.Index(fields=['service_title'], name='venues_app__service_7a8165_idx'), models.Index(fields=['is_active'], name='venues_app__is_acti_e1bdc5_idx')],
            },
        ),
        migrations.CreateModel(
            name='OperatingHours',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day', models.PositiveSmallIntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')], help_text='Day of the week (0=Monday, 6=Sunday)', verbose_name='day of week')),
                ('opening', models.TimeField(blank=True, help_text='Opening time for this day', null=True, verbose_name='opening time')),
                ('closing', models.TimeField(blank=True, help_text='Closing time for this day', null=True, verbose_name='closing time')),
                ('is_closed', models.BooleanField(default=False, help_text='Whether the venue is closed on this day', verbose_name='closed')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('venue', models.ForeignKey(help_text='Venue these hours belong to', on_delete=django.db.models.deletion.CASCADE, related_name='operating_hours_set', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Operating Hours',
                'verbose_name_plural': 'Operating Hours',
                'ordering': ['day'],
                'unique_together': {('venue', 'day')},
            },
        ),
        migrations.CreateModel(
            name='FlaggedVenue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.TextField(help_text='Reason for flagging this venue', max_length=1000, verbose_name='reason')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('reviewed', 'Reviewed'), ('resolved', 'Resolved')], default='pending', help_text='Status of the flag review', max_length=20, verbose_name='status')),
                ('admin_notes', models.TextField(blank=True, help_text='Admin notes about the flag review', verbose_name='admin notes')),
                ('reviewed_at', models.DateTimeField(blank=True, help_text='When the flag was reviewed', null=True, verbose_name='reviewed at')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('flagged_by', models.ForeignKey(help_text='Customer who flagged this venue', on_delete=django.db.models.deletion.CASCADE, related_name='flagged_venues', to=settings.AUTH_USER_MODEL, verbose_name='flagged by')),
                ('reviewed_by', models.ForeignKey(blank=True, help_text='Admin who reviewed this flag', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_flags', to=settings.AUTH_USER_MODEL, verbose_name='reviewed by')),
                ('venue', models.ForeignKey(help_text='Venue that has been flagged', on_delete=django.db.models.deletion.CASCADE, related_name='flags', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Flagged Venue',
                'verbose_name_plural': 'Flagged Venues',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['status'], name='venues_app__status_92eadd_idx'), models.Index(fields=['created_at'], name='venues_app__created_3f43c5_idx')],
                'unique_together': {('venue', 'flagged_by')},
            },
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['venue_name'], name='venues_app__venue_n_e2ef62_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['approval_status'], name='venues_app__approva_4dd942_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['visibility'], name='venues_app__visibil_24d4b7_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['state', 'county', 'city'], name='venues_app__state_5d5c7d_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['approval_status', 'visibility'], name='venues_app__approva_48ce4d_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['service_provider'], name='venues_app__service_3877cd_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['is_deleted'], name='venues_app__is_dele_126c6c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='venuefaq',
            unique_together={('venue', 'order')},
        ),
        migrations.AlterUniqueTogether(
            name='venueimage',
            unique_together={('venue', 'order')},
        ),
    ]
