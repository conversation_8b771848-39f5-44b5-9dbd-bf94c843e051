# Generated by Django 5.2.3 on 2025-06-17 08:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0003_add_contact_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='VenueAmenity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amenity_type', models.CharField(choices=[('wifi', 'Wi-Fi'), ('parking', 'Parking Available'), ('wheelchair_accessible', 'Wheelchair Accessible'), ('air_conditioning', 'Air Conditioning'), ('heating', 'Heating'), ('music', 'Background Music'), ('private_rooms', 'Private Treatment Rooms'), ('changing_rooms', 'Changing Rooms'), ('shower_facilities', 'Shower Facilities'), ('refreshments', 'Refreshments Available'), ('retail_shop', 'Retail Shop'), ('online_booking', 'Online Booking'), ('credit_cards', 'Credit Cards Accepted'), ('gift_certificates', 'Gift Certificates'), ('loyalty_program', 'Loyalty Program')], help_text='Type of amenity or feature', max_length=50, verbose_name='amenity type')),
                ('custom_name', models.CharField(blank=True, help_text='Custom name for the amenity (optional)', max_length=100, verbose_name='custom name')),
                ('description', models.TextField(blank=True, help_text='Additional details about this amenity', max_length=200, verbose_name='description')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this amenity is currently available', verbose_name='active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('venue', models.ForeignKey(help_text='Venue this amenity belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='amenities', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Venue Amenity',
                'verbose_name_plural': 'Venue Amenities',
                'ordering': ['amenity_type'],
                'unique_together': {('venue', 'amenity_type')},
            },
        ),
    ]
