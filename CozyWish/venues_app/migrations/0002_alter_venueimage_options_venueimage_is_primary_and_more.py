# Generated by Django 5.2.3 on 2025-06-17 08:25

import venues_app.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='venueimage',
            options={'ordering': ['-is_primary', 'order', 'created_at'], 'verbose_name': 'Venue Image', 'verbose_name_plural': 'Venue Images'},
        ),
        migrations.AddField(
            model_name='venueimage',
            name='is_primary',
            field=models.BooleanField(default=False, help_text='Whether this is the primary image for the venue', verbose_name='primary image'),
        ),
        migrations.AlterField(
            model_name='venueimage',
            name='image',
            field=models.ImageField(help_text='Venue image file (JPG/PNG, max 5MB)', upload_to=venues_app.models.get_venue_gallery_image_path, verbose_name='image'),
        ),
        migrations.AddConstraint(
            model_name='venueimage',
            constraint=models.UniqueConstraint(condition=models.Q(('is_primary', True)), fields=('venue',), name='unique_primary_image_per_venue'),
        ),
    ]
