from django.urls import path
from . import views


app_name = 'venues_app'

urlpatterns = [
    # Public views - non-slug patterns first
    path('', views.venue_search, name='venue_list'),
    path('search/', views.venue_search, name='venue_search'),

    # Search and filter APIs
    path('api/location-autocomplete/', views.location_autocomplete, name='location_autocomplete'),
    path('api/location-data/', views.get_location_data, name='get_location_data'),
    path('category/<slug:category_slug>/', views.category_venues, name='category_venues'),

    # Service Provider views - specific patterns before slug patterns
    path('provider/venues/', views.provider_venues, name='provider_venues'),
    path('provider/venues/<int:venue_id>/', views.provider_venue_detail, name='provider_venue_detail'),
    path('provider/venues/<int:venue_id>/change-status/', views.change_venue_status, name='change_venue_status'),
    path('provider/services/', views.manage_services, name='manage_services'),
    path('provider/faqs/', views.manage_faqs, name='manage_faqs'),
    path('provider/faqs/<int:faq_id>/edit/', views.edit_faq, name='edit_faq'),
    path('provider/faqs/<int:faq_id>/delete/', views.delete_faq, name='delete_faq'),
    path('provider/operating-hours/', views.manage_operating_hours, name='manage_operating_hours'),
    path('provider/amenities/', views.manage_amenities, name='manage_amenities'),
    path('provider/amenities/<int:amenity_id>/edit/', views.edit_amenity, name='edit_amenity'),
    path('provider/amenities/<int:amenity_id>/delete/', views.delete_amenity, name='delete_amenity'),

    # Image Management views
    path('provider/images/', views.manage_venue_images, name='manage_venue_images'),
    path('provider/images/upload/', views.upload_venue_image, name='upload_venue_image'),
    path('provider/images/<int:image_id>/set-primary/', views.set_primary_image, name='set_primary_image'),
    path('provider/images/reorder/', views.reorder_venue_images, name='reorder_venue_images'),
    path('provider/images/<int:image_id>/delete/', views.delete_venue_image, name='delete_venue_image'),

    # Class-based views that exist
    path('provider/create/', views.venue_create, name='venue_create'),
    path('provider/edit/', views.venue_edit, name='venue_edit'),
    path('provider/delete/', views.venue_delete, name='venue_delete'),

    # Service management URLs
    path('provider/services/create/', views.service_create, name='service_create'),
    path('provider/services/<int:pk>/edit/', views.service_edit, name='service_edit'),
    path('provider/services/<int:pk>/delete/', views.service_delete, name='service_delete'),

    # Public views with slug patterns - these must come AFTER specific patterns
    path('<slug:venue_slug>/', views.venue_detail, name='venue_detail'),
    path('<slug:venue_slug>/services/<slug:service_slug>/', views.service_detail, name='service_detail'),
    path('<slug:venue_slug>/flag/', views.flag_venue, name='flag_venue'),

    # Admin URLs - Venue Approval Dashboard
    path('admin/dashboard/', views.admin_venue_approval_dashboard, name='admin_venue_approval_dashboard'),
    path('admin/venues/', views.admin_venue_list, name='admin_venue_list'),
    path('admin/venues/pending/', views.admin_pending_venues, name='admin_pending_venues'),
    path('admin/venues/<int:venue_id>/', views.admin_venue_detail, name='admin_venue_detail'),
    path('admin/venues/<int:venue_id>/approval/', views.admin_venue_approval, name='admin_venue_approval'),
    path('admin/flagged-venues/', views.admin_flagged_venues, name='admin_flagged_venues'),

    # Admin URLs - Category Management
    path('admin/categories/', views.admin_category_list, name='admin_category_list'),
    path('admin/categories/create/', views.admin_category_create, name='admin_category_create'),
    path('admin/categories/<int:category_id>/edit/', views.admin_category_edit, name='admin_category_edit'),
    path('admin/categories/<int:category_id>/delete/', views.admin_category_delete, name='admin_category_delete'),
    path('admin/categories/<int:category_id>/toggle/', views.admin_category_toggle_status, name='admin_category_toggle_status'),
]
