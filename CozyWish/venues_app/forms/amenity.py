"""Forms for venue amenity management."""

# --- Django Imports ---
from django import forms
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import VenueAmenity


try:
    from utils.sanitization import sanitize_html
except ImportError:
    sanitize_html = lambda x: x


class VenueAmenityForm(forms.ModelForm):
    """Form for managing venue amenities."""

    class Meta:
        model = VenueAmenity
        fields = ['amenity_type', 'custom_name', 'description', 'is_active']
        widgets = {
            'amenity_type': forms.Select(attrs={
                'class': 'form-control',
            }),
            'custom_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Custom name (optional)',
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Additional details about this amenity',
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
            }),
        }

    def clean_custom_name(self):
        """Validate custom name."""
        custom_name = self.cleaned_data.get('custom_name', '')
        if custom_name:
            custom_name = custom_name.strip()
        return sanitize_html(custom_name)

    def clean_description(self):
        """Validate description."""
        description = self.cleaned_data.get('description', '')
        return sanitize_html(description)
