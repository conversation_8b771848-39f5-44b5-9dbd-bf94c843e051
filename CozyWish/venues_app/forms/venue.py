# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from accounts_app.models import ServiceProviderProfile
from ..models import Category, USCity, Venue, VenueImage, OperatingHours
from .operating_hours import OperatingHoursFormSetFactory

# --- Image Processing Imports ---
try:
    from utils.forms import ImageUploadForm
    from utils.image_service import ImageService
    from utils.image_utils import validate_image_extension, validate_image_size
    from utils.sanitization import sanitize_html
except ImportError:
    ImageUploadForm = None
    ImageService = None
    sanitize_html = lambda x: x


class VenueCreateForm(forms.ModelForm):
    """Simplified form for creating venues with essential fields only."""

    # Venue status selection field
    VENUE_STATUS_CHOICES = [
        ('draft', _('Save as Draft')),
        ('pending', _('Submit for Admin Approval')),
    ]

    venue_status = forms.ChoiceField(
        choices=VENUE_STATUS_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
        initial='draft',
        help_text=_('Choose whether to save as draft or submit for approval')
    )

    class Meta:
        model = Venue
        fields = [
            'venue_name', 'state', 'county', 'city', 'street_number', 'street_name', 'short_description'
        ]
        widgets = {
            'venue_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter venue name',
            }),
            'state': forms.Select(attrs={'class': 'form-select'}),
            'county': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter county name',
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter city name',
            }),
            'street_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Street number',
            }),
            'street_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Street name',
            }),
            'short_description': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Briefly describe your venue and services (minimum 10 characters)',
                'rows': 3,
                'maxlength': 500
            }),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Set required fields
        self.fields['venue_name'].required = True
        self.fields['short_description'].required = True
        self.fields['state'].required = True
        self.fields['county'].required = True
        self.fields['city'].required = True
        self.fields['street_number'].required = True
        self.fields['street_name'].required = True

        # Set up state choices from model STATE_CHOICES
        from ..models import Venue
        self.fields['state'].choices = [('', 'Select state')] + list(Venue.STATE_CHOICES)

    def clean_venue_name(self):
        """Validate venue name."""
        venue_name = self.cleaned_data.get('venue_name', '').strip()
        if not venue_name:
            raise ValidationError(_('Venue name is required'))
        if len(venue_name) < 2:
            raise ValidationError(_('Venue name must be at least 2 characters long'))
        return sanitize_html(venue_name)

    def clean_short_description(self):
        """Validate short description."""
        description = self.cleaned_data.get('short_description', '').strip()
        if not description:
            raise ValidationError(_('Short description is required'))
        if len(description) < 10:
            raise ValidationError(_('Description must be at least 10 characters long'))
        return sanitize_html(description)

    def clean_state(self):
        """Validate state selection."""
        state = self.cleaned_data.get('state', '').strip()
        if not state:
            raise ValidationError(_('State is required'))
        return state

    def clean_county(self):
        """Validate county."""
        county = self.cleaned_data.get('county', '').strip()
        if not county:
            raise ValidationError(_('County is required'))
        return sanitize_html(county)

    def clean_city(self):
        """Validate city."""
        city = self.cleaned_data.get('city', '').strip()
        if not city:
            raise ValidationError(_('City is required'))
        return sanitize_html(city)

    def clean_street_number(self):
        """Validate street number."""
        street_number = self.cleaned_data.get('street_number', '').strip()
        if not street_number:
            raise ValidationError(_('Street number is required'))
        return sanitize_html(street_number)

    def clean_street_name(self):
        """Validate street name."""
        street_name = self.cleaned_data.get('street_name', '').strip()
        if not street_name:
            raise ValidationError(_('Street name is required'))
        return sanitize_html(street_name)





    def save(self, commit=True):
        venue = super().save(commit=False)

        if not venue.pk and self.user:
            try:
                service_provider_profile = self.user.service_provider_profile
                venue.service_provider = service_provider_profile
            except ServiceProviderProfile.DoesNotExist:
                raise ValidationError(_('User must have a service provider profile to create a venue'))

        # Set approval status based on venue_status selection
        venue_status = self.cleaned_data.get('venue_status', 'draft')
        if venue_status == 'draft':
            venue.approval_status = Venue.DRAFT
        else:
            venue.approval_status = Venue.PENDING

        venue.visibility = Venue.ACTIVE

        if commit:
            venue.save()

        return venue


class VenueForm(forms.ModelForm):
    """Form for creating and editing venues."""

    categories = forms.ModelMultipleChoiceField(
        queryset=Category.objects.filter(is_active=True),
        widget=forms.CheckboxSelectMultiple,
        required=False,  # Temporarily make optional for debugging
        help_text=_('Select at least one category that best describes your venue')
    )

    class Meta:
        model = Venue
        fields = [
            'venue_name', 'short_description', 'phone', 'email', 'website_url',
            'state', 'county', 'city', 'street_number', 'street_name',
            'latitude', 'longitude',
            'opening_notes', 'tags', 'categories'
        ]
        widgets = {
            'venue_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter venue name',
            }),
            'short_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Brief description of your venue (max 500 characters)',
                'maxlength': 500,
            }),

            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Contact phone number (e.g., (*************)',
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Contact email address',
            }),
            'website_url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'Website URL (e.g., https://www.example.com)',
            }),
            'state': forms.Select(attrs={'class': 'form-select'}),
            'county': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter county name',
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter city name',
            }),
            'street_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Street number',
            }),
            'street_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Street name',
            }),
            'latitude': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': 'any',
                'placeholder': 'Latitude (optional)',
            }),
            'longitude': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': 'any',
                'placeholder': 'Longitude (optional)',
            }),

            'opening_notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Additional notes about opening times (e.g., "Closed on holidays")',
            }),
            'tags': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter up to 5 tags separated by commas (e.g., spa, massage, relaxation)',
            }),
        }
        help_texts = {
            'tags': _('Keywords for search optimization (maximum 5, comma-separated)'),
            'latitude': _('Optional: Used for map display'),
            'longitude': _('Optional: Used for map display'),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        self.fields['venue_name'].required = True
        self.fields['short_description'].required = True
        self.fields['state'].required = True
        self.fields['county'].required = True
        self.fields['city'].required = True
        self.fields['street_number'].required = True
        self.fields['street_name'].required = True



        # Set up state choices from model STATE_CHOICES
        from ..models import Venue
        self.fields['state'].choices = [('', 'Select state')] + list(Venue.STATE_CHOICES)

    def clean_venue_name(self):
        venue_name = self.cleaned_data.get('venue_name', '').strip()
        if not venue_name:
            raise ValidationError(_('Venue name is required'))
        if len(venue_name) < 2:
            raise ValidationError(_('Venue name must be at least 2 characters long'))
        return sanitize_html(venue_name)

    def clean_short_description(self):
        description = self.cleaned_data.get('short_description', '').strip()
        if not description:
            raise ValidationError(_('Short description is required'))
        if len(description) < 10:
            raise ValidationError(_('Description must be at least 10 characters long'))
        return sanitize_html(description)

    def clean_state(self):
        state = self.cleaned_data.get('state', '').strip()
        if not state:
            raise ValidationError(_('State is required'))
        return state

    def clean_county(self):
        county = self.cleaned_data.get('county', '').strip()
        if not county:
            raise ValidationError(_('County is required'))
        return sanitize_html(county)

    def clean_city(self):
        city = self.cleaned_data.get('city', '').strip()
        if not city:
            raise ValidationError(_('City is required'))
        return sanitize_html(city)

    def clean_street_number(self):
        street_number = self.cleaned_data.get('street_number', '').strip()
        if not street_number:
            raise ValidationError(_('Street number is required'))
        return sanitize_html(street_number)

    def clean_street_name(self):
        street_name = self.cleaned_data.get('street_name', '').strip()
        if not street_name:
            raise ValidationError(_('Street name is required'))
        return sanitize_html(street_name)

    def clean_phone(self):
        phone = self.cleaned_data.get('phone', '').strip()
        if phone:
            # Basic phone validation - allow various formats
            import re
            # More lenient phone pattern - allows letters, numbers, spaces, dashes, parentheses, dots, plus
            phone_pattern = r'^[\+]?[\d\s\-\(\)\.a-zA-Z]{7,20}$'
            if not re.match(phone_pattern, phone):
                raise ValidationError(_('Please enter a valid phone number (7-20 characters, letters and numbers allowed)'))
        return sanitize_html(phone)

    def clean_email(self):
        email = self.cleaned_data.get('email', '').strip()
        if email:
            # Django's EmailField already validates format
            pass
        return email

    def clean_website_url(self):
        website_url = self.cleaned_data.get('website_url', '').strip()
        if website_url:
            # Django's URLField already validates format
            if not website_url.startswith(('http://', 'https://')):
                website_url = 'https://' + website_url
        return website_url

    def clean_categories(self):
        categories = self.cleaned_data.get('categories')
        # Temporarily disable validation for debugging
        # if not categories or not categories.exists():
        #     raise ValidationError(_('Please select at least one category for your venue'))
        return categories

    def clean_tags(self):
        tags = self.cleaned_data.get('tags', '')
        if tags:
            tags_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
            if len(tags_list) > 5:
                raise ValidationError(_('Maximum 5 tags allowed'))
            cleaned = ', '.join(tags_list)
            return sanitize_html(cleaned)
        return sanitize_html(tags)

    def clean_opening_notes(self):
        return sanitize_html(self.cleaned_data.get('opening_notes', ''))





    def save(self, commit=True):
        venue = super().save(commit=False)

        if not venue.pk and self.user:
            try:
                service_provider_profile = self.user.service_provider_profile
                venue.service_provider = service_provider_profile
            except ServiceProviderProfile.DoesNotExist:
                raise ValidationError(_('User must have a service provider profile to create a venue'))

        if not venue.pk:
            venue.approval_status = Venue.PENDING
            venue.visibility = Venue.ACTIVE

        if commit:
            venue.save()
            if 'categories' in self.cleaned_data:
                # Handle categories through VenueCategory model since it uses 'through'
                from ..models import VenueCategory
                # Clear existing categories for this venue
                VenueCategory.objects.filter(venue=venue).delete()
                # Add new categories
                for category in self.cleaned_data['categories']:
                    VenueCategory.objects.create(venue=venue, category=category)


        return venue


class VenueImageForm(forms.ModelForm):
    """Form for uploading additional venue images."""

    class Meta:
        model = VenueImage
        fields = ['image', 'caption', 'order']
        widgets = {
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/jpeg,image/jpg,image/png',
            }),
            'caption': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Optional caption for this image',
            }),
            'order': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'max': 5,
            }),
        }
        help_texts = {
            'image': _('Upload venue image (JPG/PNG, max 5MB)'),
            'order': _('Display order (1-5)'),
        }

    def clean_image(self):
        image = self.cleaned_data.get('image')
        if image and hasattr(image, 'content_type'):
            try:
                validate_image_extension(image.name, 'venue_gallery')
            except ValidationError as e:
                raise ValidationError(str(e))
            try:
                validate_image_size(image, max_size_kb=5120)  # 5MB in KB
            except ValidationError as e:
                raise ValidationError(str(e))
        return image

    def save(self, commit=True):
        venue_image = super().save(commit=False)
        if commit:
            try:
                venue_image.save()
                # Simple direct save - let Django and S3 handle the upload
                # The complex image processing is causing issues, so we'll use direct upload
            except Exception as e:
                # Log the error for debugging
                import logging
                logger = logging.getLogger('venues_app')
                logger.error(f"Failed to save venue image: {str(e)}", exc_info=True)
                raise ValidationError(f"Failed to upload image: {str(e)}")
        return venue_image


class MultipleFileInput(forms.ClearableFileInput):
    """Custom widget for multiple file uploads."""
    allow_multiple_selected = True

    def __init__(self, attrs=None):
        if attrs is None:
            attrs = {}
        attrs.setdefault('multiple', True)
        super().__init__(attrs)


class VenueGalleryImagesForm(forms.Form):
    """Form for handling 5 individual image uploads during venue creation."""

    image_1 = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control image-upload-input',
            'accept': 'image/jpeg,image/png',
            'data-slot': '1'
        }),
        help_text=_('Upload venue image (JPG/PNG, max 5MB)')
    )

    image_2 = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control image-upload-input',
            'accept': 'image/jpeg,image/png',
            'data-slot': '2'
        }),
        help_text=_('Upload venue image (JPG/PNG, max 5MB)')
    )

    image_3 = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control image-upload-input',
            'accept': 'image/jpeg,image/png',
            'data-slot': '3'
        }),
        help_text=_('Upload venue image (JPG/PNG, max 5MB)')
    )

    image_4 = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control image-upload-input',
            'accept': 'image/jpeg,image/png',
            'data-slot': '4'
        }),
        help_text=_('Upload venue image (JPG/PNG, max 5MB)')
    )

    image_5 = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control image-upload-input',
            'accept': 'image/jpeg,image/png',
            'data-slot': '5'
        }),
        help_text=_('Upload venue image (JPG/PNG, max 5MB)')
    )

    def clean_image_1(self):
        return self._validate_image(self.cleaned_data.get('image_1'), 'Image 1')

    def clean_image_2(self):
        return self._validate_image(self.cleaned_data.get('image_2'), 'Image 2')

    def clean_image_3(self):
        return self._validate_image(self.cleaned_data.get('image_3'), 'Image 3')

    def clean_image_4(self):
        return self._validate_image(self.cleaned_data.get('image_4'), 'Image 4')

    def clean_image_5(self):
        return self._validate_image(self.cleaned_data.get('image_5'), 'Image 5')

    def _validate_image(self, image, field_name):
        """Validate individual image field."""
        if image and hasattr(image, 'content_type'):
            try:
                validate_image_extension(image.name, 'venue_gallery')
                validate_image_size(image, max_size_kb=5120)  # 5MB in KB
            except ValidationError as e:
                raise ValidationError(_('{field}: {error}').format(field=field_name, error=str(e)))
        return image

    def get_uploaded_images(self):
        """Return list of uploaded images with their slot numbers."""
        images = []
        if not hasattr(self, 'cleaned_data'):
            return images

        for i in range(1, 6):
            field_name = f'image_{i}'
            image = self.cleaned_data.get(field_name)
            if image:
                images.append((i, image))
        return images


class VenueWithOperatingHoursForm:
    """Combined form for venue creation with operating hours."""

    def __init__(self, data=None, files=None, instance=None, user=None):
        self.user = user
        self.venue_form = VenueForm(data=data, files=files, instance=instance, user=user)
        self.gallery_images_form = VenueGalleryImagesForm(data=data, files=files)

        # Initialize operating hours formset
        if instance and instance.pk:
            # For editing existing venue
            existing_hours = OperatingHours.objects.filter(venue=instance).order_by('day')
            initial_data = []
            for day in range(7):  # 0-6 for Monday-Sunday
                hour = existing_hours.filter(day=day).first()
                if hour:
                    initial_data.append({
                        'day': hour.day,
                        'opening': hour.opening,
                        'closing': hour.closing,
                        'is_closed': hour.is_closed,
                    })
                else:
                    initial_data.append({
                        'day': day,
                        'opening': None,
                        'closing': None,
                        'is_closed': True,
                    })
            self.operating_hours_formset = OperatingHoursFormSetFactory(
                data=data, initial=initial_data, prefix='operating_hours'
            )
        else:
            # For creating new venue - initialize with default closed days
            initial_data = [
                {'day': day, 'opening': None, 'closing': None, 'is_closed': True}
                for day in range(7)
            ]
            self.operating_hours_formset = OperatingHoursFormSetFactory(
                data=data, initial=initial_data, prefix='operating_hours'
            )

    def is_valid(self):
        """Check if venue form, operating hours formset, and gallery images are valid."""
        venue_valid = self.venue_form.is_valid()
        hours_valid = self.operating_hours_formset.is_valid()
        gallery_valid = self.gallery_images_form.is_valid()
        return venue_valid and hours_valid and gallery_valid

    @property
    def errors(self):
        """Combine errors from all forms."""
        errors = {}
        if self.venue_form.errors:
            errors.update(self.venue_form.errors)
        if self.operating_hours_formset.errors:
            errors['operating_hours'] = self.operating_hours_formset.errors
        if self.gallery_images_form.errors:
            errors.update(self.gallery_images_form.errors)
        return errors

    def save(self, commit=True):
        """Save venue, operating hours, and gallery images."""
        venue = self.venue_form.save(commit=commit)

        if commit:
            # Ensure venue has been saved and has a primary key
            if not venue.pk:
                venue.save()

            # Delete existing operating hours (for updates)
            if venue.pk:
                OperatingHours.objects.filter(venue=venue).delete()

            # Save new operating hours (only for non-closed days)
            for form in self.operating_hours_formset:
                if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                    # Only save if the day is not closed
                    if not form.cleaned_data.get('is_closed', True):
                        operating_hour = form.save(commit=False)
                        operating_hour.venue = venue
                        operating_hour.save()

            # Save gallery images from individual slots
            uploaded_images = self.gallery_images_form.get_uploaded_images()
            for order, image in uploaded_images:
                venue_image = VenueImage(
                    venue=venue,
                    image=image,
                    order=order,
                    is_primary=(order == 1),  # First image is primary
                    is_active=True
                )
                venue_image.save()

        return venue
