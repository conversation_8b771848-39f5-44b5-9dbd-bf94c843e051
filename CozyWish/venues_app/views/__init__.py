"""Aggregate imports for venues_app views."""

# --- Common helpers and constants ---
from .common import (
    ServiceProviderRequiredMixin,
    MAX_SERVICES_PER_VENUE,
    MAX_FAQS_PER_VENUE,
)

# --- Search and public views ---
from .search import (
    venue_search,
    location_autocomplete,
    category_venues,
    get_location_data,
    venue_detail,
    service_detail,
    flag_venue,
)

# --- Provider management views ---
from .provider import (
    VenueCreateView,
    VenueDeleteView,
    venue_create,
    venue_edit,
    venue_delete,
    manage_services,
    manage_faqs,
    manage_operating_hours,
    manage_amenities,
    edit_faq,
    delete_faq,
    edit_amenity,
    delete_amenity,
    provider_venues,
    provider_venue_detail,
    change_venue_status,
    ServiceCreateView,
    ServiceUpdateView,
    ServiceDeleteView,
    service_create,
    service_edit,
    service_delete,
    manage_venue_images,
    upload_venue_image,
    set_primary_image,
    reorder_venue_images,
    delete_venue_image,
)

# --- Admin management views ---
from .admin import (
    admin_venue_approval_dashboard,
    admin_venue_list,
    admin_pending_venues,
    admin_venue_detail,
    admin_venue_approval,
    admin_category_list,
    admin_category_create,
    admin_category_edit,
    admin_category_delete,
    admin_category_toggle_status,
    admin_flagged_venues,
)

__all__ = [
    'ServiceProviderRequiredMixin',
    'MAX_SERVICES_PER_VENUE',
    'MAX_FAQS_PER_VENUE',
    'venue_search',
    'location_autocomplete',
    'category_venues',
    'get_location_data',
    'venue_detail',
    'service_detail',
    'flag_venue',
    'VenueCreateView',
    'VenueDeleteView',
    'venue_create',
    'venue_edit',
    'venue_delete',
    'manage_services',
    'manage_faqs',
    'manage_operating_hours',
    'manage_amenities',
    'edit_faq',
    'delete_faq',
    'edit_amenity',
    'delete_amenity',
    'provider_venues',
    'provider_venue_detail',
    'change_venue_status',
    'ServiceCreateView',
    'ServiceUpdateView',
    'ServiceDeleteView',
    'service_create',
    'service_edit',
    'service_delete',
    'manage_venue_images',
    'upload_venue_image',
    'set_primary_image',
    'reorder_venue_images',
    'delete_venue_image',
    'admin_venue_approval_dashboard',
    'admin_venue_list',
    'admin_pending_venues',
    'admin_venue_detail',
    'admin_venue_approval',
    'admin_category_list',
    'admin_category_create',
    'admin_category_edit',
    'admin_category_delete',
    'admin_category_toggle_status',
    'admin_flagged_venues',
]
