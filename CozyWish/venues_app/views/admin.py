# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.paginator import Paginator
from django.db.models import Q
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone

# --- Local App Imports ---
from ..models import Category, FlaggedVenue, Venue


# --- Venue Approval Views ---
@login_required
def admin_venue_approval_dashboard(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('utility_app:home')

    total_venues = Venue.objects.count()
    pending_venues = Venue.objects.filter(approval_status=Venue.PENDING).count()
    approved_venues = Venue.objects.filter(approval_status=Venue.APPROVED).count()
    rejected_venues = Venue.objects.filter(approval_status=Venue.REJECTED).count()

    recent_pending = Venue.objects.filter(
        approval_status=Venue.PENDING
    ).select_related('service_provider').order_by('-created_at')[:10]

    recently_approved = Venue.objects.filter(
        approval_status=Venue.APPROVED
    ).select_related('service_provider').order_by('-approved_at')[:5]

    context = {
        'total_venues': total_venues,
        'pending_venues': pending_venues,
        'approved_venues': approved_venues,
        'rejected_venues': rejected_venues,
        'recent_pending': recent_pending,
        'recently_approved': recently_approved,
    }
    return render(request, 'venues_app/admin/approval_dashboard.html', context)


# --- Venue Listing Views ---
@login_required
def admin_venue_list(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('utility_app:home')

    venues = Venue.objects.select_related('service_provider').order_by('-created_at')
    status_filter = request.GET.get('status')
    if status_filter and status_filter in ['pending', 'approved', 'rejected']:
        venues = venues.filter(approval_status=status_filter)

    search_query = request.GET.get('search')
    if search_query:
        venues = venues.filter(
            Q(venue_name__icontains=search_query)
            | Q(service_provider__email__icontains=search_query)
            | Q(city__icontains=search_query)
            | Q(state__icontains=search_query)
        )

    paginator = Paginator(venues, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'venues': page_obj.object_list,
        'status_filter': status_filter,
        'search_query': search_query,
    }
    return render(request, 'venues_app/admin/venue_list.html', context)


# --- Pending Venue Views ---
@login_required
def admin_pending_venues(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('utility_app:home')

    pending_venues = Venue.objects.filter(
        approval_status=Venue.PENDING
    ).select_related('service_provider').order_by('-created_at')

    paginator = Paginator(pending_venues, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'pending_venues': page_obj.object_list,
    }
    return render(request, 'venues_app/admin/pending_venues.html', context)


# --- Venue Detail & Approval ---
@login_required
def admin_venue_detail(request, venue_id):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('utility_app:home')

    venue = get_object_or_404(Venue.objects.select_related('service_provider'), id=venue_id)

    venue_images = venue.images.all().order_by('order')
    venue_faqs = venue.faqs.all().order_by('order')
    venue_services = venue.services.all().order_by('service_title')
    venue_categories = venue.categories.all()

    context = {
        'venue': venue,
        'venue_images': venue_images,
        'venue_faqs': venue_faqs,
        'venue_services': venue_services,
        'venue_categories': venue_categories,
    }
    return render(request, 'venues_app/admin/venue_detail.html', context)


@login_required
def admin_venue_approval(request, venue_id):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('utility_app:home')

    venue = get_object_or_404(Venue, id=venue_id)
    if venue.approval_status != Venue.PENDING:
        messages.info(request, f'This venue has already been {venue.approval_status}.')
        return redirect('venues_app:admin_venue_detail', venue_id=venue.id)

    if request.method == 'POST':
        action = request.POST.get('action')
        admin_notes = request.POST.get('admin_notes', '').strip()

        if action == 'approve':
            venue.approval_status = Venue.APPROVED
            venue.approved_at = timezone.now()
            venue.rejected_at = None  # Clear rejection timestamp
            venue.admin_notes = admin_notes
            venue.save()
        elif action == 'reject':
            if not admin_notes:
                messages.error(request, 'Please provide a reason for rejection.')
                return redirect('venues_app:admin_venue_approval', venue_id=venue.id)
            venue.approval_status = Venue.REJECTED
            venue.rejected_at = timezone.now()
            venue.approved_at = None  # Clear approval timestamp
            venue.admin_notes = admin_notes
            venue.save()
        messages.success(request, f'Venue "{venue.venue_name}" has been {venue.approval_status} successfully.')
        return redirect('venues_app:admin_pending_venues')

    return render(request, 'venues_app/admin/venue_approval.html', {'venue': venue})


# --- Category Management Views ---
@login_required
def admin_category_list(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('utility_app:home')

    categories = Category.objects.all().order_by('category_name')
    return render(request, 'venues_app/admin/category_list.html', {'categories': categories})


@login_required
def admin_category_create(request):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('utility_app:home')

    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        is_active = request.POST.get('is_active') == 'on'

        if not name:
            messages.error(request, 'Category name is required.')
        elif Category.objects.filter(category_name__iexact=name).exists():
            messages.error(request, 'A category with this name already exists.')
        else:
            category = Category.objects.create(category_name=name, category_description=description, is_active=is_active)
            messages.success(request, f'Category "{category.category_name}" has been created successfully.')
            return redirect('venues_app:admin_category_list')

    return render(request, 'venues_app/admin/category_form.html', {'action': 'Create'})


@login_required
def admin_category_edit(request, category_id):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('utility_app:home')

    category = get_object_or_404(Category, id=category_id)

    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        is_active = request.POST.get('is_active') == 'on'

        if not name:
            messages.error(request, 'Category name is required.')
        elif Category.objects.filter(category_name__iexact=name).exclude(id=category.id).exists():
            messages.error(request, 'A category with this name already exists.')
        else:
            category.category_name = name
            category.category_description = description
            category.is_active = is_active
            category.save()
            messages.success(request, f'Category "{category.category_name}" has been updated successfully.')
            return redirect('venues_app:admin_category_list')

    return render(request, 'venues_app/admin/category_form.html', {'category': category, 'action': 'Edit'})


@login_required
def admin_category_delete(request, category_id):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('utility_app:home')

    category = get_object_or_404(Category, id=category_id)
    venue_count = category.venues.count()

    if request.method == 'POST':
        if venue_count > 0:
            messages.error(request, f'Cannot delete category "{category.name}" because it has {venue_count} venues assigned to it.')
        else:
            category_name = category.name
            category.delete()
            messages.success(request, f'Category "{category_name}" has been deleted successfully.')
        return redirect('venues_app:admin_category_list')

    return render(request, 'venues_app/admin/category_delete.html', {'category': category, 'venue_count': venue_count})


@login_required
def admin_category_toggle_status(request, category_id):
    if not request.user.is_staff:
        messages.error(request, 'Only administrators can access this page.')
        return redirect('utility_app:home')

    category = get_object_or_404(Category, id=category_id)
    category.is_active = not category.is_active
    category.save()

    status = 'activated' if category.is_active else 'deactivated'
    messages.success(request, f'Category "{category.name}" has been {status}.')
    return redirect('venues_app:admin_category_list')


# --- Flagged Venues ---
@login_required
@user_passes_test(lambda u: u.is_staff)
def admin_flagged_venues(request):
    flagged_venues = FlaggedVenue.objects.select_related('venue', 'flagged_by', 'reviewed_by').order_by('-created_at')
    return render(request, 'venues_app/admin/flagged_venues.html', {'flagged_venues': flagged_venues})

