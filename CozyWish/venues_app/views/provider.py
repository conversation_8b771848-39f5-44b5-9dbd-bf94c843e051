"""Views for service provider venue and service management."""

# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.paginator import Paginator
from django.db.models import Avg, Max
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.views.generic import CreateView, DeleteView, UpdateView
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.core.exceptions import ValidationError
import json

# --- Local App Imports ---
from booking_cart_app.models import Booking
from ..forms import (
    VenueFAQForm, ServiceForm, VenueForm, OperatingHoursForm,
    OperatingHoursFormSetFactory, VenueAmenityForm
)
from ..forms.venue import VenueImageForm, VenueWithOperatingHoursForm, VenueCreateForm
from ..models import Service, Venue, VenueImage, OperatingHours, VenueAmenity, VenueFAQ
from .common import MAX_FAQS_PER_VENUE, MAX_SERVICES_PER_VENUE, ServiceProviderRequiredMixin


@login_required
def venue_create_view(request):
    """Allow service providers to create a new venue with simplified form."""
    # Ensure user has service provider profile
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'You must have a service provider profile to create a venue.')
        return redirect('accounts_app:service_provider_profile')

    # Check if user already has a venue
    existing_venue = Venue.objects.filter(
        service_provider=request.user.service_provider_profile,
        is_deleted=False
    ).first()

    if existing_venue:
        messages.warning(request, 'You already have a venue. You can only have one venue per account. You can edit your existing venue below.')
        return redirect('venues_app:venue_edit')

    if request.method == 'POST':
        form = VenueCreateForm(
            data=request.POST,
            user=request.user
        )

        if form.is_valid():
            try:
                # Double-check no venue exists before saving
                existing_venue = Venue.objects.filter(
                    service_provider=request.user.service_provider_profile,
                    is_deleted=False
                ).first()

                if existing_venue:
                    messages.error(request, 'You already have a venue. Cannot create multiple venues.')
                    return redirect('venues_app:venue_edit')

                # Save the venue
                venue = form.save()

                # Set appropriate success message based on venue status
                venue_status = form.cleaned_data.get('venue_status', 'draft')
                if venue_status == 'draft':
                    messages.success(
                        request,
                        f'Your venue "{venue.venue_name}" has been saved as a draft. '
                        'You can edit it anytime and submit for approval when ready.'
                    )
                else:
                    messages.success(
                        request,
                        f'Your venue "{venue.venue_name}" has been submitted for admin approval. '
                        'You will be notified once it is reviewed.'
                    )

                # Redirect to provider dashboard instead of venue detail
                return redirect('dashboard_app:provider_dashboard')

            except Exception as e:
                # Log the error for debugging
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f'Error creating venue for user {request.user.id}: {str(e)}', exc_info=True)

                messages.error(
                    request,
                    'There was an error creating your venue. Please check all required fields and try again. '
                    'If the problem persists, please contact support.'
                )
        else:
            # Form is not valid - show specific error messages
            error_messages = []
            for field, errors in form.errors.items():
                if field == '__all__':
                    error_messages.extend(errors)
                else:
                    field_label = form.fields[field].label or field.replace('_', ' ').title()
                    for error in errors:
                        error_messages.append(f"{field_label}: {error}")

            if error_messages:
                messages.error(
                    request,
                    'Please correct the following errors: ' + '; '.join(error_messages)
                )
            else:
                messages.error(
                    request,
                    'Please correct the errors below. All required fields must be filled out correctly.'
                )
    else:
        form = VenueCreateForm(user=request.user)

    context = {
        'form': form,
    }
    return render(request, 'venues_app/venue_create.html', context)


class VenueCreateView(LoginRequiredMixin, ServiceProviderRequiredMixin, CreateView):
    """Allow service providers to create a new venue."""
    model = Venue
    form_class = VenueForm
    template_name = 'venues_app/venue_create.html'
    def get_success_url(self):
        return reverse_lazy('venues_app:provider_venue_detail', kwargs={'venue_id': self.object.id})

    def dispatch(self, request, *args, **kwargs):
        # Ensure user has service provider profile
        if not hasattr(request.user, 'service_provider_profile'):
            messages.error(request, 'You must have a service provider profile to create a venue.')
            return redirect('accounts_app:service_provider_profile')

        # Check if user already has a venue (more robust check)
        existing_venue = Venue.objects.filter(
            service_provider=request.user.service_provider_profile,
            is_deleted=False
        ).first()

        if existing_venue:
            messages.warning(request, 'You already have a venue. You can only have one venue per account. You can edit your existing venue below.')
            return redirect('venues_app:venue_edit')

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        try:
            # Double-check no venue exists before saving
            existing_venue = Venue.objects.filter(
                service_provider=self.request.user.service_provider_profile,
                is_deleted=False
            ).first()

            if existing_venue:
                messages.error(self.request, 'You already have a venue. Cannot create multiple venues.')
                return redirect('venues_app:venue_edit')

            # Save the venue
            response = super().form_valid(form)
            venue = self.object

            # Ensure venue is created with pending status
            if venue.approval_status != Venue.PENDING:
                venue.approval_status = Venue.PENDING
                venue.save(update_fields=['approval_status'])

            messages.success(
                self.request,
                f'Your venue "{venue.venue_name}" has been created successfully and is pending admin approval. '
                'You will be notified once it is reviewed.'
            )
            return response

        except Exception as e:
            messages.error(self.request, 'There was an error creating your venue. Please try again.')
            return self.form_invalid(form)

    def form_invalid(self, form):
        messages.error(
            self.request,
            'Please correct the errors below and try again. All required fields must be filled out.'
        )
        return super().form_invalid(form)


@login_required
def venue_edit_view(request):
    """Allow service providers to edit their venue information (excluding operating hours)."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit venues.')
        return redirect('utility_app:home')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = VenueForm(
            data=request.POST,
            files=request.FILES,
            instance=venue,
            user=request.user
        )

        if form.is_valid():
            try:
                # Save venue form
                venue = form.save(commit=False)
                venue.service_provider = request.user.service_provider_profile
                venue.save()
                form.save_m2m()  # Save many-to-many relationships (categories)

                messages.success(request, 'Your venue has been updated successfully.')
                return redirect('venues_app:provider_venue_detail', venue_id=venue.id)
            except Exception as e:
                messages.error(request, f'Error updating venue: {str(e)}')
        else:
            # Form is not valid - show specific error messages
            error_messages = []
            for field, errors in form.errors.items():
                if field == '__all__':
                    error_messages.extend(errors)
                else:
                    field_label = form.fields[field].label or field.replace('_', ' ').title()
                    for error in errors:
                        error_messages.append(f"{field_label}: {error}")

            if error_messages:
                messages.error(
                    request,
                    'Please correct the following errors: ' + '; '.join(error_messages)
                )
            else:
                messages.error(request, 'There were errors in your venue update. Please check the form below.')
    else:
        form = VenueForm(instance=venue, user=request.user)

    # Get venue images for display
    venue_images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')

    context = {
        'form': form,
        'venue': venue,
        'venue_images': venue_images,
        'action': 'Edit',
    }
    return render(request, 'venues_app/venue_edit.html', context)


class VenueDeleteView(LoginRequiredMixin, ServiceProviderRequiredMixin, DeleteView):
    """Allow service providers to delete their venue."""
    model = Venue
    template_name = 'venues_app/venue_delete.html'
    success_url = reverse_lazy('venues_app:venue_create')

    def get_object(self, queryset=None):
        try:
            return self.request.user.service_provider_profile.venue
        except Venue.DoesNotExist:
            messages.error(self.request, 'You do not have a venue to delete.')
            raise

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Your venue has been deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Function-based aliases
venue_create = venue_create_view
venue_edit = venue_edit_view
venue_delete = VenueDeleteView.as_view()


@login_required
def manage_services(request):
    """Allow service providers to manage their venue services."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can manage services.')
        return redirect('utility_app:home')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before managing services.')
        return redirect('venues_app:venue_create')

    services = venue.services.all().order_by('service_title')

    context = {
        'venue': venue,
        'services': services,
        'max_services': MAX_SERVICES_PER_VENUE,
        'can_add_service': services.count() < MAX_SERVICES_PER_VENUE,
    }
    return render(request, 'venues_app/manage_services.html', context)


@login_required
def manage_faqs(request):
    """Allow service providers to manage their venue FAQs."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can manage FAQs.')
        return redirect('utility_app:home')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before adding FAQs.')
        return redirect('venues_app:venue_create')

    faqs = venue.faqs.all().order_by('order')
    can_add_faq = faqs.count() < MAX_FAQS_PER_VENUE

    if request.method == 'POST' and can_add_faq:
        form = VenueFAQForm(request.POST)
        if form.is_valid():
            faq = form.save(commit=False)
            faq.venue = venue

            # Auto-assign the next available order number
            existing_orders = set(venue.faqs.values_list('order', flat=True))
            for order in range(1, MAX_FAQS_PER_VENUE + 1):
                if order not in existing_orders:
                    faq.order = order
                    break

            faq.save()
            messages.success(request, 'FAQ added successfully.')
            return redirect('venues_app:manage_faqs')
    else:
        form = VenueFAQForm()

    context = {
        'venue': venue,
        'faqs': faqs,
        'form': form,
        'max_faqs': MAX_FAQS_PER_VENUE,
        'can_add_faq': can_add_faq,
    }
    return render(request, 'venues_app/manage_faqs.html', context)


@login_required
def manage_operating_hours(request):
    """Allow service providers to manage their venue operating hours."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can manage operating hours.')
        return redirect('utility_app:home')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before setting operating hours.')
        return redirect('venues_app:venue_create')

    # Get existing operating hours
    existing_hours = {oh.day: oh for oh in venue.operating_hours_set.all()}

    if request.method == 'POST':
        # Create initial data for all 7 days
        initial_data = []
        for day in range(7):  # 0=Monday to 6=Sunday
            if day in existing_hours:
                oh = existing_hours[day]
                initial_data.append({
                    'day': oh.day,
                    'opening': oh.opening,
                    'closing': oh.closing,
                    'is_closed': oh.is_closed,
                })
            else:
                initial_data.append({
                    'day': day,
                    'opening': None,
                    'closing': None,
                    'is_closed': True,
                })

        formset = OperatingHoursFormSetFactory(request.POST, initial=initial_data)
        if formset.is_valid():
            # Delete existing hours and create new ones
            venue.operating_hours_set.all().delete()

            for form in formset:
                if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                    operating_hour = form.save(commit=False)
                    operating_hour.venue = venue
                    operating_hour.save()

            messages.success(request, 'Operating hours updated successfully.')
            return redirect('venues_app:provider_venue_detail', venue_id=venue.id)
    else:
        # Create initial data for all 7 days
        initial_data = []
        for day in range(7):  # 0=Monday to 6=Sunday
            if day in existing_hours:
                oh = existing_hours[day]
                initial_data.append({
                    'day': oh.day,
                    'opening': oh.opening,
                    'closing': oh.closing,
                    'is_closed': oh.is_closed,
                })
            else:
                initial_data.append({
                    'day': day,
                    'opening': None,
                    'closing': None,
                    'is_closed': True,
                })

        formset = OperatingHoursFormSetFactory(initial=initial_data)

    # Create day names for template
    day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    formset_with_days = list(zip(formset, day_names))

    context = {
        'venue': venue,
        'formset': formset,
        'formset_with_days': formset_with_days,
    }
    return render(request, 'venues_app/manage_operating_hours.html', context)


@login_required
def manage_amenities(request):
    """Allow service providers to manage their venue amenities."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can manage amenities.')
        return redirect('utility_app:home')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before adding amenities.')
        return redirect('venues_app:venue_create')

    amenities = venue.amenities.all().order_by('amenity_type')
    can_add_amenity = amenities.count() < 15

    if request.method == 'POST' and can_add_amenity:
        form = VenueAmenityForm(request.POST)
        if form.is_valid():
            amenity = form.save(commit=False)
            amenity.venue = venue
            amenity.save()
            messages.success(request, 'Amenity added successfully.')
            return redirect('venues_app:manage_amenities')
    else:
        form = VenueAmenityForm()

    context = {
        'venue': venue,
        'amenities': amenities,
        'form': form,
        'can_add_amenity': can_add_amenity,
        'max_amenities': 15,
    }
    return render(request, 'venues_app/manage_amenities.html', context)


@login_required
def edit_faq(request, faq_id):
    """Allow service providers to edit their venue FAQs."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit FAQs.')
        return redirect('utility_app:home')

    try:
        venue = request.user.service_provider_profile.venue
        faq = get_object_or_404(VenueFAQ, id=faq_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = VenueFAQForm(request.POST, instance=faq)
        if form.is_valid():
            form.save()
            messages.success(request, 'FAQ updated successfully.')
            return redirect('venues_app:manage_faqs')
    else:
        form = VenueFAQForm(instance=faq)

    context = {
        'venue': venue,
        'faq': faq,
        'form': form,
        'action': 'Edit',
    }
    return render(request, 'venues_app/faq_form.html', context)


@login_required
def delete_faq(request, faq_id):
    """Allow service providers to delete their venue FAQs."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can delete FAQs.')
        return redirect('utility_app:home')

    try:
        venue = request.user.service_provider_profile.venue
        faq = get_object_or_404(VenueFAQ, id=faq_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        faq.delete()
        messages.success(request, 'FAQ deleted successfully.')
        return redirect('venues_app:manage_faqs')

    context = {
        'venue': venue,
        'faq': faq,
    }
    return render(request, 'venues_app/faq_delete.html', context)


@login_required
def edit_amenity(request, amenity_id):
    """Allow service providers to edit their venue amenities."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit amenities.')
        return redirect('utility_app:home')

    try:
        venue = request.user.service_provider_profile.venue
        amenity = get_object_or_404(VenueAmenity, id=amenity_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = VenueAmenityForm(request.POST, instance=amenity)
        if form.is_valid():
            form.save()
            messages.success(request, 'Amenity updated successfully.')
            return redirect('venues_app:manage_amenities')
    else:
        form = VenueAmenityForm(instance=amenity)

    context = {
        'venue': venue,
        'amenity': amenity,
        'form': form,
        'action': 'Edit',
    }
    return render(request, 'venues_app/amenity_form.html', context)


@login_required
def delete_amenity(request, amenity_id):
    """Allow service providers to delete their venue amenities."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can delete amenities.')
        return redirect('utility_app:home')

    try:
        venue = request.user.service_provider_profile.venue
        amenity = get_object_or_404(VenueAmenity, id=amenity_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        amenity.delete()
        messages.success(request, 'Amenity deleted successfully.')
        return redirect('venues_app:manage_amenities')

    context = {
        'venue': venue,
        'amenity': amenity,
    }
    return render(request, 'venues_app/amenity_delete.html', context)


@login_required
def provider_venues(request):
    """Display the provider's venue management dashboard (single venue per provider)."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('utility_app:home')

    # Get the provider's venue (should be only one)
    venue = Venue.objects.filter(
        service_provider=request.user.service_provider_profile,
        is_deleted=False
    ).prefetch_related('images', 'services', 'reviews').first()

    # If venue exists, redirect to venue detail page for management
    if venue:
        return redirect('venues_app:provider_venue_detail', venue_id=venue.id)

    # If no venue exists, show the venues list page with create option
    total_services = 0
    total_bookings = 0
    avg_rating = 0

    context = {
        'venues': [],
        'page_obj': None,
        'total_services': total_services,
        'total_bookings': total_bookings,
        'avg_rating': avg_rating,
    }
    return render(request, 'venues_app/provider/venues_list.html', context)


@login_required
def provider_venue_detail(request, venue_id):
    """Display detailed information about a specific venue for the provider."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('utility_app:home')

    venue = get_object_or_404(
        Venue.objects.select_related('service_provider', 'service_provider__user')
        .prefetch_related('services', 'faqs', 'images', 'categories', 'amenities'),
        id=venue_id,
        service_provider=request.user.service_provider_profile
    )

    services = venue.services.all().order_by('service_title')
    opening_hours = venue.operating_hours_set.all().order_by('day')
    faqs = venue.faqs.all().order_by('order')
    images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')
    primary_image = images.filter(is_primary=True).first()
    gallery_images = images.filter(is_primary=False)
    amenities = venue.amenities.filter(is_active=True).order_by('amenity_type')

    # Calculate price range from services
    price_range = None
    if services.exists():
        prices = [service.price_min for service in services if service.price_min]
        if prices:
            min_price = min(prices)
            max_prices = [service.price_max or service.price_min for service in services if service.price_min]
            max_price = max(max_prices) if max_prices else min_price
            price_range = f"${min_price}" if min_price == max_price else f"${min_price} - ${max_price}"

    # Check if can add more FAQs
    can_add_faq = faqs.count() < MAX_FAQS_PER_VENUE

    # Check if venue is ready for approval (has minimum required content)
    is_ready_for_approval = (
        venue.venue_name and
        venue.short_description and
        services.exists() and
        images.exists()
    )

    context = {
        'venue': venue,
        'services': services,
        'opening_hours': opening_hours,
        'faqs': faqs,
        'images': images,
        'primary_image': primary_image,
        'gallery_images': gallery_images,
        'amenities': amenities,
        'price_range': price_range,
        'can_add_faq': can_add_faq,
        'max_faqs': MAX_FAQS_PER_VENUE,
        'is_ready_for_approval': is_ready_for_approval,
    }
    return render(request, 'venues_app/provider/venue_detail.html', context)


@login_required
def change_venue_status(request, venue_id):
    """Allow service providers to change their venue status (draft/pending approval)."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can change venue status.')
        return redirect('utility_app:home')

    venue = get_object_or_404(
        Venue,
        id=venue_id,
        service_provider=request.user.service_provider_profile
    )

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'submit_for_approval':
            # Check if venue has minimum required content
            services = venue.services.all()
            images = venue.images.filter(is_active=True)

            if not (venue.venue_name and venue.short_description and services.exists() and images.exists()):
                messages.error(
                    request,
                    'Your venue needs at least a name, description, one service, and one image before submitting for approval.'
                )
                return redirect('venues_app:provider_venue_detail', venue_id=venue.id)

            venue.approval_status = Venue.PENDING
            venue.save(update_fields=['approval_status'])
            messages.success(
                request,
                'Your venue has been submitted for admin approval. You will be notified once it is reviewed.'
            )

        elif action == 'save_as_draft':
            venue.approval_status = Venue.DRAFT
            venue.save(update_fields=['approval_status'])
            messages.success(
                request,
                'Your venue has been saved as a draft. You can continue editing and submit for approval when ready.'
            )
        else:
            messages.error(request, 'Invalid action.')

    return redirect('venues_app:provider_venue_detail', venue_id=venue.id)


class ServiceCreateView(LoginRequiredMixin, ServiceProviderRequiredMixin, CreateView):
    """Allow service providers to create a new service for their venue."""
    model = Service
    form_class = ServiceForm
    template_name = 'venues_app/service_create.html'

    def dispatch(self, request, *args, **kwargs):
        try:
            self.venue = request.user.service_provider_profile.venue
        except Venue.DoesNotExist:
            messages.error(request, 'You need to create a venue first before adding services.')
            return redirect('venues_app:venue_create')
        if self.venue.services.count() >= MAX_SERVICES_PER_VENUE:
            messages.error(request, f'You have reached the maximum limit of {MAX_SERVICES_PER_VENUE} services per venue.')
            return redirect('venues_app:manage_services')
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['venue'] = self.venue
        context['action'] = 'Add'
        return context

    def form_valid(self, form):
        form.instance.venue = self.venue
        response = super().form_valid(form)
        messages.success(self.request, f'Service "{form.instance.service_title}" has been created successfully.')
        return response

    def form_invalid(self, form):
        messages.error(self.request, 'There were errors in your service submission. Please check the form below.')
        return super().form_invalid(form)

    def get_success_url(self):
        return reverse_lazy('venues_app:manage_services')


class ServiceUpdateView(LoginRequiredMixin, ServiceProviderRequiredMixin, UpdateView):
    """Allow service providers to edit their venue services."""
    model = Service
    form_class = ServiceForm
    template_name = 'venues_app/service_edit.html'

    def get_object(self, queryset=None):
        try:
            venue = self.request.user.service_provider_profile.venue
            return get_object_or_404(Service, pk=self.kwargs['pk'], venue=venue)
        except Venue.DoesNotExist:
            messages.error(self.request, 'You need to create a venue first.')
            return redirect('venues_app:venue_create')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['venue'] = self.object.venue
        context['action'] = 'Edit'
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Service "{form.instance.service_title}" has been updated successfully.')
        return response

    def form_invalid(self, form):
        messages.error(self.request, 'There were errors in your service update. Please check the form below.')
        return super().form_invalid(form)

    def get_success_url(self):
        return reverse_lazy('venues_app:manage_services')


class ServiceDeleteView(LoginRequiredMixin, ServiceProviderRequiredMixin, DeleteView):
    """Allow service providers to delete their venue services."""
    model = Service
    template_name = 'venues_app/service_delete.html'

    def get_object(self, queryset=None):
        try:
            venue = self.request.user.service_provider_profile.venue
            return get_object_or_404(Service, pk=self.kwargs['pk'], venue=venue)
        except Venue.DoesNotExist:
            messages.error(self.request, 'You need to create a venue first.')
            return redirect('venues_app:venue_create')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['venue'] = self.object.venue
        return context

    def delete(self, request, *args, **kwargs):
        service = self.get_object()
        service_title = service.service_title
        response = super().delete(request, *args, **kwargs)
        messages.success(request, f'Service "{service_title}" has been deleted successfully.')
        return response

    def get_success_url(self):
        return reverse_lazy('venues_app:manage_services')


service_create = ServiceCreateView.as_view()
service_edit = ServiceUpdateView.as_view()
service_delete = ServiceDeleteView.as_view()


# --- Image Management Views ---

@login_required
def manage_venue_images(request):
    """Allow service providers to manage their venue images."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can manage venue images.')
        return redirect('utility_app:home')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before managing images.')
        return redirect('venues_app:venue_create')

    images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')
    can_add_image = images.count() < 5

    if request.method == 'POST' and can_add_image:
        form = VenueImageForm(request.POST, request.FILES)
        if form.is_valid():
            image = form.save(commit=False)
            image.venue = venue

            # Auto-assign order if not provided
            if not image.order:
                max_order = images.aggregate(max_order=Max('order'))['max_order'] or 0
                image.order = max_order + 1

            # If this is the first image, make it primary
            if not images.exists():
                image.is_primary = True

            try:
                image.save()
                messages.success(request, 'Image uploaded successfully.')
                return redirect('venues_app:manage_venue_images')
            except ValidationError as e:
                form.add_error(None, e)
            except Exception as e:
                # Log the error for debugging
                import logging
                logger = logging.getLogger('venues_app')
                logger.error(f"Failed to upload venue image: {str(e)}", exc_info=True)
                form.add_error(None, f"Failed to upload image: {str(e)}")
    else:
        form = VenueImageForm()

    context = {
        'venue': venue,
        'images': images,
        'form': form,
        'can_add_image': can_add_image,
        'max_images': 7,
    }
    return render(request, 'venues_app/provider/manage_images.html', context)


@login_required
@require_POST
def upload_venue_image(request):
    """AJAX endpoint for uploading venue images."""
    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can upload images.'})

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'You need to create a venue first.'})

    # Check image limit
    current_count = venue.images.filter(is_active=True).count()
    if current_count >= 5:
        return JsonResponse({'success': False, 'error': 'Maximum 5 images allowed per venue.'})

    form = VenueImageForm(request.POST, request.FILES)
    if form.is_valid():
        image = form.save(commit=False)
        image.venue = venue

        # Auto-assign order
        max_order = venue.images.aggregate(max_order=Max('order'))['max_order'] or 0
        image.order = max_order + 1

        # If this is the first image, make it primary
        if current_count == 0:
            image.is_primary = True

        try:
            image.save()
            return JsonResponse({
                'success': True,
                'image_id': image.id,
                'image_url': image.image.url,
                'caption': image.caption,
                'order': image.order,
                'is_primary': image.is_primary,
            })
        except ValidationError as e:
            return JsonResponse({'success': False, 'error': str(e)})
        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger('venues_app')
            logger.error(f"Failed to upload venue image via AJAX: {str(e)}", exc_info=True)
            return JsonResponse({'success': False, 'error': f'Upload failed: {str(e)}'})
    else:
        errors = []
        for field, field_errors in form.errors.items():
            for error in field_errors:
                errors.append(f"{field}: {error}")
        return JsonResponse({'success': False, 'error': '; '.join(errors)})


@login_required
@require_POST
def set_primary_image(request, image_id):
    """Set a venue image as primary."""
    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can manage images.'})

    try:
        venue = request.user.service_provider_profile.venue
        image = get_object_or_404(VenueImage, id=image_id, venue=venue, is_active=True)

        # Unset current primary image
        VenueImage.objects.filter(venue=venue, is_primary=True).update(is_primary=False)

        # Set new primary image
        image.is_primary = True
        image.save(update_fields=['is_primary'])

        return JsonResponse({'success': True, 'message': 'Primary image updated successfully.'})

    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'You need to create a venue first.'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_POST
def reorder_venue_images(request):
    """Reorder venue images via AJAX."""
    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can manage images.'})

    try:
        venue = request.user.service_provider_profile.venue
        data = json.loads(request.body)
        image_orders = data.get('image_orders', [])

        # Use a transaction to handle the reordering atomically
        from django.db import transaction
        with transaction.atomic():
            # Create a mapping of image_id to new_order
            order_map = {}
            for item in image_orders:
                image_id = item.get('id')
                new_order = item.get('order')
                if image_id and new_order:
                    order_map[image_id] = new_order

            # Get all images that need to be reordered
            images = list(VenueImage.objects.filter(
                venue=venue,
                is_active=True,
                id__in=order_map.keys()
            ))

            # Simple approach: delete and recreate with new orders
            # Store image data
            image_data = []
            for image in images:
                if image.id in order_map:
                    image_data.append({
                        'id': image.id,
                        'venue': image.venue,
                        'image': image.image,
                        'caption': image.caption,
                        'is_primary': image.is_primary,
                        'is_active': image.is_active,
                        'created_at': image.created_at,
                        'new_order': order_map[image.id]
                    })

            # Update each image individually to avoid constraint conflicts
            for data in image_data:
                VenueImage.objects.filter(id=data['id']).update(order=data['new_order'])

        return JsonResponse({'success': True, 'message': 'Images reordered successfully.'})

    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'You need to create a venue first.'})
    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'Invalid JSON data.'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_POST
def delete_venue_image(request, image_id):
    """Delete a venue image."""
    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can manage images.'})

    try:
        venue = request.user.service_provider_profile.venue
        image = get_object_or_404(VenueImage, id=image_id, venue=venue, is_active=True)

        was_primary = image.is_primary
        image.delete()  # This will handle file deletion and primary reassignment

        message = 'Image deleted successfully.'
        if was_primary:
            message += ' A new primary image has been automatically selected.'

        return JsonResponse({'success': True, 'message': message})

    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'You need to create a venue first.'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

