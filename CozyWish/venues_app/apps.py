"""Application configuration for ``venues_app``."""

# --- Django Imports ---
from django.apps import AppConfig


class VenuesAppConfig(AppConfig):
    """
    Configuration for the venues_app Django application.
    Handles venue management, services, categories, and venue flagging functionality.
    """

    default_auto_field = 'django.db.models.BigAutoField'
    name = 'venues_app'
    verbose_name = 'Venues Management'

    def ready(self):
        """Import signals when the app is ready."""
        import venues_app.signals
