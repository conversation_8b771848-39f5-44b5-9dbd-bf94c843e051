# --- Standard Library Imports ---
from datetime import timedelta
from decimal import Decimal

# --- Django Imports ---
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django.contrib.postgres.search import SearchVectorField

# --- Local App Imports ---
from accounts_app.models import CustomUser, ServiceProviderProfile
from utils.image_utils import generate_image_path


def get_venue_main_image_path(instance, filename):
    """Generate upload path for venue main/featured images."""
    return generate_image_path(
        entity_type='venues',
        entity_id=instance.id if instance.id else 'temp',
        image_type='venue_featured',
        filename=filename
    )


def get_venue_gallery_image_path(instance, filename):
    """Generate upload path for venue gallery images."""
    return generate_image_path(
        entity_type='venues',
        entity_id=instance.venue.id if instance.venue_id else 'temp',
        image_type='venue_gallery',
        filename=filename
    )



# --- Category Model ---

class Category(models.Model):
    """Classification categories for venues (Spa, Massage, Salon, etc.)."""
    # Status Constants
    ACTIVE = True
    INACTIVE = False

    # Field Definitions
    category_name = models.CharField(
        _('category name'),
        max_length=100,
        help_text=_('Category name (e.g., Spa, Massage, Salon)')
    )
    slug = models.SlugField(
        _('slug'),
        max_length=120,
        unique=True,
        blank=True,
        null=True,
        help_text=_('URL-friendly slug (auto-generated)')
    )
    category_description = models.TextField(
        _('description'),
        blank=True,
        help_text=_('Optional description of the category')
    )
    is_active = models.BooleanField(
        _('active status'),
        default=ACTIVE,
        help_text=_('Whether this category is active and visible')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Category')
        verbose_name_plural = _('Categories')
        ordering = ['category_name']
        indexes = [
            models.Index(fields=['category_name']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.category_name

    def save(self, *args, **kwargs):
        """Automatically generate slug on every save."""
        # Always generate slug from category_name
        self.slug = slugify(self.category_name)

        # Ensure slug uniqueness
        if self.slug:
            base_slug = self.slug
            counter = 1
            while Category.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
                self.slug = f"{base_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        """URL for category-specific venue listings."""
        return reverse('venues_app:category_venues', kwargs={'category_slug': self.slug})

    # Backward compatibility properties
    @property
    def name(self):
        return self.category_name

    @property
    def description(self):
        return self.category_description



# --- Venue Model ---

class Venue(models.Model):
    """Represents a service provider's business location."""
    # Approval Status Choices
    DRAFT = 'draft'
    PENDING = 'pending'
    APPROVED = 'approved'
    REJECTED = 'rejected'

    APPROVAL_STATUS_CHOICES = (
        (DRAFT, _('Draft')),
        (PENDING, _('Pending')),
        (APPROVED, _('Approved')),
        (REJECTED, _('Rejected')),
    )

    # Visibility Status Choices
    ACTIVE = 'active'
    INACTIVE = 'inactive'

    VISIBILITY_CHOICES = (
        (ACTIVE, _('Active')),
        (INACTIVE, _('Inactive')),
    )

    # State Choices for Consistency
    STATE_CHOICES = (
        ('AL', 'Alabama'),
        ('AK', 'Alaska'),
        ('AZ', 'Arizona'),
        ('AR', 'Arkansas'),
        ('CA', 'California'),
        ('CO', 'Colorado'),
        ('CT', 'Connecticut'),
        ('DE', 'Delaware'),
        ('FL', 'Florida'),
        ('GA', 'Georgia'),
        ('HI', 'Hawaii'),
        ('ID', 'Idaho'),
        ('IL', 'Illinois'),
        ('IN', 'Indiana'),
        ('IA', 'Iowa'),
        ('KS', 'Kansas'),
        ('KY', 'Kentucky'),
        ('LA', 'Louisiana'),
        ('ME', 'Maine'),
        ('MD', 'Maryland'),
        ('MA', 'Massachusetts'),
        ('MI', 'Michigan'),
        ('MN', 'Minnesota'),
        ('MS', 'Mississippi'),
        ('MO', 'Missouri'),
        ('MT', 'Montana'),
        ('NE', 'Nebraska'),
        ('NV', 'Nevada'),
        ('NH', 'New Hampshire'),
        ('NJ', 'New Jersey'),
        ('NM', 'New Mexico'),
        ('NY', 'New York'),
        ('NC', 'North Carolina'),
        ('ND', 'North Dakota'),
        ('OH', 'Ohio'),
        ('OK', 'Oklahoma'),
        ('OR', 'Oregon'),
        ('PA', 'Pennsylvania'),
        ('RI', 'Rhode Island'),
        ('SC', 'South Carolina'),
        ('SD', 'South Dakota'),
        ('TN', 'Tennessee'),
        ('TX', 'Texas'),
        ('UT', 'Utah'),
        ('VT', 'Vermont'),
        ('VA', 'Virginia'),
        ('WA', 'Washington'),
        ('WV', 'West Virginia'),
        ('WI', 'Wisconsin'),
        ('WY', 'Wyoming'),
    )

    # Core Relationships
    service_provider = models.OneToOneField(
        ServiceProviderProfile,
        on_delete=models.CASCADE,
        related_name='venue',
        verbose_name=_('service provider'),
        help_text=_('Service provider who owns this venue')
    )

    # Basic Information
    venue_name = models.CharField(
        _('venue name'),
        max_length=255,
        blank=False,
        null=False,
        help_text=_('Name of the venue/business')
    )
    slug = models.SlugField(
        _('slug'),
        max_length=255,
        unique=True,
        blank=True,
        null=True,
        help_text=_('URL-friendly slug (auto-generated)')
    )
    short_description = models.TextField(
        _('description'),
        max_length=500,
        blank=False,
        null=False,
        help_text=_('Brief description of the venue (max 500 characters)')
    )

    # Location Details
    state = models.CharField(
        _('state'),
        max_length=2,
        choices=STATE_CHOICES,
        blank=False,
        null=False,
        help_text=_('State where venue is located')
    )
    county = models.CharField(
        _('county'),
        max_length=100,
        blank=False,
        null=False,
        help_text=_('County where the venue is located')
    )
    city = models.CharField(
        _('city'),
        max_length=100,
        blank=False,
        null=False,
        help_text=_('City where the venue is located')
    )
    street_number = models.CharField(
        _('street number'),
        max_length=20,
        blank=False,
        null=False,
        help_text=_('Street number')
    )
    street_name = models.CharField(
        _('street name'),
        max_length=255,
        blank=False,
        null=False,
        help_text=_('Street name')
    )

    # Geographic Coordinates
    latitude = models.DecimalField(
        _('latitude'),
        max_digits=10,
        decimal_places=8,
        null=True,
        blank=True,
        help_text=_('Latitude coordinate for mapping')
    )
    longitude = models.DecimalField(
        _('longitude'),
        max_digits=11,
        decimal_places=8,
        null=True,
        blank=True,
        help_text=_('Longitude coordinate for mapping')
    )

    # Relationships
    us_city = models.ForeignKey(
        'USCity',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='venues',
        verbose_name=_('US city'),
        help_text=_('Standardized location data'),
        db_index=True  # Add index for faster lookups
    )
    categories = models.ManyToManyField(
        Category,
        through='VenueCategory',
        related_name='venues',
        blank=True,
        verbose_name=_('categories'),
        help_text=_('Categories this venue belongs to')
    )

    # Media
    main_image = models.ImageField(
        _('main image'),
        upload_to=get_venue_main_image_path,
        blank=True,
        null=True,
        help_text=_('Main featured image for the venue')
    )

    # Contact Information
    phone = models.CharField(
        _('phone number'),
        max_length=20,
        blank=True,
        help_text=_('Contact phone number for the venue')
    )
    email = models.EmailField(
        _('email address'),
        blank=True,
        help_text=_('Contact email address for the venue')
    )
    website_url = models.URLField(
        _('website URL'),
        blank=True,
        help_text=_('Official website URL for the venue')
    )

    # Operational Details
    operating_hours = models.TextField(
        _('operating hours'),
        max_length=500,
        blank=True,
        help_text=_('Operating hours (e.g., 9AM-5PM daily schedule)')
    )
    opening_notes = models.TextField(
        _('opening notes'),
        max_length=300,
        blank=True,
        help_text=_('Custom notes about opening times')
    )

    # Search & Metadata
    tags = models.CharField(
        _('tags'),
        max_length=255,
        blank=True,
        help_text=_('Keywords for search optimization (comma-separated)'),
        db_index=True
    )
    search_vector = SearchVectorField(
        null=True,
        blank=True,
        editable=False,
        help_text=_('Search optimization field')
    )
    approval_status = models.CharField(
        _('approval status'),
        max_length=20,
        choices=APPROVAL_STATUS_CHOICES,
        default=DRAFT,
        help_text=_('Admin approval status')
    )
    visibility = models.CharField(
        _('visibility'),
        max_length=20,
        choices=VISIBILITY_CHOICES,
        default=ACTIVE,
        help_text=_('Visibility to customers')
    )
    admin_notes = models.TextField(
        _('admin notes'),
        blank=True,
        help_text=_('Internal notes about approval/rejection')
    )

    # Status Tracking
    status_log = models.JSONField(
        default=list,
        editable=False,
        help_text=_('Audit log of status changes')
    )

    # Soft Delete
    is_deleted = models.BooleanField(
        _('deleted status'),
        default=False,
        help_text=_('Mark as deleted instead of actual deletion')
    )

    # Timestamps and Tracking
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    approved_at = models.DateTimeField(
        _('approved at'),
        null=True,
        blank=True,
        help_text=_('When the venue was approved')
    )
    rejected_at = models.DateTimeField(
        _('rejected at'),
        null=True,
        blank=True,
        help_text=_('When the venue was rejected')
    )
    last_modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='modified_venues',
        verbose_name=_('last modified by'),
        help_text=_('User who last modified this venue')
    )
    last_modified_at = models.DateTimeField(
        _('last modified at'),
        auto_now=True,
        help_text=_('When the venue was last modified')
    )

    class Meta:
        verbose_name = _('Venue')
        verbose_name_plural = _('Venues')
        ordering = ['-created_at']
        # select_related = ['service_provider', 'us_city']
        # prefetch_related = ['categories', 'images', 'services']
        indexes = [
            models.Index(fields=['venue_name']),
            models.Index(fields=['approval_status']),
            models.Index(fields=['visibility']),
            models.Index(fields=['state', 'county', 'city']),
            models.Index(fields=['approval_status', 'visibility']),
            models.Index(fields=['service_provider']),
            models.Index(fields=['is_deleted']),
        ]

    def __str__(self):
        return f"{self.venue_name} - {self.service_provider.business_name}"

    def clean(self):
        """Validate tags (max 5), address fields, and field relationships."""
        super().clean()

        # Validate required fields
        errors = {}

        if not self.venue_name or not self.venue_name.strip():
            errors['venue_name'] = _('Venue name is required')

        if not self.short_description or not self.short_description.strip():
            errors['short_description'] = _('Short description is required')

        if not self.state:
            errors['state'] = _('State is required')

        if not self.county or not self.county.strip():
            errors['county'] = _('County is required')

        if not self.city or not self.city.strip():
            errors['city'] = _('City is required')

        if not self.street_number or not self.street_number.strip():
            errors['street_number'] = _('Street number is required')

        if not self.street_name or not self.street_name.strip():
            errors['street_name'] = _('Street name is required')

        # Validate state is in choices
        if self.state and self.state not in [choice[0] for choice in self.STATE_CHOICES]:
            errors['state'] = _('Please select a valid state')

        # Validate tags
        if self.tags:
            tags_list = self.get_tags_list()
            if len(tags_list) > 5:
                errors['tags'] = _('Maximum 5 tags allowed')
            if any(len(tag) > 50 for tag in tags_list):
                errors['tags'] = _('Tags cannot exceed 50 characters')

        if errors:
            raise ValidationError(errors)

    def save(self, *args, **kwargs):
        """Handle slug generation, approval timestamps, and status tracking."""
        # Always generate slug from venue_name with length limit
        self.slug = orig = slugify(self.venue_name)[:250]  # Ensure length limit
        counter = 1
        while Venue.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
            self.slug = f"{orig}-{counter}"
            counter += 1

        # Status tracking
        if self.pk:
            try:
                orig = Venue.objects.get(pk=self.pk)
                if orig.approval_status != self.approval_status:
                    self.status_log.append({
                        'status': self.approval_status,
                        'timestamp': timezone.now().isoformat(),
                        'by': kwargs.get('request_user', 'system')
                    })
            except Venue.DoesNotExist:
                pass

        # Approval/Rejection timestamp handling
        if self.approval_status == self.APPROVED and not self.approved_at:
            self.approved_at = timezone.now()
            self.rejected_at = None  # Clear rejection timestamp if approved
        elif self.approval_status == self.REJECTED and not self.rejected_at:
            self.rejected_at = timezone.now()
            self.approved_at = None  # Clear approval timestamp if rejected
        elif self.approval_status == self.PENDING:
            # Clear both timestamps if back to pending
            self.approved_at = None
            self.rejected_at = None

        super().save(*args, **kwargs)

    def get_absolute_url(self):
        """URL for venue detail page."""
        return reverse('venues_app:venue_detail', kwargs={'venue_slug': self.slug})

    # Status Properties
    @property
    def is_approved(self):
        return self.approval_status == self.APPROVED

    @property
    def is_draft(self):
        return self.approval_status == self.DRAFT

    @property
    def is_visible(self):
        return (self.visibility == self.ACTIVE and
                self.is_approved and
                self.approval_status != self.DRAFT)

    @property
    def full_address(self):
        return f"{self.street_number} {self.street_name}, {self.city}, {self.county}, {self.state}"

    # Backward compatibility property
    @property
    def name(self):
        """Backward compatibility property for templates."""
        return self.venue_name

    def delete(self, *args, **kwargs):
        """Implement soft delete."""
        self.is_deleted = True
        self.visibility = self.INACTIVE
        self.save()

    def get_tags_list(self):
        """Get cleaned and normalized tags list."""
        return list(set(tag.strip().lower() for tag in self.tags.split(',') if tag.strip())) if self.tags else []

    def get_primary_image(self):
        """Return URL of primary gallery image, main image, or first active gallery image."""
        # First check for primary gallery image
        primary_image = self.images.filter(is_primary=True, is_active=True).first()
        if primary_image:
            return primary_image.image.url

        # Fallback to main_image field
        if self.main_image:
            return self.main_image.url

        # Fallback to first active gallery image
        try:
            first_image = self.images.filter(is_active=True).order_by('order').first()
            return first_image.image.url if first_image else None
        except AttributeError:
            return getattr(settings, 'DEFAULT_VENUE_IMAGE_URL', None)

    def get_gallery_images(self):
        """Return all active gallery images ordered by primary status and order."""
        return self.images.filter(is_active=True).order_by('-is_primary', 'order')

    def get_non_primary_images(self):
        """Return all active non-primary gallery images ordered by order."""
        return self.images.filter(is_active=True, is_primary=False).order_by('order')



# --- VenueCategory Relationship Model ---

class VenueCategory(models.Model):
    """Through model for Venue-Category relationships."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='venue_categories',
        verbose_name=_('venue')
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        related_name='category_venues',
        verbose_name=_('category')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)

    class Meta:
        verbose_name = _('Venue Category')
        verbose_name_plural = _('Venue Categories')
        unique_together = ['venue', 'category']

    def __str__(self):
        return f"{self.venue.venue_name} - {self.category.name}"



# --- VenueImage Model ---
class VenueImage(models.Model):
    """Gallery images for venues (max 5 per venue)."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='images',
        verbose_name=_('venue'),
        help_text=_('Venue this image belongs to')
    )
    image = models.ImageField(
        _('image'),
        upload_to=get_venue_gallery_image_path,
        help_text=_('Venue image file (JPG/PNG, max 5MB)')
    )
    order = models.PositiveIntegerField(
        _('display order'),
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_('Display order of the image (1-5)')
    )
    caption = models.CharField(
        _('caption'),
        max_length=255,
        blank=True,
        help_text=_('Optional caption for the image')
    )
    is_primary = models.BooleanField(
        _('primary image'),
        default=False,
        help_text=_('Whether this is the primary image for the venue')
    )
    is_active = models.BooleanField(
        _('active status'),
        default=True,
        help_text=_('Whether this image is visible')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Venue Image')
        verbose_name_plural = _('Venue Images')
        ordering = ['-is_primary', 'order', 'created_at']
        constraints = [
            models.UniqueConstraint(
                fields=['venue', 'order'],
                name='unique_venue_image_order',
                deferrable=models.Deferrable.DEFERRED
            ),
            models.UniqueConstraint(
                fields=['venue'],
                condition=models.Q(is_primary=True),
                name='unique_primary_image_per_venue'
            )
        ]

    def __str__(self):
        primary_text = " (Primary)" if self.is_primary else ""
        return f"{self.venue.venue_name} - Image {self.order}{primary_text}"

    def clean(self):
        """Enforce maximum of 5 images per venue and validate image file."""
        super().clean()
        if self.venue_id:
            existing = VenueImage.objects.filter(venue=self.venue).exclude(pk=self.pk)
            if existing.count() >= 5:
                raise ValidationError(_('Maximum 5 images allowed per venue'))

        # Validate image file if present
        if self.image:
            try:
                from utils.image_utils import validate_image_extension, validate_image_size
                validate_image_extension(self.image.name, 'venue_gallery')
                validate_image_size(self.image, max_size_kb=5120)  # 5MB
            except ImportError:
                pass  # Skip validation if utils not available
            except ValidationError as e:
                raise ValidationError({'image': e.message})

    def save(self, *args, **kwargs):
        """Handle primary image logic and auto-assign order."""
        # If this is being set as primary, unset other primary images
        if self.is_primary:
            VenueImage.objects.filter(venue=self.venue, is_primary=True).exclude(pk=self.pk).update(is_primary=False)

        # Auto-assign order if not set
        if self.order is None:
            max_order = VenueImage.objects.filter(venue=self.venue).exclude(pk=self.pk).aggregate(
                max_order=models.Max('order')
            )['max_order'] or 0
            self.order = max_order + 1

        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """Handle image file deletion and primary image reassignment."""
        # Store venue reference before deletion
        venue = self.venue
        was_primary = self.is_primary

        # Delete the actual image file
        if self.image:
            try:
                from utils.image_service import ImageService
                ImageService.delete_image(self.image.path)
            except (ImportError, Exception):
                # Fallback to basic file deletion
                if self.image.storage.exists(self.image.name):
                    self.image.storage.delete(self.image.name)

        super().delete(*args, **kwargs)

        # If this was the primary image, set the first remaining image as primary
        if was_primary:
            first_image = VenueImage.objects.filter(venue=venue, is_active=True).order_by('order').first()
            if first_image:
                first_image.is_primary = True
                first_image.save(update_fields=['is_primary'])


# --- VenueFAQ Model ---
class VenueFAQ(models.Model):
    """Frequently asked questions for venues (max 5 per venue)."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='faqs',
        verbose_name=_('venue'),
        help_text=_('Venue this FAQ belongs to')
    )
    question = models.CharField(
        _('question'),
        max_length=255,
        help_text=_('Frequently asked question')
    )
    answer = models.TextField(
        _('answer'),
        max_length=500,
        help_text=_('Answer to the question (max 500 characters)')
    )
    order = models.PositiveIntegerField(
        _('display order'),
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_('Display order of the FAQ (1-5)')
    )
    is_active = models.BooleanField(
        _('active status'),
        default=True,
        help_text=_('Whether this FAQ is visible')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Venue FAQ')
        verbose_name_plural = _('Venue FAQs')
        ordering = ['order', 'created_at']
        unique_together = ['venue', 'order']

    def __str__(self):
        return f"{self.venue.venue_name} - FAQ {self.order}: {self.question[:50]}"

    def clean(self):
        """Enforce maximum of 5 FAQs per venue."""
        super().clean()
        if self.venue_id:
            existing = VenueFAQ.objects.filter(venue=self.venue).exclude(pk=self.pk)
            if existing.count() >= 5:
                raise ValidationError(_('Maximum 5 FAQs allowed per venue'))


# --- Service Model ---
class Service(models.Model):
    """Services offered at a venue (max 7 per venue)."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='services',
        verbose_name=_('venue'),
        help_text=_('Venue where this service is offered')
    )
    service_title = models.CharField(
        _('service title'),
        max_length=255,
        help_text=_('Title/name of the service')
    )
    slug = models.SlugField(
        _('slug'),
        max_length=255,
        unique=True,
        blank=True,
        null=True,
        help_text=_('URL-friendly slug (auto-generated)')
    )
    short_description = models.TextField(
        _('description'),
        max_length=500,
        blank=True,
        help_text=_('Brief description of the service')
    )

    # Pricing & Duration
    price_min = models.DecimalField(
        _('minimum price'),
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('Minimum price for this service')
    )
    price_max = models.DecimalField(
        _('maximum price'),
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        null=True,
        blank=True,
        help_text=_('Maximum price for variable pricing')
    )
    duration_minutes = models.PositiveIntegerField(
        _('duration (minutes)'),
        validators=[MinValueValidator(1), MaxValueValidator(1440)],
        default=60,
        help_text=_('Duration of the service in minutes')
    )

    # Status
    is_active = models.BooleanField(
        _('active status'),
        default=True,
        help_text=_('Whether this service is bookable')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Service')
        verbose_name_plural = _('Services')
        ordering = ['service_title']
        indexes = [
            models.Index(fields=['service_title']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.service_title

    def clean(self):
        """Validate price range and service limits."""
        super().clean()

        # Pricing validation
        if self.price_min is not None:
            if self.price_max and self.price_max < self.price_min:
                raise ValidationError(_('Maximum price cannot be less than minimum price'))
            if self.price_min <= Decimal('0.00'):
                raise ValidationError(_('Price must be greater than zero'))

        # Service limit validation
        if self.venue_id:
            existing = Service.objects.filter(venue=self.venue).exclude(pk=self.pk)
            if existing.count() >= 7:
                raise ValidationError(_('Maximum 7 services allowed per venue'))

    def save(self, *args, **kwargs):
        """Automatically generate slug on every save."""
        # Always generate slug from service_title
        self.slug = slugify(self.service_title)
        base_slug = self.slug
        counter = 1
        while Service.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
            self.slug = f"{base_slug}-{counter}"
            counter += 1
        super().save(*args, **kwargs)

    # Display Properties
    @property
    def price_display(self):
        if self.price_max and self.price_max != self.price_min:
            return f"${self.price_min} - ${self.price_max}"
        return f"${self.price_min}"

    @property
    def duration_display(self):
        hours, minutes = divmod(self.duration_minutes, 60)
        return f"{hours}h {minutes}m" if hours else f"{minutes}m"

    # Backward compatibility
    @property
    def price(self):
        return self.price_min


# --- OperatingHours Model ---
class OperatingHours(models.Model):
    """Structured business hours for venues."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='operating_hours_set',
        verbose_name=_('venue'),
        help_text=_('Venue these hours belong to')
    )

    DAY_CHOICES = [
        (0, _('Monday')),
        (1, _('Tuesday')),
        (2, _('Wednesday')),
        (3, _('Thursday')),
        (4, _('Friday')),
        (5, _('Saturday')),
        (6, _('Sunday')),
    ]

    day = models.PositiveSmallIntegerField(
        _('day of week'),
        choices=DAY_CHOICES,
        help_text=_('Day of the week (0=Monday, 6=Sunday)')
    )
    opening = models.TimeField(
        _('opening time'),
        null=True,
        blank=True,
        help_text=_('Opening time for this day')
    )
    closing = models.TimeField(
        _('closing time'),
        null=True,
        blank=True,
        help_text=_('Closing time for this day')
    )
    is_closed = models.BooleanField(
        _('closed'),
        default=False,
        help_text=_('Whether the venue is closed on this day')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Operating Hours')
        verbose_name_plural = _('Operating Hours')
        unique_together = ['venue', 'day']
        ordering = ['day']

    def __str__(self):
        day_name = dict(self.DAY_CHOICES)[self.day]
        if self.is_closed:
            return f"{self.venue.venue_name} - {day_name}: Closed"
        return f"{self.venue.venue_name} - {day_name}: {self.opening}-{self.closing}"

    def clean(self):
        """Validate opening/closing times."""
        super().clean()
        if not self.is_closed:
            if not self.opening or not self.closing:
                raise ValidationError(_('Opening and closing times are required when not closed'))
            if self.opening >= self.closing:
                raise ValidationError(_('Opening time must be before closing time'))


# --- VenueAmenity Model ---
class VenueAmenity(models.Model):
    """Amenities and features available at venues."""

    # Predefined amenity choices
    WIFI = 'wifi'
    PARKING = 'parking'
    WHEELCHAIR_ACCESSIBLE = 'wheelchair_accessible'
    AIR_CONDITIONING = 'air_conditioning'
    HEATING = 'heating'
    MUSIC = 'music'
    PRIVATE_ROOMS = 'private_rooms'
    CHANGING_ROOMS = 'changing_rooms'
    SHOWER_FACILITIES = 'shower_facilities'
    REFRESHMENTS = 'refreshments'
    RETAIL_SHOP = 'retail_shop'
    ONLINE_BOOKING = 'online_booking'
    CREDIT_CARDS = 'credit_cards'
    GIFT_CERTIFICATES = 'gift_certificates'
    LOYALTY_PROGRAM = 'loyalty_program'

    AMENITY_CHOICES = [
        (WIFI, _('Wi-Fi')),
        (PARKING, _('Parking Available')),
        (WHEELCHAIR_ACCESSIBLE, _('Wheelchair Accessible')),
        (AIR_CONDITIONING, _('Air Conditioning')),
        (HEATING, _('Heating')),
        (MUSIC, _('Background Music')),
        (PRIVATE_ROOMS, _('Private Treatment Rooms')),
        (CHANGING_ROOMS, _('Changing Rooms')),
        (SHOWER_FACILITIES, _('Shower Facilities')),
        (REFRESHMENTS, _('Refreshments Available')),
        (RETAIL_SHOP, _('Retail Shop')),
        (ONLINE_BOOKING, _('Online Booking')),
        (CREDIT_CARDS, _('Credit Cards Accepted')),
        (GIFT_CERTIFICATES, _('Gift Certificates')),
        (LOYALTY_PROGRAM, _('Loyalty Program')),
    ]

    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='amenities',
        verbose_name=_('venue'),
        help_text=_('Venue this amenity belongs to')
    )
    amenity_type = models.CharField(
        _('amenity type'),
        max_length=50,
        choices=AMENITY_CHOICES,
        help_text=_('Type of amenity or feature')
    )
    custom_name = models.CharField(
        _('custom name'),
        max_length=100,
        blank=True,
        help_text=_('Custom name for the amenity (optional)')
    )
    description = models.TextField(
        _('description'),
        max_length=200,
        blank=True,
        help_text=_('Additional details about this amenity')
    )
    is_active = models.BooleanField(
        _('active'),
        default=True,
        help_text=_('Whether this amenity is currently available')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Venue Amenity')
        verbose_name_plural = _('Venue Amenities')
        unique_together = ['venue', 'amenity_type']
        ordering = ['amenity_type']

    def __str__(self):
        display_name = self.custom_name if self.custom_name else self.get_amenity_type_display()
        return f"{self.venue.venue_name} - {display_name}"

    def clean(self):
        """Validate amenity data."""
        super().clean()
        # Ensure venue doesn't have too many amenities (max 15)
        if self.venue_id:
            existing = VenueAmenity.objects.filter(venue=self.venue).exclude(pk=self.pk)
            if existing.count() >= 15:
                raise ValidationError(_('Maximum 15 amenities allowed per venue'))


# --- FlaggedVenue Model ---
class FlaggedVenue(models.Model):
    """Venues flagged by customers for admin review."""
    # Status Choices
    PENDING = 'pending'
    REVIEWED = 'reviewed'
    RESOLVED = 'resolved'
    
    STATUS_CHOICES = (
        (PENDING, _('Pending')),
        (REVIEWED, _('Reviewed')),
        (RESOLVED, _('Resolved')),
    )

    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='flags',
        verbose_name=_('venue'),
        help_text=_('Venue that has been flagged')
    )
    flagged_by = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='flagged_venues',
        verbose_name=_('flagged by'),
        help_text=_('Customer who flagged this venue')
    )
    reason = models.TextField(
        _('reason'),
        max_length=1000,
        help_text=_('Reason for flagging this venue')
    )
    status = models.CharField(
        _('status'),
        max_length=20,
        choices=STATUS_CHOICES,
        default=PENDING,
        help_text=_('Status of the flag review')
    )
    reviewed_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_flags',
        verbose_name=_('reviewed by'),
        help_text=_('Admin who reviewed this flag')
    )
    admin_notes = models.TextField(
        _('admin notes'),
        blank=True,
        help_text=_('Admin notes about the flag review')
    )
    reviewed_at = models.DateTimeField(
        _('reviewed at'),
        null=True,
        blank=True,
        help_text=_('When the flag was reviewed')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Flagged Venue')
        verbose_name_plural = _('Flagged Venues')
        ordering = ['-created_at']
        unique_together = ['venue', 'flagged_by']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Flag: {self.venue.venue_name} by {self.flagged_by.email}"

    def save(self, *args, **kwargs):
        """Update reviewed_at when status changes."""
        if self.status in [self.REVIEWED, self.RESOLVED] and not self.reviewed_at:
            self.reviewed_at = timezone.now()
        elif self.status == self.PENDING:
            self.reviewed_at = None
        super().save(*args, **kwargs)

    # Status Properties
    @property
    def is_pending(self):
        return self.status == self.PENDING

    @property
    def is_resolved(self):
        return self.status == self.RESOLVED

    @classmethod
    def cleanup_old_flags(cls, days=90):
        """Delete flag reports older than specified days."""
        cutoff = timezone.now() - timedelta(days=days)
        cls.objects.filter(created_at__lt=cutoff).delete()


# --- USCity Model ---
class USCity(models.Model):
    """US cities for location search and mapping."""
    city = models.CharField(
        _('city'),
        max_length=100,
        help_text=_('City name')
    )
    state_id = models.CharField(
        _('state abbreviation'),
        max_length=2,
        help_text=_('State abbreviation (e.g., NY, CA)')
    )
    state_name = models.CharField(
        _('state name'),
        max_length=100,
        help_text=_('Full state name')
    )
    county_name = models.CharField(
        _('county name'),
        max_length=100,
        help_text=_('County name')
    )
    latitude = models.DecimalField(
        _('latitude'),
        max_digits=10,
        decimal_places=7,
        help_text=_('Latitude coordinate')
    )
    longitude = models.DecimalField(
        _('longitude'),
        max_digits=10,
        decimal_places=7,
        help_text=_('Longitude coordinate')
    )
    zip_codes = models.TextField(
        _('ZIP codes'),
        help_text=_('Space-separated list of ZIP codes')
    )
    city_id = models.CharField(
        _('city ID'),
        max_length=20,
        unique=True,
        help_text=_('Unique identifier for the city')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('US City')
        verbose_name_plural = _('US Cities')
        ordering = ['state_name', 'city']
        indexes = [
            models.Index(fields=['city']),
            models.Index(fields=['state_name']),
            models.Index(fields=['county_name']),
            models.Index(fields=['state_id']),
        ]

    def __str__(self):
        return f"{self.city}, {self.state_id}"

    def get_full_location(self):
        return f"{self.city}, {self.county_name}, {self.state_name}"