# --- Django Imports ---
from django.contrib import admin, messages
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local Imports ---
from .models import (
    Category,
    Venue,
    VenueAmenity,
    VenueCategory,
    VenueImage,
    VenueFAQ,
    Service,
    OperatingHours,
    FlaggedVenue,
    USCity,
)


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """
    Admin interface for Category model with slug auto-generation.
    """
    list_display = ('category_name', 'slug', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('category_name', 'category_description')
    readonly_fields = ('slug', 'created_at', 'updated_at')
    prepopulated_fields = {'slug': ('category_name',)}

    fieldsets = (
        (_('Category Information'), {
            'fields': ('category_name', 'slug', 'category_description')
        }),
        (_('Status'), {'fields': ('is_active',)}),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )


@admin.register(Venue)
class VenueAdmin(admin.ModelAdmin):
    """
    Admin interface for Venue model with comprehensive management features.
    """
    list_display = (
        'venue_name', 'service_provider', 'city', 'state',
        'approval_status', 'visibility', 'is_approved', 'created_at'
    )
    list_filter = (
        'approval_status', 'visibility', 'state', 'county',
        'created_at', 'approved_at'
    )
    search_fields = (
        'venue_name', 'short_description', 'city', 'state',
        'service_provider__business_name', 'tags'
    )
    readonly_fields = (
        'slug', 'created_at', 'updated_at', 'approved_at',
        'full_address', 'search_vector'
    )

    fieldsets = (
        (_('Basic Information'), {
            'fields': (
                'service_provider', 'venue_name', 'slug',
                'short_description', 'main_image'
            )
        }),
        (_('Location'), {
            'fields': (
                'street_number', 'street_name', 'city', 'county',
                'state', 'us_city', 'full_address',
                'latitude', 'longitude'
            )
        }),
        (_('Operational Details'), {
            'fields': (
                'operating_hours', 'opening_notes', 'tags'
            )
        }),
        (_('Status & Approval'), {
            'fields': (
                'approval_status', 'visibility', 'admin_notes',
                'approved_at'
            )
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at', 'search_vector')
        }),
    )

    actions = ['approve_venues', 'reject_venues', 'activate_venues', 'deactivate_venues']

    def approve_venues(self, request, queryset):
        """Approve selected venues and trigger notifications."""
        from django.utils import timezone
        updated = 0
        for venue in queryset:
            venue.approval_status = Venue.APPROVED
            venue.approved_at = timezone.now()
            venue.rejected_at = None
            venue.save()  # This will trigger signals
            updated += 1
        self.message_user(request, f'{updated} venue(s) approved.', messages.SUCCESS)
    approve_venues.short_description = 'Approve selected venues'

    def reject_venues(self, request, queryset):
        """Reject selected venues and trigger notifications."""
        from django.utils import timezone
        updated = 0
        for venue in queryset:
            venue.approval_status = Venue.REJECTED
            venue.rejected_at = timezone.now()
            venue.approved_at = None
            if not venue.admin_notes:
                venue.admin_notes = 'Rejected via bulk action'
            venue.save()  # This will trigger signals
            updated += 1
        self.message_user(request, f'{updated} venue(s) rejected.', messages.SUCCESS)
    reject_venues.short_description = 'Reject selected venues'

    def activate_venues(self, request, queryset):
        updated = queryset.update(visibility=Venue.ACTIVE)
        self.message_user(request, f'{updated} venue(s) activated.', messages.SUCCESS)
    activate_venues.short_description = 'Activate selected venues'

    def deactivate_venues(self, request, queryset):
        updated = queryset.update(visibility=Venue.INACTIVE)
        self.message_user(request, f'{updated} venue(s) deactivated.', messages.SUCCESS)
    deactivate_venues.short_description = 'Deactivate selected venues'


@admin.register(VenueAmenity)
class VenueAmenityAdmin(admin.ModelAdmin):
    """
    Admin interface for VenueAmenity model.
    """
    list_display = ('venue', 'amenity_type', 'custom_name', 'is_active', 'created_at')
    list_filter = ('amenity_type', 'is_active', 'created_at')
    search_fields = ('venue__venue_name', 'custom_name', 'description')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Amenity Details'), {
            'fields': ('venue', 'amenity_type', 'custom_name', 'description')
        }),
        (_('Status'), {'fields': ('is_active',)}),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )


@admin.register(VenueCategory)
class VenueCategoryAdmin(admin.ModelAdmin):
    """
    Admin interface for VenueCategory through model.
    """
    list_display = ('venue', 'category', 'created_at')
    list_filter = ('category', 'created_at')
    search_fields = ('venue__venue_name', 'category__category_name')
    readonly_fields = ('created_at',)


@admin.register(VenueImage)
class VenueImageAdmin(admin.ModelAdmin):
    """
    Admin interface for VenueImage model.
    """
    list_display = ('venue', 'order', 'caption', 'is_active', 'created_at')
    list_filter = ('is_active', 'order', 'created_at')
    search_fields = ('venue__venue_name', 'caption')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Image Details'), {
            'fields': ('venue', 'image', 'order', 'caption')
        }),
        (_('Status'), {'fields': ('is_active',)}),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )


@admin.register(VenueFAQ)
class VenueFAQAdmin(admin.ModelAdmin):
    """
    Admin interface for VenueFAQ model.
    """
    list_display = ('venue', 'question_preview', 'is_active', 'order', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('venue__venue_name', 'question', 'answer')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('FAQ Details'), {
            'fields': ('venue', 'question', 'answer', 'order')
        }),
        (_('Status'), {'fields': ('is_active',)}),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )

    def question_preview(self, obj):
        return obj.question[:50] + '...' if len(obj.question) > 50 else obj.question
    question_preview.short_description = 'Question'


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    """
    Admin interface for Service model.
    """
    list_display = (
        'service_title', 'venue', 'price_min', 'price_max',
        'duration_minutes', 'is_active', 'created_at'
    )
    list_filter = ('is_active', 'created_at', 'venue__approval_status')
    search_fields = (
        'service_title', 'short_description',
        'venue__venue_name', 'venue__service_provider__business_name'
    )
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Service Information'), {
            'fields': (
                'venue', 'service_title', 'short_description'
            )
        }),
        (_('Pricing & Duration'), {
            'fields': ('price_min', 'price_max', 'duration_minutes')
        }),
        (_('Status'), {'fields': ('is_active',)}),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )


@admin.register(OperatingHours)
class OperatingHoursAdmin(admin.ModelAdmin):
    """
    Admin interface for OperatingHours model.
    """
    list_display = ('venue', 'day', 'opening', 'closing', 'is_closed', 'created_at')
    list_filter = ('day', 'is_closed', 'created_at')
    search_fields = ('venue__venue_name',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Schedule Details'), {
            'fields': ('venue', 'day', 'opening', 'closing', 'is_closed')
        }),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )


@admin.register(FlaggedVenue)
class FlaggedVenueAdmin(admin.ModelAdmin):
    """
    Admin interface for FlaggedVenue model.
    """
    list_display = (
        'venue', 'flagged_by', 'status', 'reason_preview',
        'created_at', 'reviewed_at'
    )
    list_filter = ('status', 'created_at', 'reviewed_at')
    search_fields = (
        'venue__venue_name', 'flagged_by__email',
        'reason', 'admin_notes'
    )
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Flag Details'), {
            'fields': ('venue', 'flagged_by', 'reason')
        }),
        (_('Admin Review'), {
            'fields': ('status', 'admin_notes', 'reviewed_at')
        }),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )

    actions = ['mark_as_reviewed', 'mark_as_resolved']

    def reason_preview(self, obj):
        return obj.reason[:50] + '...' if len(obj.reason) > 50 else obj.reason
    reason_preview.short_description = 'Reason'

    def mark_as_reviewed(self, request, queryset):
        updated = queryset.update(status=FlaggedVenue.REVIEWED, reviewed_at=timezone.now())
        self.message_user(request, f'{updated} flag(s) marked as reviewed.', messages.SUCCESS)
    mark_as_reviewed.short_description = 'Mark as reviewed'

    def mark_as_resolved(self, request, queryset):
        updated = queryset.update(status=FlaggedVenue.RESOLVED, reviewed_at=timezone.now())
        self.message_user(request, f'{updated} flag(s) marked as resolved.', messages.SUCCESS)
    mark_as_resolved.short_description = 'Mark as resolved'


@admin.register(USCity)
class USCityAdmin(admin.ModelAdmin):
    """
    Admin interface for USCity model.
    """
    list_display = ('city', 'state_name', 'state_id', 'county_name', 'city_id')
    list_filter = ('state_name', 'county_name')
    search_fields = ('city', 'state_name', 'county_name', 'zip_codes')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Location Information'), {
            'fields': (
                'city', 'state_name', 'state_id', 'county_name',
                'city_id', 'zip_codes'
            )
        }),
        (_('Coordinates'), {
            'fields': ('latitude', 'longitude')
        }),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )