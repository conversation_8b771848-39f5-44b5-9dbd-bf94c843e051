from django.db.models import Avg, Count, Min, <PERSON>, Q
from .models import Category, Service, USCity, Venue


def build_venue_search_query(query_params):

    venues = Venue.objects.filter(
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE
    ).exclude(
        approval_status=Venue.DRAFT
    ).select_related('service_provider').prefetch_related('categories', 'services')
    
    # Add annotations for sorting and filtering
    venues = venues.annotate(
        avg_rating=Avg('review_app_reviews__rating'),
        review_count=Count('review_app_reviews'),
        min_service_price=Min('services__price_min'),
        max_service_price=Max('services__price_max'),
        has_discount=Count('services', filter=Q(services__discounted_price__isnull=False)) > 0
    )
    
    return venues


def get_location_suggestions(query, limit=10):

    if not query or len(query) < 2:
        return []
    
    suggestions = []
    
    # Search in USCity model
    cities = USCity.objects.filter(
        Q(city__icontains=query) |
        Q(state_name__icontains=query) |
        Q(county_name__icontains=query) |
        Q(state_id__iexact=query)
    ).distinct()[:limit]
    
    for city in cities:
        suggestions.append({
            'label': f"{city.city}, {city.state_id}",
            'value': f"{city.city}, {city.state_id}",
            'type': 'city',
            'state': city.state_name,
            'county': city.county_name
        })
    
    return suggestions


def get_hierarchical_location_data():
    """
    Get hierarchical location data for dropdowns.
    
    Returns:
        dict: Dictionary containing states, counties, and cities
    """
    states = USCity.objects.values_list('state_name', flat=True).distinct().order_by('state_name')
    
    location_data = {
        'states': list(states),
        'counties': {},
        'cities': {}
    }
    
    # Get counties for each state
    for state in states:
        counties = USCity.objects.filter(
            state_name=state
        ).values_list('county_name', flat=True).distinct().order_by('county_name')
        location_data['counties'][state] = list(counties)
        
        # Get cities for each county in this state
        for county in counties:
            cities = USCity.objects.filter(
                state_name=state,
                county_name=county
            ).values_list('city', flat=True).distinct().order_by('city')
            location_data['cities'][f"{state}_{county}"] = list(cities)
    
    return location_data


def apply_search_filters(venues, search_params):

    # Keyword search
    if search_params.get('query'):
        query = search_params['query']
        venues = venues.filter(
            Q(venue_name__icontains=query) |
            Q(short_description__icontains=query) |
            Q(tags__icontains=query) |
            Q(services__service_title__icontains=query) |
            Q(services__short_description__icontains=query) |
            Q(categories__category_name__icontains=query) |
            Q(service_provider__business_name__icontains=query)
        ).distinct()
    
    # Location search
    if search_params.get('location'):
        location = search_params['location']
        city_matches = USCity.objects.filter(
            Q(city__icontains=location) |
            Q(state_name__icontains=location) |
            Q(county_name__icontains=location) |
            Q(state_id__iexact=location)
        )
        
        if city_matches.exists():
            venues = venues.filter(
                Q(us_city__in=city_matches) |
                Q(city__icontains=location) |
                Q(state__icontains=location) |
                Q(county__icontains=location)
            ).distinct()
        else:
            venues = venues.filter(
                Q(city__icontains=location) |
                Q(state__icontains=location) |
                Q(county__icontains=location)
            )
    
    # Category filtering
    if search_params.get('category'):
        venues = venues.filter(categories__slug=search_params['category'])
    
    return venues


def apply_advanced_filters(venues, filter_params):

    # Venue type filtering
    if filter_params.get('venue_type'):
        venues = venues.filter(venue_type=filter_params['venue_type'])
    
    # Discount filtering
    if filter_params.get('has_discount'):
        venues = venues.filter(has_discount=True)
    
    # Price range filtering
    if filter_params.get('price_min') is not None:
        venues = venues.filter(min_service_price__gte=filter_params['price_min'])
    
    if filter_params.get('price_max') is not None:
        venues = venues.filter(max_service_price__lte=filter_params['price_max'])
    
    # Hierarchical location filters
    if filter_params.get('state'):
        venues = venues.filter(
            Q(state__iexact=filter_params['state']) | 
            Q(us_city__state_name__iexact=filter_params['state'])
        )
    
    if filter_params.get('county'):
        venues = venues.filter(
            Q(county__iexact=filter_params['county']) | 
            Q(us_city__county_name__iexact=filter_params['county'])
        )
    
    if filter_params.get('city'):
        venues = venues.filter(
            Q(city__iexact=filter_params['city']) | 
            Q(us_city__city__iexact=filter_params['city'])
        )
    
    # Multiple category filtering
    if filter_params.get('categories'):
        venues = venues.filter(categories__in=filter_params['categories']).distinct()
    
    return venues


def apply_sorting(venues, sort_by):

    if sort_by == 'rating_high':
        return venues.order_by('-avg_rating', '-review_count')
    elif sort_by == 'rating_low':
        return venues.order_by('avg_rating', 'review_count')
    elif sort_by == 'price_high':
        return venues.order_by('-min_service_price')
    elif sort_by == 'price_low':
        return venues.order_by('min_service_price')
    elif sort_by == 'newest':
        return venues.order_by('-created_at')
    elif sort_by == 'discount':
        return venues.order_by('-has_discount', '-min_service_price')
    elif sort_by == 'name':
        return venues.order_by('venue_name')
    else:
        # Default sorting: highest rated first, then by review count
        return venues.order_by('-avg_rating', '-review_count', 'venue_name')


def get_search_context_data(venues, search_form, filter_form, applied_filters, total_venues):
    # Get filter options
    categories = Category.objects.filter(is_active=True).order_by('name')
    states = USCity.objects.values_list('state_name', flat=True).distinct().order_by('state_name')
    
    # Get price range
    price_range = Service.objects.aggregate(
        min_price=Min('price_min'),
        max_price=Max('price_max')
    )
    
    return {
        'venues': venues,
        'search_form': search_form,
        'filter_form': filter_form,
        'categories': categories,
        'states': states,
        'applied_filters': applied_filters,
        'total_venues': total_venues,
        'price_range': price_range,
    }


def get_popular_searches():
    popular_categories = Category.objects.filter(
        is_active=True
    ).annotate(
        venue_count=Count('venues', filter=Q(venues__approval_status=Venue.APPROVED) & ~Q(venues__approval_status=Venue.DRAFT))
    ).order_by('-venue_count')[:6]

    popular_locations = USCity.objects.filter(
        venues__approval_status=Venue.APPROVED
    ).exclude(
        venues__approval_status=Venue.DRAFT
    ).annotate(
        venue_count=Count('venues')
    ).order_by('-venue_count')[:6]
    
    return {
        'popular_categories': popular_categories,
        'popular_locations': popular_locations,
    }