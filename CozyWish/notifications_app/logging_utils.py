"""
Comprehensive logging utilities for notifications_app.

This module provides structured logging functions for notification-related events
including user activities, notification delivery, admin operations, and system monitoring.
Built on top of the centralized logging utilities in utils/logging_utils.py.

Usage:
    from notifications_app.logging_utils import log_notification_created, log_notification_read
    
    log_notification_created(user, notification, request)
    log_notification_read(user, notification, request)
"""

from typing import Any, Dict, Optional

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.utils import timezone

# --- Local Imports ---
from utils.logging_utils import (
    get_app_logger, log_user_activity, log_error, log_security_event,
    log_audit_event, log_performance, get_client_info
)


User = get_user_model()


# Get app-specific loggers
logger = get_app_logger('notifications_app')
activity_logger = get_app_logger('notifications_app', 'activity')
security_logger = get_app_logger('notifications_app', 'security')
audit_logger = get_app_logger('notifications_app', 'audit')
performance_logger = get_app_logger('notifications_app', 'performance')


# ===== NOTIFICATION ACTIVITY LOGGING =====

def log_notification_created(
    notification_type: str,
    title: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    notification_id: Optional[int] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log notification creation events.
    
    Args:
        notification_type: Type of notification (booking, payment, review, etc.)
        title: Notification title
        user: User receiving the notification
        request: Django HttpRequest object
        notification_id: ID of the created notification
        details: Additional notification details
    """
    log_data = {
        'notification_type': notification_type,
        'notification_title': title,
        'notification_id': notification_id,
        'recipient_email': user.email if user else None,
        'recipient_id': user.id if user else None,
        'recipient_role': user.role if user else None
    }
    
    if details:
        log_data.update(details)
    
    log_user_activity(
        app_name='notifications_app',
        activity_type='notification_created',
        user=user,
        request=request,
        details=log_data
    )


def log_notification_read(
    user: User,
    notification_id: int,
    notification_title: str,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log notification read events.
    
    Args:
        user: User who read the notification
        notification_id: ID of the notification
        notification_title: Title of the notification
        request: Django HttpRequest object
        details: Additional details
    """
    log_data = {
        'notification_id': notification_id,
        'notification_title': notification_title,
        'action': 'read'
    }
    
    if details:
        log_data.update(details)
    
    log_user_activity(
        app_name='notifications_app',
        activity_type='notification_read',
        user=user,
        request=request,
        details=log_data
    )


def log_notification_deleted(
    user: User,
    notification_id: int,
    notification_title: str,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log notification deletion events.
    
    Args:
        user: User who deleted the notification
        notification_id: ID of the notification
        notification_title: Title of the notification
        request: Django HttpRequest object
        details: Additional details
    """
    log_data = {
        'notification_id': notification_id,
        'notification_title': notification_title,
        'action': 'deleted'
    }
    
    if details:
        log_data.update(details)
    
    log_user_activity(
        app_name='notifications_app',
        activity_type='notification_deleted',
        user=user,
        request=request,
        details=log_data
    )


def log_notifications_marked_read(
    user: User,
    count: int,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log bulk notification mark as read events.
    
    Args:
        user: User who marked notifications as read
        count: Number of notifications marked as read
        request: Django HttpRequest object
        details: Additional details
    """
    log_data = {
        'notifications_count': count,
        'action': 'bulk_mark_read'
    }
    
    if details:
        log_data.update(details)
    
    log_user_activity(
        app_name='notifications_app',
        activity_type='notifications_bulk_read',
        user=user,
        request=request,
        details=log_data
    )


def log_notification_preferences_updated(
    user: User,
    preferences_changed: Dict[str, Any],
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log notification preferences update events.
    
    Args:
        user: User who updated preferences
        preferences_changed: Dictionary of changed preferences
        request: Django HttpRequest object
        details: Additional details
    """
    log_data = {
        'preferences_changed': preferences_changed,
        'action': 'preferences_updated'
    }
    
    if details:
        log_data.update(details)
    
    log_user_activity(
        app_name='notifications_app',
        activity_type='notification_preferences_updated',
        user=user,
        request=request,
        details=log_data
    )


# ===== ADMIN AND SYSTEM LOGGING =====

def log_system_announcement_created(
    admin_user: User,
    title: str,
    target_audience: str,
    request: Optional[HttpRequest] = None,
    announcement_id: Optional[int] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log system announcement creation events.

    Args:
        admin_user: Admin user who created the announcement
        title: Announcement title
        target_audience: Target audience (all, customers, providers, etc.)
        request: Django HttpRequest object
        announcement_id: ID of the created announcement
        details: Additional announcement details
    """
    log_data = {
        'announcement_title': title,
        'target_audience': target_audience,
        'announcement_id': announcement_id,
        'action': 'system_announcement_created'
    }

    if details:
        log_data.update(details)

    log_audit_event(
        app_name='notifications_app',
        action='system_announcement_created',
        admin_user=admin_user,
        request=request,
        details=log_data
    )


def log_notification_delivery_status(
    notification_id: int,
    delivery_method: str,
    status: str,
    user: Optional[User] = None,
    error_message: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log notification delivery status events.

    Args:
        notification_id: ID of the notification
        delivery_method: Method of delivery (email, in_app, etc.)
        status: Delivery status (sent, failed, pending)
        user: User receiving the notification
        error_message: Error message if delivery failed
        details: Additional delivery details
    """
    log_data = {
        'notification_id': notification_id,
        'delivery_method': delivery_method,
        'delivery_status': status,
        'recipient_email': user.email if user else None,
        'recipient_id': user.id if user else None
    }

    if error_message:
        log_data['error_message'] = error_message

    if details:
        log_data.update(details)

    if status == 'failed':
        log_error(
            app_name='notifications_app',
            error_type='notification_delivery_failed',
            error_message=f'Failed to deliver notification {notification_id} via {delivery_method}',
            user=user,
            details=log_data
        )
    else:
        activity_logger.info(
            f"NOTIFICATION: delivery_{status} | Notification: {notification_id} | Method: {delivery_method}",
            extra=log_data
        )


def log_notification_category_management(
    admin_user: User,
    action: str,
    category_name: str,
    request: Optional[HttpRequest] = None,
    category_id: Optional[int] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log notification category management events.

    Args:
        admin_user: Admin user performing the action
        action: Action performed (created, updated, deleted)
        category_name: Name of the category
        request: Django HttpRequest object
        category_id: ID of the category
        details: Additional category details
    """
    log_data = {
        'category_name': category_name,
        'category_id': category_id,
        'action': f'category_{action}'
    }

    if details:
        log_data.update(details)

    log_audit_event(
        app_name='notifications_app',
        action=f'notification_category_{action}',
        admin_user=admin_user,
        request=request,
        details=log_data
    )


# ===== SECURITY LOGGING =====

def log_unauthorized_notification_access(
    user: Optional[User],
    notification_id: int,
    attempted_action: str,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log unauthorized notification access attempts.

    Args:
        user: User attempting unauthorized access
        notification_id: ID of the notification
        attempted_action: Action that was attempted
        request: Django HttpRequest object
        details: Additional security details
    """
    log_data = {
        'notification_id': notification_id,
        'attempted_action': attempted_action,
        'violation_type': 'unauthorized_notification_access'
    }

    if details:
        log_data.update(details)

    log_security_event(
        app_name='notifications_app',
        event_type='unauthorized_notification_access',
        user_email=user.email if user else None,
        request=request,
        details=log_data
    )


def log_notification_spam_detection(
    user: Optional[User],
    notification_type: str,
    detection_reason: str,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log notification spam detection events.

    Args:
        user: User associated with potential spam
        notification_type: Type of notification
        detection_reason: Reason for spam detection
        request: Django HttpRequest object
        details: Additional spam detection details
    """
    log_data = {
        'notification_type': notification_type,
        'detection_reason': detection_reason,
        'security_event': 'spam_detection'
    }

    if details:
        log_data.update(details)

    log_security_event(
        app_name='notifications_app',
        event_type='notification_spam_detection',
        user_email=user.email if user else None,
        request=request,
        details=log_data
    )


# ===== ERROR LOGGING =====

def log_notification_error(
    error_type: str,
    error_message: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    exception: Optional[Exception] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log notification-specific error events using centralized logging.

    Args:
        error_type: Type of error (validation, delivery, database, etc.)
        error_message: Human-readable error message
        user: User object (if applicable)
        request: Django HttpRequest object
        exception: Exception object (if applicable)
        details: Additional error details
    """
    log_error(
        app_name='notifications_app',
        error_type=error_type,
        error_message=error_message,
        user=user,
        request=request,
        exception=exception,
        details=details
    )


def log_notification_validation_error(
    validation_type: str,
    field_name: str,
    error_message: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log notification validation errors.

    Args:
        validation_type: Type of validation (form, model, business_rule)
        field_name: Name of the field that failed validation
        error_message: Validation error message
        user: User object (if applicable)
        request: Django HttpRequest object
        details: Additional validation details
    """
    error_details = {
        'validation_type': validation_type,
        'field_name': field_name,
        'validation_error': error_message
    }

    if details:
        error_details.update(details)

    log_notification_error(
        error_type='validation_error',
        error_message=f'Notification validation failed: {field_name} - {error_message}',
        user=user,
        request=request,
        details=error_details
    )


# ===== PERFORMANCE LOGGING =====

def log_notification_performance(
    operation_name: str,
    duration: float,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    performance_context: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log notification performance metrics.

    Args:
        operation_name: Name of the operation
        duration: Duration in seconds
        user: User object (if applicable)
        request: Django HttpRequest object
        performance_context: Additional performance context
    """
    details = {
        'operation_name': operation_name,
        'duration_seconds': duration,
        'performance_category': 'slow_operation' if duration > 2.0 else 'normal_operation'
    }

    if performance_context:
        details.update(performance_context)

    log_performance(
        app_name='notifications_app',
        operation=operation_name,
        duration=duration,
        user=user,
        request=request,
        details=details
    )


def performance_monitor(operation_name: str):
    """
    Decorator to monitor performance of notification functions.

    Args:
        operation_name: Name of the operation being monitored

    Usage:
        @performance_monitor('notification_creation')
        def create_notification_view(request):
            # view logic here
            pass
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = timezone.now()

            try:
                result = func(*args, **kwargs)

                # Calculate duration
                end_time = timezone.now()
                duration = (end_time - start_time).total_seconds()

                # Extract request and user from args/kwargs if available
                request = None
                user = None

                # Try to find request in args (common for view functions)
                for arg in args:
                    if hasattr(arg, 'user') and hasattr(arg, 'method'):
                        request = arg
                        user = getattr(arg, 'user', None) if hasattr(arg, 'user') else None
                        break

                # Log performance
                log_notification_performance(
                    operation_name=operation_name,
                    duration=duration,
                    user=user,
                    request=request,
                    performance_context={
                        'function_name': func.__name__,
                        'success': True
                    }
                )

                return result

            except Exception as e:
                # Calculate duration even for failed operations
                end_time = timezone.now()
                duration = (end_time - start_time).total_seconds()

                # Extract request and user from args/kwargs if available
                request = None
                user = None

                for arg in args:
                    if hasattr(arg, 'user') and hasattr(arg, 'method'):
                        request = arg
                        user = getattr(arg, 'user', None) if hasattr(arg, 'user') else None
                        break

                # Log performance for failed operation
                log_notification_performance(
                    operation_name=f"{operation_name}_failed",
                    duration=duration,
                    user=user,
                    request=request,
                    performance_context={
                        'function_name': func.__name__,
                        'success': False,
                        'exception_type': type(e).__name__
                    }
                )

                # Log the error
                log_notification_error(
                    error_type='operation_failed',
                    error_message=f'Operation {operation_name} failed in {func.__name__}',
                    user=user,
                    request=request,
                    exception=e,
                    details={
                        'operation_name': operation_name,
                        'function_name': func.__name__,
                        'duration_seconds': duration
                    }
                )

                raise

        return wrapper
    return decorator
