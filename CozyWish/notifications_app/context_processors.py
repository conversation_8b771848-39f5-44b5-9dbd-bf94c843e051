"""Context processors used by :mod:`notifications_app`."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache

# --- Local Imports ---
from .models import Notification
from .utils import get_unread_count


# Set up logging
logger = logging.getLogger('notifications_app.context_processors')

# Get the user model
User = get_user_model()


def notifications_context(request):
    """
    Add notification context to all templates.
    
    This context processor adds:
    - unread_notifications_count: Count of unread notifications for the current user
    - recent_notifications: Recent notifications for the current user (last 5)
    
    Args:
        request: The HTTP request object
        
    Returns:
        dict: Context dictionary with notification data
    """
    context = {
        'unread_notifications_count': 0,
        'recent_notifications': [],
    }
    
    try:
        # Only add notification data for authenticated users
        if request.user.is_authenticated:
            cache_key = f"notif_unread_count_{request.user.id}"
            unread_count = cache.get(cache_key)
            if unread_count is None:
                unread_count = get_unread_count(request.user)
                cache.set(cache_key, unread_count, getattr(settings, 'NOTIFICATION_CACHE_TIMEOUT', 60))

            context['unread_notifications_count'] = unread_count

            context['recent_notifications'] = Notification.objects.filter(
                user=request.user
            ).order_by('-created_at')[:5]
            
    except Exception as e:
        logger.error(
            f"Error in notifications context processor: {str(e)}",
            extra={
                'user_id': request.user.id if request.user.is_authenticated else None,
                'error': str(e)
            }
        )
    
    return context
