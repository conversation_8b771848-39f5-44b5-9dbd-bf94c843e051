"""URL configuration for :mod:`notifications_app`."""

# --- Third-Party Imports ---
from django.conf import settings
from django.urls import path

# --- Local Imports ---
from . import views


app_name = 'notifications_app'

urlpatterns = [
    # ===== CUSTOMER AND PROVIDER NOTIFICATION URLS =====
    
    # Main notification views
    path('', views.notification_list, name='notification_list'),
    path('<int:notification_id>/', views.notification_detail, name='notification_detail'),
    
    # Notification actions
    path('<int:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),
    path('<int:notification_id>/unread/', views.mark_notification_unread, name='mark_notification_unread'),
    path('<int:notification_id>/delete/', views.delete_notification, name='delete_notification'),
    path('mark-all-read/', views.mark_all_notifications_read, name='mark_all_notifications_read'),
    path('mark-all-unread/', views.mark_all_notifications_unread, name='mark_all_notifications_unread'),
    path('bulk-mark-read/', views.bulk_mark_notifications_read, name='bulk_mark_notifications_read'),
    path('bulk-mark-unread/', views.bulk_mark_notifications_unread, name='bulk_mark_notifications_unread'),
    path('preferences/', views.notification_preferences_view, name='notification_preferences'),
    
    # AJAX endpoints
    path('unread/', views.get_unread_notifications, name='get_unread_notifications'),
    
    # ===== ADMIN NOTIFICATION URLS =====
    
    # Admin dashboard and management
    path('admin/dashboard/', views.admin_notification_dashboard, name='admin_notification_dashboard'),
    path('admin/announcement/create/', views.admin_create_announcement, name='admin_create_announcement'),
    path('announcement/<slug:slug>/', views.announcement_detail, name='announcement_detail'),
    
    # Admin notification management
    path('admin/notifications/', views.admin_notification_list, name='admin_notification_list'),
    path('admin/notifications/<int:notification_id>/', views.admin_notification_detail, name='admin_notification_detail'),
    
    # ===== TEST AND UTILITY URLS =====
]

# Test view (development only)
if getattr(settings, 'ENABLE_TEST_VIEW', False):
    urlpatterns.append(path('test/', views.test_view, name='test_view'))
