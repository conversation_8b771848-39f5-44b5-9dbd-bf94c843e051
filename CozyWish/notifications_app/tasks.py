"""Celery tasks used by :mod:`notifications_app`."""

# --- Third-Party Imports ---
from celery import shared_task
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone

# --- Local Imports ---
from .models import AdminAnnouncement, Notification


User = get_user_model()

@shared_task
def send_admin_announcement_task(announcement_id):
    try:
        announcement = AdminAnnouncement.objects.get(id=announcement_id)
    except AdminAnnouncement.DoesNotExist:
        return 0

    target_users = announcement.get_target_users()
    notifications = [
        Notification(
            user=user,
            notification_type=Notification.ANNOUNCEMENT,
            title=announcement.title,
            message=announcement.announcement_text,
            related_object_id=announcement.id,
            related_object_type='AdminAnnouncement'
        )
        for user in target_users
    ]
    Notification.objects.bulk_create(notifications)

    announcement.status = AdminAnnouncement.SENT
    announcement.sent_at = timezone.now()
    announcement.total_recipients = len(notifications)
    announcement.save(update_fields=['status', 'sent_at', 'total_recipients'])
    return announcement.total_recipients


@shared_task
def send_daily_digest_task(user_id):
    """Send a daily notification digest email to a user."""
    try:
        user = User.objects.get(id=user_id, is_active=True)
    except User.DoesNotExist:
        return 0

    since = timezone.now() - timezone.timedelta(days=1)
    notifications = Notification.objects.filter(
        user=user,
        created_at__gte=since,
        read_status=Notification.UNREAD,
    ).order_by('-created_at')

    count = notifications.count()
    if count == 0:
        return 0

    context = {
        'user': user,
        'notifications': notifications,
        'count': count,
    }
    message = render_to_string('notifications_app/emails/digest_email.txt', context)

    send_mail(
        subject='CozyWish Daily Notification Digest',
        message=message,
        from_email=settings.DEFAULT_FROM_EMAIL,
        recipient_list=[user.email],
        fail_silently=False,
    )

    return count
