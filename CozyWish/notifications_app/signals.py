"""Signal handlers for :mod:`notifications_app`."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone

# --- Local Imports ---
from .utils import (
    notify_booking_cancellation,
    notify_booking_status_changed,
    notify_new_booking,
    notify_new_review,
    notify_payment_successful,
    notify_review_response,
    notify_service_provider_approval,
)


# Import logging utilities
try:
    from .logging_utils import (
        log_notification_created, log_notification_error,
        log_notification_delivery_status
    )
    from utils.logging_utils import get_app_logger
    LOGGING_ENABLED = True
except ImportError:
    LOGGING_ENABLED = False

    def log_notification_created(*args, **kwargs):
        pass

    def log_notification_error(*args, **kwargs):
        pass

    def log_notification_delivery_status(*args, **kwargs):
        pass

    def get_app_logger(app_name, logger_type=''):
        import logging
        return logging.getLogger(app_name)

# Set up logging
logger = get_app_logger('notifications_app', 'signals')

# Get the user model
User = get_user_model()


@receiver(pre_save, sender='booking_cart_app.Booking')
def store_old_booking_status(sender, instance, **kwargs):
    """
    Store the old booking status before saving to detect changes.
    
    This signal handler stores the previous status of a booking so we can
    detect status changes in the post_save signal.
    """
    try:
        if instance.pk:
            # Get the old instance from database
            old_instance = sender.objects.get(pk=instance.pk)
            instance._old_status = old_instance.status
        else:
            instance._old_status = None
            
    except sender.DoesNotExist:
        instance._old_status = None
    except Exception as e:
        # Use app-specific error logging
        log_notification_error(
            error_type='signal_processing_error',
            error_message=f"Error storing old booking status for booking {instance.pk}",
            exception=e,
            details={
                'booking_id': instance.pk,
                'signal_handler': 'store_old_booking_status'
            }
        )
        instance._old_status = None


@receiver(post_save, sender='booking_cart_app.Booking')
def handle_booking_notifications(sender, instance, created, **kwargs):
    """
    Handle booking-related notifications.
    
    This signal handler:
    1. Creates notifications for new bookings
    2. Creates notifications for booking status changes
    3. Creates notifications for booking cancellations
    """
    try:
        if created:
            # New booking created
            notify_new_booking(instance)
            logger.info(
                f"New booking notification sent for booking {instance.booking_id}",
                extra={
                    'booking_id': instance.booking_id,
                    'customer': instance.customer.email,
                    'venue': instance.venue.venue_name
                }
            )
        else:
            # Existing booking updated - check for status changes
            old_status = getattr(instance, '_old_status', None)
            
            if old_status and old_status != instance.status:
                if instance.status == 'cancelled':
                    # Booking was cancelled
                    notify_booking_cancellation(instance)
                    logger.info(
                        f"Booking cancellation notification sent for booking {instance.booking_id}",
                        extra={
                            'booking_id': instance.booking_id,
                            'old_status': old_status,
                            'new_status': instance.status
                        }
                    )
                else:
                    # Other status change
                    notify_booking_status_changed(instance, old_status)
                    logger.info(
                        f"Booking status change notification sent for booking {instance.booking_id}",
                        extra={
                            'booking_id': instance.booking_id,
                            'old_status': old_status,
                            'new_status': instance.status
                        }
                    )
                    
    except Exception as e:
        logger.error(
            f"Error in booking notification signal: {str(e)}",
            extra={
                'booking_id': instance.booking_id if hasattr(instance, 'booking_id') else None,
                'created': created,
                'error': str(e)
            }
        )


@receiver(post_save, sender='review_app.Review')
def handle_review_notifications(sender, instance, created, **kwargs):
    """
    Handle review-related notifications.
    
    This signal handler creates notifications when new reviews are posted.
    """
    try:
        if created:
            # New review created
            notify_new_review(instance)
            logger.info(
                f"New review notification sent for review {instance.id}",
                extra={
                    'review_id': instance.id,
                    'customer': instance.customer.email,
                    'venue': instance.venue.venue_name,
                    'rating': instance.rating
                }
            )
            
    except Exception as e:
        logger.error(
            f"Error in review notification signal: {str(e)}",
            extra={
                'review_id': instance.id if hasattr(instance, 'id') else None,
                'created': created,
                'error': str(e)
            }
        )


@receiver(post_save, sender='review_app.ReviewResponse')
def handle_review_response_notifications(sender, instance, created, **kwargs):
    """
    Handle review response notifications.
    
    This signal handler creates notifications when providers respond to reviews.
    """
    try:
        if created:
            # New review response created
            notify_review_response(instance.review, instance)
            logger.info(
                f"Review response notification sent for response {instance.id}",
                extra={
                    'response_id': instance.id,
                    'review_id': instance.review.id,
                    'provider': instance.provider.email
                }
            )
            
    except Exception as e:
        logger.error(
            f"Error in review response notification signal: {str(e)}",
            extra={
                'response_id': instance.id if hasattr(instance, 'id') else None,
                'created': created,
                'error': str(e)
            }
        )


@receiver(post_save, sender='payments_app.Payment')
def handle_payment_notifications(sender, instance, created, **kwargs):
    """
    Handle payment-related notifications.
    
    This signal handler creates notifications for successful payments.
    """
    try:
        # Only notify for successful payments
        if instance.payment_status == 'succeeded':
            notify_payment_successful(instance)
            logger.info(
                f"Payment success notification sent for payment {instance.payment_id}",
                extra={
                    'payment_id': instance.payment_id,
                    'customer': instance.customer.email,
                    'amount': str(instance.amount_paid),
                    'status': instance.payment_status
                }
            )
            
    except Exception as e:
        logger.error(
            f"Error in payment notification signal: {str(e)}",
            extra={
                'payment_id': instance.payment_id if hasattr(instance, 'payment_id') else None,
                'created': created,
                'error': str(e)
            }
        )


@receiver(post_save, sender='accounts_app.CustomUser')
def handle_user_activation(sender, instance, created, **kwargs):
    """
    Handle user activation and registration notifications.

    This signal handler:
    1. Sends welcome emails to customers when they register
    2. Sends welcome notifications to service providers when they verify their email
    """
    try:
        # Handle customer registration - send welcome email immediately
        if instance.is_customer and created:
            from utility_app.email_utils import send_welcome_email

            # Get customer name for personalization
            customer_name = instance.get_full_name() or instance.email.split('@')[0]

            # Send welcome email
            email_sent = send_welcome_email(instance.email, customer_name)

            if email_sent:
                logger.info(
                    f"Welcome email sent to new customer: {instance.email}",
                    extra={
                        'user_id': instance.id,
                        'user_email': instance.email,
                        'user_type': 'customer',
                        'customer_name': customer_name
                    }
                )
            else:
                logger.warning(
                    f"Failed to send welcome email to new customer: {instance.email}",
                    extra={
                        'user_id': instance.id,
                        'user_email': instance.email,
                        'user_type': 'customer'
                    }
                )

        # Handle service providers who just became active (email verification)
        elif (instance.is_service_provider and instance.is_active and
              hasattr(instance, 'service_provider_profile')):

            # Check if this is a new activation (not creation)
            if not created:
                # Send welcome notification for newly activated service provider
                notify_service_provider_approval(instance.service_provider_profile)
                logger.info(
                    f"Service provider welcome notification sent after email verification for user {instance.id}",
                    extra={
                        'user_id': instance.id,
                        'user_email': instance.email,
                        'business_name': instance.service_provider_profile.business_name
                    }
                )

    except Exception as e:
        logger.error(
            f"Error in user activation notification signal: {str(e)}",
            extra={
                'user_id': instance.id if hasattr(instance, 'id') else None,
                'created': created,
                'error': str(e)
            }
        )


@receiver(post_save, sender='accounts_app.ServiceProviderProfile')
def handle_provider_notifications(sender, instance, created, **kwargs):
    """
    Handle service provider profile notifications.

    This signal handler is disabled to prevent duplicate emails.
    Welcome notifications are now sent when user email is verified.
    """
    # Disabled to prevent duplicate emails during signup
    # Welcome notifications are handled in handle_user_activation signal
    pass


@receiver(post_save, sender='venues_app.Venue')
def handle_venue_notifications(sender, instance, created, **kwargs):
    """
    Handle venue-related notifications.
    
    This signal handler creates notifications for venue status changes.
    """
    try:
        # Only handle venue approval/rejection notifications if needed
        # For now, we'll just log venue creation
        if created:
            logger.info(
                f"New venue created: {instance.venue_name}",
                extra={
                    'venue_id': instance.id,
                    'venue_name': instance.venue_name,
                    'provider': instance.service_provider.user.email
                }
            )
            
    except Exception as e:
        logger.error(
            f"Error in venue notification signal: {str(e)}",
            extra={
                'venue_id': instance.id if hasattr(instance, 'id') else None,
                'created': created,
                'error': str(e)
            }
        )
