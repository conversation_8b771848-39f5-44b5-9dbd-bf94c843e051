# --- Render Blueprint for CozyWish Deployment ---


# --- Database Configuration ---
databases:
  - name: cozywishdb                # Unique name for your database instance
    plan: basic-256mb               # Resource plan (adjust as needed)
    databaseName: cozywish          # DB name inside the database server
    user: cozywish                  # DB user


# --- Web Service Configuration ---
services:
  - type: web
    name: cozywish                  # Name of your web service
    plan: starter                   # Resource plan (adjust for traffic/load)
    runtime: python                 # Specify Python runtime
    buildCommand: "./build.sh"      # Custom build script
    startCommand: "gunicorn project_root.wsgi:application"  # Start Django with Gunicorn

    # --- Environment Variables ---
    envVars:

      # --- Application Secrets & Settings ---
      - key: SECRET_KEY
        generateValue: true

      - key: DEBUG
        value: false

      # --- Database Connection ---
      - key: DATABASE_URL
        fromDatabase:
          name: cozywishdb
          property: connectionString

      # --- Web Server Settings ---
      - key: WEB_CONCURRENCY
        value: "4"

      # --- Logging Level ---
      - key: LOG_LEVEL
        value: INFO 




# Notes:
# - Indentation and comments are consistent for readability.
# - You can add more environment variables as needed under envVars.
# - For production, make sure all sensitive info (email creds, API keys, etc.)
#   are managed with envVars (never hard-coded).
# - Adjust plans based on your usage and traffic.
