"""
Management command to test the welcome email functionality.
"""
from django.core.management.base import BaseCommand
from django.conf import settings
from utility_app.email_utils import send_welcome_email


class Command(BaseCommand):
    """Test the welcome email functionality."""
    
    help = 'Test the welcome email functionality for customer registration'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            'recipient_email',
            type=str,
            help='Email address to send test welcome email to'
        )
        parser.add_argument(
            '--name',
            type=str,
            default='Test User',
            help='Name to use in the welcome email (default: "Test User")'
        )

    def handle(self, *args, **options):
        """Execute the command."""
        recipient_email = options['recipient_email']
        user_name = options['name']
        
        self.stdout.write(
            self.style.SUCCESS(f'Testing welcome email functionality...')
        )
        self.stdout.write(f'   📧 To: {recipient_email}')
        self.stdout.write(f'   👤 Name: {user_name}')
        
        # Display current email configuration
        self.stdout.write('\n📋 Current Email Configuration:')
        self.stdout.write(f'   Backend: {settings.EMAIL_BACKEND}')
        self.stdout.write(f'   Host: {settings.EMAIL_HOST}')
        self.stdout.write(f'   Port: {settings.EMAIL_PORT}')
        self.stdout.write(f'   TLS: {settings.EMAIL_USE_TLS}')
        self.stdout.write(f'   From Email: {settings.DEFAULT_FROM_EMAIL}')
        
        if settings.EMAIL_BACKEND == 'django.core.mail.backends.console.EmailBackend':
            self.stdout.write(
                self.style.WARNING('\n⚠️  Using console email backend - emails will be printed to console')
            )
        elif not settings.EMAIL_HOST_PASSWORD:
            self.stdout.write(
                self.style.WARNING('\n⚠️  EMAIL_HOST_PASSWORD not configured - emails may not be sent')
            )
        
        self.stdout.write('\n🚀 Sending welcome email...')
        
        try:
            # Test the welcome email function
            success = send_welcome_email(recipient_email, user_name)
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS('\n✅ Welcome email sent successfully!')
                )
                
                if settings.EMAIL_BACKEND == 'django.core.mail.backends.console.EmailBackend':
                    self.stdout.write(
                        self.style.SUCCESS('📺 Check the console output above for the email content')
                    )
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f'📬 Email should be delivered to {recipient_email}')
                    )
                    self.stdout.write('💡 If you don\'t receive the email, check:')
                    self.stdout.write('   - Spam/junk folder')
                    self.stdout.write('   - Email address spelling')
                    self.stdout.write('   - SendGrid account status')
                    
            else:
                self.stdout.write(
                    self.style.ERROR('\n❌ Failed to send welcome email')
                )
                self.stdout.write('💡 Check the logs for detailed error information')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'\n❌ Error testing welcome email: {str(e)}')
            )
