"""
Master management command to seed all apps with realistic test data.
Performs full database reset before seeding and includes production safety checks.
"""
import os
import shutil
import subprocess
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.db import transaction, connection
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from accounts_app.models import CustomerProfile, ServiceProviderProfile, TeamMember
from venues_app.models import Category, Venue, Service
from booking_cart_app.models import Booking, Cart
from review_app.models import Review
from discount_app.models import VenueDiscount, ServiceDiscount, PlatformDiscount
from payments_app.models import Payment
from notifications_app.models import Notification

User = get_user_model()


class Command(BaseCommand):
    """Master command to seed all CozyWish apps with test data."""

    help = 'Seed all CozyWish apps with realistic test data (DEVELOPMENT ONLY)'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--reset-db',
            action='store_true',
            help='Perform full database reset before seeding (recommended)',
        )
        parser.add_argument(
            '--accounts-only',
            action='store_true',
            help='Seed only accounts_app data',
        )
        parser.add_argument(
            '--venues-only',
            action='store_true',
            help='Seed only venues_app data',
        )
        parser.add_argument(
            '--force-production',
            action='store_true',
            help='Force run in production (NOT RECOMMENDED)',
        )

    def handle(self, *args, **options):
        """Execute the command."""
        # Production safety check
        self.check_production_safety(options)

        self.stdout.write(
            self.style.SUCCESS('🌱 Starting CozyWish complete data seeding...')
        )

        # Perform database reset if requested
        if options['reset_db']:
            self.perform_database_reset()

        # Show current data counts before seeding
        self.show_current_data_counts('Before seeding')

        try:
            # Disable threading for notifications during seeding to avoid DB locks
            self.disable_async_notifications()

            with transaction.atomic():
                if options['accounts_only']:
                    self.seed_accounts_only()
                elif options['venues_only']:
                    self.seed_venues_only()
                else:
                    self.seed_all_data()

            # Show final data counts
            self.show_current_data_counts('After seeding')

            # Run basic validation tests
            self.run_validation_tests()

            self.stdout.write(
                self.style.SUCCESS('✅ Complete CozyWish data seeding finished successfully!')
            )

            # Show usage examples
            self.show_usage_examples()

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Seeding failed: {str(e)}')
            )
            raise
        finally:
            # Re-enable async notifications
            self.enable_async_notifications()

    def seed_accounts_only(self):
        """Seed only accounts app data."""
        self.stdout.write('📋 Seeding accounts_app data only...')
        call_command('seed_data_accounts_app', clear=True)

    def seed_venues_only(self):
        """Seed only venues app data."""
        self.stdout.write('📋 Seeding venues_app data only...')
        call_command('seed_data_venues_app', clear=True)

    def seed_all_data(self):
        """Seed all app data in correct order."""
        self.stdout.write('📋 Seeding all CozyWish app data...')

        # Seed accounts first (venues depend on service providers)
        self.stdout.write('1️⃣ Seeding accounts_app...')
        call_command('seed_data_accounts_app', clear=True)

        # Then seed venues
        self.stdout.write('2️⃣ Seeding venues_app...')
        call_command('seed_data_venues_app', clear=True)

        # Seed discounts (depends on venues and services)
        self.stdout.write('3️⃣ Seeding discount_app...')
        call_command('seed_data_discount_app', clear=True)

        # Seed bookings and carts (depends on customers, venues, services)
        self.stdout.write('4️⃣ Seeding booking_cart_app...')
        call_command('seed_data_booking_cart_app', clear=True)

        # Seed payments (depends on bookings)
        self.stdout.write('5️⃣ Seeding payments_app...')
        call_command('seed_data_payments_app', clear=True)

        # Seed reviews (depends on customers, venues, bookings)
        self.stdout.write('6️⃣ Seeding review_app...')
        call_command('seed_data_review_app', clear=True)

        # Seed notifications (depends on users, bookings, reviews)
        self.stdout.write('7️⃣ Seeding notifications_app...')
        call_command('seed_data_notifications_app', clear=True)

        # Seed admin content (can be done last)
        self.stdout.write('8️⃣ Seeding admin_app...')
        call_command('seed_data_admin_app', clear=True)

    def check_production_safety(self, options):
        """Check if running in production and prevent if not forced."""
        if not settings.DEBUG and not options.get('force_production'):
            raise CommandError(
                "🚨 PRODUCTION SAFETY: This command is designed for development only!\n"
                "It will completely wipe your database and seed with test data.\n"
                "If you really need to run this in production (NOT RECOMMENDED), "
                "use --force-production flag.\n"
                f"Current DEBUG setting: {settings.DEBUG}"
            )

        if not settings.DEBUG:
            self.stdout.write(
                self.style.WARNING(
                    "⚠️  WARNING: Running seed_data in production environment! "
                    "This will replace all data with test data."
                )
            )

    def perform_database_reset(self):
        """Perform complete database reset."""
        self.stdout.write(
            self.style.WARNING('🔄 Performing complete database reset...')
        )

        # Clear cache
        self.stdout.write('🧹 Clearing cache...')
        cache.clear()

        # Clear media files
        self.clear_media_files()

        # Reset database
        self.reset_database()

        # Apply migrations
        self.stdout.write('⚙️  Applying migrations...')
        call_command('migrate', verbosity=0)

        # Create superuser
        self.create_superuser()

        self.stdout.write(
            self.style.SUCCESS('✅ Database reset completed!')
        )

    def clear_media_files(self):
        """Clear all media files."""
        self.stdout.write('🖼️  Clearing media files...')
        media_root = Path(settings.MEDIA_ROOT)
        if media_root.exists():
            for item in media_root.iterdir():
                if item.is_file():
                    item.unlink()
                elif item.is_dir():
                    shutil.rmtree(item)
        self.stdout.write('   ✅ Media files cleared')

    def reset_database(self):
        """Reset the database based on the database backend."""
        db_config = settings.DATABASES['default']

        if db_config['ENGINE'] == 'django.db.backends.sqlite3':
            self.reset_sqlite_database(db_config)
        elif 'postgresql' in db_config['ENGINE']:
            self.reset_postgresql_database(db_config)
        else:
            self.stdout.write(
                self.style.WARNING(
                    f"⚠️  Database engine {db_config['ENGINE']} not fully supported. "
                    "Using Django flush instead."
                )
            )
            call_command('flush', interactive=False, verbosity=0)

    def reset_sqlite_database(self, db_config):
        """Reset SQLite database by deleting the file."""
        self.stdout.write('🗑️  Deleting SQLite database file...')
        db_path = Path(db_config['NAME'])
        if db_path.exists():
            db_path.unlink()
        self.stdout.write('   ✅ SQLite database file deleted')

    def reset_postgresql_database(self, db_config):
        """Reset PostgreSQL database."""
        self.stdout.write('🗑️  Resetting PostgreSQL database...')
        # Close all connections first
        connection.close()

        # Use Django's flush command for PostgreSQL
        call_command('flush', interactive=False, verbosity=0)
        self.stdout.write('   ✅ PostgreSQL database reset')

    def create_superuser(self):
        """Create a superuser for development."""
        self.stdout.write('👤 Creating development superuser...')
        try:
            email = '<EMAIL>'
            password = '123'

            if not User.objects.filter(email=email).exists():
                User.objects.create_superuser(email=email, password=password)
                self.stdout.write(f'   ✅ Superuser created: {email} / {password}')
            else:
                self.stdout.write(f'   ⚠️  Superuser already exists: {email}')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'   ⚠️  Could not create superuser: {e}')
            )

    def disable_async_notifications(self):
        """Disable async notifications to prevent database locking during seeding."""
        # Set environment variable to disable async notifications
        os.environ['NOTIFICATIONS_ASYNC'] = 'False'

    def enable_async_notifications(self):
        """Re-enable async notifications after seeding."""
        # Remove the environment variable
        os.environ.pop('NOTIFICATIONS_ASYNC', None)

    def run_validation_tests(self):
        """Run basic validation tests to ensure seeding was successful."""
        self.stdout.write('\n🧪 Running validation tests...')

        errors = []

        # Test 1: Check if users exist
        if User.objects.count() == 0:
            errors.append("No users found in database")

        # Test 2: Check if venues exist
        if Venue.objects.count() == 0:
            errors.append("No venues found in database")

        # Test 3: Check if services exist
        if Service.objects.count() == 0:
            errors.append("No services found in database")

        # Test 4: Check if categories exist
        if Category.objects.count() == 0:
            errors.append("No categories found in database")

        # Test 5: Check if there are approved venues
        if Venue.objects.filter(approval_status='approved').count() == 0:
            errors.append("No approved venues found")

        # Test 6: Check if service providers have venues
        providers_without_venues = User.objects.filter(
            role=User.SERVICE_PROVIDER
        ).exclude(
            service_provider_profile__venue__isnull=False
        ).count()

        if providers_without_venues > 50:  # Allow some providers without venues
            errors.append(f"Too many service providers without venues: {providers_without_venues}")

        if errors:
            self.stdout.write(
                self.style.ERROR(f'❌ Validation failed with {len(errors)} errors:')
            )
            for error in errors:
                self.stdout.write(f'   • {error}')
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ All validation tests passed!')
            )

    def show_current_data_counts(self, title):
        """Display current data counts in the database."""
        self.stdout.write(f'\n📊 {title}:')
        self.stdout.write('=' * 50)
        
        # Users
        total_users = User.objects.count()
        customers = User.objects.filter(role=User.CUSTOMER).count()
        providers = User.objects.filter(role=User.SERVICE_PROVIDER).count()
        superusers = User.objects.filter(is_superuser=True).count()
        
        self.stdout.write(f'👥 Users: {total_users} total')
        self.stdout.write(f'   - Customers: {customers}')
        self.stdout.write(f'   - Service Providers: {providers}')
        self.stdout.write(f'   - Superusers: {superusers}')
        
        # Profiles
        customer_profiles = CustomerProfile.objects.count()
        provider_profiles = ServiceProviderProfile.objects.count()
        team_members = TeamMember.objects.count()
        
        self.stdout.write(f'📝 Profiles:')
        self.stdout.write(f'   - Customer Profiles: {customer_profiles}')
        self.stdout.write(f'   - Provider Profiles: {provider_profiles}')
        self.stdout.write(f'   - Team Members: {team_members}')
        
        # Venues and Services
        categories = Category.objects.count()
        venues = Venue.objects.count()
        approved_venues = Venue.objects.filter(approval_status='approved').count()
        active_venues = Venue.objects.filter(visibility='active').count()
        services = Service.objects.count()
        active_services = Service.objects.filter(is_active=True).count()

        self.stdout.write(f'🏢 Venues & Services:')
        self.stdout.write(f'   - Categories: {categories}')
        self.stdout.write(f'   - Venues: {venues} total')
        self.stdout.write(f'     • Approved: {approved_venues}')
        self.stdout.write(f'     • Active: {active_venues}')
        self.stdout.write(f'   - Services: {services} total')
        self.stdout.write(f'     • Active: {active_services}')

        # Bookings and Carts
        bookings = Booking.objects.count()
        confirmed_bookings = Booking.objects.filter(status='confirmed').count()
        carts = Cart.objects.count()

        self.stdout.write(f'📅 Bookings & Carts:')
        self.stdout.write(f'   - Bookings: {bookings} total')
        self.stdout.write(f'     • Confirmed: {confirmed_bookings}')
        self.stdout.write(f'   - Active Carts: {carts}')

        # Reviews and Ratings
        reviews = Review.objects.count()
        approved_reviews = Review.objects.filter(is_approved=True).count()

        self.stdout.write(f'⭐ Reviews:')
        self.stdout.write(f'   - Reviews: {reviews} total')
        self.stdout.write(f'     • Approved: {approved_reviews}')

        # Discounts
        venue_discounts = VenueDiscount.objects.count()
        service_discounts = ServiceDiscount.objects.count()
        platform_discounts = PlatformDiscount.objects.count()

        self.stdout.write(f'💰 Discounts:')
        self.stdout.write(f'   - Venue Discounts: {venue_discounts}')
        self.stdout.write(f'   - Service Discounts: {service_discounts}')
        self.stdout.write(f'   - Platform Discounts: {platform_discounts}')

        # Payments
        payments = Payment.objects.count()
        successful_payments = Payment.objects.filter(payment_status='succeeded').count()

        self.stdout.write(f'💳 Payments:')
        self.stdout.write(f'   - Payments: {payments} total')
        self.stdout.write(f'     • Successful: {successful_payments}')

        # Notifications
        notifications = Notification.objects.count()
        unread_notifications = Notification.objects.filter(read_status=Notification.UNREAD).count()

        self.stdout.write(f'🔔 Notifications:')
        self.stdout.write(f'   - Notifications: {notifications} total')
        self.stdout.write(f'     • Unread: {unread_notifications}')

        self.stdout.write('=' * 50)

    def show_usage_examples(self):
        """Show usage examples and next steps."""
        self.stdout.write('\n🎯 Next Steps & Usage Examples:')
        self.stdout.write('=' * 50)
        
        self.stdout.write('🔐 Test Login Credentials:')
        self.stdout.write('   Customers:')
        self.stdout.write('   - <EMAIL> / testpass123')
        self.stdout.write('   - <EMAIL> / testpass123')
        self.stdout.write('   - <EMAIL> / testpass123')
        self.stdout.write('')
        self.stdout.write('   Service Providers:')
        self.stdout.write('   - <EMAIL> / testpass123')
        self.stdout.write('   - <EMAIL> / testpass123')
        self.stdout.write('   - <EMAIL> / testpass123')
        self.stdout.write('   - <EMAIL> / testpass123')
        self.stdout.write('   - <EMAIL> / testpass123')
        
        self.stdout.write('\n🚀 Development Server:')
        self.stdout.write('   python manage.py runserver')
        self.stdout.write('   # or')
        self.stdout.write('   make run')
        
        self.stdout.write('\n🧪 Testing:')
        self.stdout.write('   python manage.py test')
        
        self.stdout.write('\n🔄 Seeding Options:')
        self.stdout.write('   # Full database reset and seed (RECOMMENDED)')
        self.stdout.write('   python manage.py seed_data --reset-db')
        self.stdout.write('')
        self.stdout.write('   # Seed without database reset (may cause conflicts)')
        self.stdout.write('   python manage.py seed_data')
        self.stdout.write('')
        self.stdout.write('   # Seed only specific apps')
        self.stdout.write('   python manage.py seed_data --accounts-only')
        self.stdout.write('   python manage.py seed_data --venues-only')
        self.stdout.write('')
        self.stdout.write('   # Individual app commands')
        self.stdout.write('   python manage.py seed_data_accounts_app --clear')
        self.stdout.write('   python manage.py seed_data_venues_app --clear')
        self.stdout.write('   python manage.py seed_data_discount_app --clear')
        self.stdout.write('   python manage.py seed_data_booking_cart_app --clear')
        self.stdout.write('   python manage.py seed_data_payments_app --clear')
        self.stdout.write('   python manage.py seed_data_review_app --clear')
        self.stdout.write('   python manage.py seed_data_notifications_app --clear')
        self.stdout.write('   python manage.py seed_data_admin_app --clear')
        self.stdout.write('')
        self.stdout.write('⚠️  IMPORTANT: Always use --reset-db for clean seeding!')
        self.stdout.write('   This prevents data conflicts and ensures consistent results.')
        
        self.stdout.write('\n📊 Database Inspection:')
        self.stdout.write('   python manage.py shell')
        self.stdout.write('   >>> from accounts_app.models import *')
        self.stdout.write('   >>> from venues_app.models import *')
        self.stdout.write('   >>> User.objects.count()')
        self.stdout.write('   >>> Venue.objects.count()')
        
        self.stdout.write('=' * 50)
        self.stdout.write('🎉 Happy coding with CozyWish!')
