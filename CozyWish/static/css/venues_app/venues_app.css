:root {
  --brand-primary: black;
  --brand-secondary: white;
  --brand-text: black;
  --brand-text-inverse: white;
}

.card {
  border: 2px solid var(--brand-primary);
  border-radius: 0.5rem;
  background-color: white;
}

.btn-primary,
.btn-success,
.btn-danger {
  background-color: var(--brand-secondary) !important;
  border-color: var(--brand-primary) !important;
  color: var(--brand-text) !important;
}

.btn-primary:hover,
.btn-success:hover,
.btn-danger:hover {
  background-color: #f8f9fa !important;
  border-color: var(--brand-primary) !important;
  color: var(--brand-text) !important;
}

.btn-info,
.btn-warning {
  background-color: var(--brand-secondary) !important;
  border-color: var(--brand-primary) !important;
  color: var(--brand-text) !important;
}

.btn-info:hover,
.btn-warning:hover {
  background-color: var(--brand-primary) !important;
  border-color: var(--brand-primary) !important;
  color: var(--brand-text-inverse) !important;
}

.btn-outline-primary,
.btn-outline-success,
.btn-outline-info,
.btn-outline-warning,
.btn-outline-danger {
  background-color: var(--brand-secondary) !important;
  border-color: var(--brand-primary) !important;
  color: var(--brand-text) !important;
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover,
.btn-outline-danger:hover {
  background-color: var(--brand-primary) !important;
  border-color: var(--brand-primary) !important;
  color: var(--brand-text-inverse) !important;
}

.form-control {
  min-height: 3rem;
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.form-control:focus {
  background-color: white !important;
  color: black !important;
  border-color: black !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1) !important;
}

.input-group-text {
  background-color: var(--brand-secondary) !important;
  border-color: var(--brand-primary) !important;
  color: var(--brand-text) !important;
}

/* Badge styles for better visibility */
.badge {
  font-weight: 500 !important;
}

.badge.bg-success {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.badge.bg-secondary {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.badge.bg-primary {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.badge.bg-info,
.badge.bg-warning {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.badge.bg-danger {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

/* Text color fixes */
.text-muted {
  color: rgba(0, 0, 0, 0.6) !important;
}

.text-success,
.text-danger,
.text-info,
.text-warning {
  color: black !important;
}
