document.addEventListener('DOMContentLoaded', function() {
    const skeleton = document.getElementById('booking-skeleton');
    const results = document.getElementById('booking-results') || document.getElementById('bookings-results');
    if (skeleton && results) {
        setTimeout(() => {
            skeleton.style.display = 'none';
            results.style.display = '';
        }, 300);
    }

    const searchInput = document.getElementById('booking-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const term = this.value.toLowerCase();
            document.querySelectorAll('#bookings-results .card').forEach(card => {
                const text = card.textContent.toLowerCase();
                card.style.display = text.includes(term) ? '' : 'none';
            });
        });
    }
});
