/**
 * CozyWish Notifications App - Accessibility Enhancements
 * Keyboard navigation, screen reader support, and ARIA live regions
 */

class NotificationAccessibility {
    constructor() {
        this.currentFocusIndex = -1;
        this.focusableElements = [];
        this.announcements = [];
        this.init();
    }

    init() {
        this.setupKeyboardNavigation();
        this.setupAriaLiveRegions();
        this.setupFocusManagement();
        this.setupScreenReaderAnnouncements();
        this.enhanceFormAccessibility();
    }

    // ===== KEYBOARD NAVIGATION =====
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Handle notification card navigation
            if (e.target.closest('.notification-card')) {
                this.handleNotificationCardKeys(e);
            }
            
            // Handle dropdown navigation
            if (e.target.closest('.professional-notification-dropdown')) {
                this.handleDropdownKeys(e);
            }

            // Handle form navigation
            if (e.target.closest('form')) {
                this.handleFormKeys(e);
            }

            // Global shortcuts
            this.handleGlobalKeys(e);
        });

        // Update focusable elements when DOM changes
        this.updateFocusableElements();
        
        // Re-scan on dynamic content changes
        const observer = new MutationObserver(() => {
            this.updateFocusableElements();
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    handleNotificationCardKeys(e) {
        const card = e.target.closest('.notification-card');
        
        switch(e.key) {
            case 'Enter':
            case ' ':
                if (e.target === card) {
                    e.preventDefault();
                    const viewLink = card.querySelector('.btn[href*="notification_detail"]');
                    if (viewLink) {
                        viewLink.click();
                    }
                }
                break;
                
            case 'ArrowDown':
                e.preventDefault();
                this.focusNextNotification(card);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.focusPreviousNotification(card);
                break;
                
            case 'Delete':
            case 'Backspace':
                if (e.ctrlKey) {
                    e.preventDefault();
                    const deleteBtn = card.querySelector('.btn-outline-danger');
                    if (deleteBtn) {
                        deleteBtn.click();
                    }
                }
                break;
                
            case 'm':
                if (e.ctrlKey) {
                    e.preventDefault();
                    const markBtn = card.querySelector('.btn-outline-success, .btn-outline-secondary');
                    if (markBtn) {
                        markBtn.click();
                    }
                }
                break;
        }
    }

    handleDropdownKeys(e) {
        const dropdown = e.target.closest('.professional-notification-dropdown');
        const items = dropdown.querySelectorAll('.professional-notification-item');
        
        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.focusNextDropdownItem(items);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.focusPreviousDropdownItem(items);
                break;
                
            case 'Escape':
                e.preventDefault();
                const trigger = document.querySelector('[data-bs-toggle="dropdown"]');
                if (trigger) {
                    bootstrap.Dropdown.getInstance(trigger).hide();
                    trigger.focus();
                }
                break;
        }
    }

    handleFormKeys(e) {
        // Enhanced form navigation
        if (e.key === 'Enter' && e.target.type !== 'textarea') {
            // Allow form submission on Enter for most inputs
            const form = e.target.closest('form');
            if (form && !e.target.closest('.dropdown')) {
                // Don't auto-submit if in a dropdown or specific input types
                if (!['select', 'checkbox', 'radio'].includes(e.target.type)) {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn && !submitBtn.disabled) {
                        submitBtn.click();
                    }
                }
            }
        }
    }

    handleGlobalKeys(e) {
        // Global keyboard shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case '/':
                    e.preventDefault();
                    const searchInput = document.querySelector('#search-input');
                    if (searchInput) {
                        searchInput.focus();
                        this.announceToScreenReader('Search field focused');
                    }
                    break;
                    
                case 'a':
                    if (e.shiftKey) {
                        e.preventDefault();
                        const selectAllBtn = document.querySelector('#selectAllBtn');
                        if (selectAllBtn) {
                            selectAllBtn.click();
                        }
                    }
                    break;
            }
        }
        
        // Escape key handling
        if (e.key === 'Escape') {
            this.handleEscapeKey();
        }
    }

    // ===== FOCUS MANAGEMENT =====
    updateFocusableElements() {
        this.focusableElements = Array.from(document.querySelectorAll(
            'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
        ));
    }

    focusNextNotification(currentCard) {
        const cards = Array.from(document.querySelectorAll('.notification-card'));
        const currentIndex = cards.indexOf(currentCard);
        const nextCard = cards[currentIndex + 1];
        
        if (nextCard) {
            nextCard.focus();
            this.announceToScreenReader(`Focused notification ${currentIndex + 2} of ${cards.length}`);
        }
    }

    focusPreviousNotification(currentCard) {
        const cards = Array.from(document.querySelectorAll('.notification-card'));
        const currentIndex = cards.indexOf(currentCard);
        const prevCard = cards[currentIndex - 1];
        
        if (prevCard) {
            prevCard.focus();
            this.announceToScreenReader(`Focused notification ${currentIndex} of ${cards.length}`);
        }
    }

    focusNextDropdownItem(items) {
        const focused = document.activeElement;
        const currentIndex = Array.from(items).indexOf(focused);
        const nextItem = items[currentIndex + 1] || items[0];
        
        if (nextItem) {
            nextItem.focus();
        }
    }

    focusPreviousDropdownItem(items) {
        const focused = document.activeElement;
        const currentIndex = Array.from(items).indexOf(focused);
        const prevItem = items[currentIndex - 1] || items[items.length - 1];
        
        if (prevItem) {
            prevItem.focus();
        }
    }

    handleEscapeKey() {
        // Close modals, dropdowns, etc.
        const activeModal = document.querySelector('.modal.show');
        if (activeModal) {
            const modal = bootstrap.Modal.getInstance(activeModal);
            if (modal) modal.hide();
            return;
        }

        const activeDropdown = document.querySelector('.dropdown-menu.show');
        if (activeDropdown) {
            const trigger = document.querySelector('[aria-expanded="true"]');
            if (trigger) {
                bootstrap.Dropdown.getInstance(trigger).hide();
                trigger.focus();
            }
            return;
        }

        // Clear search if focused
        const searchInput = document.querySelector('#search-input');
        if (document.activeElement === searchInput && searchInput.value) {
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('input'));
        }
    }

    // ===== ARIA LIVE REGIONS =====
    setupAriaLiveRegions() {
        // Create live region for announcements
        if (!document.querySelector('#aria-live-region')) {
            const liveRegion = document.createElement('div');
            liveRegion.id = 'aria-live-region';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.className = 'visually-hidden';
            document.body.appendChild(liveRegion);
        }

        // Create assertive live region for urgent announcements
        if (!document.querySelector('#aria-live-assertive')) {
            const assertiveRegion = document.createElement('div');
            assertiveRegion.id = 'aria-live-assertive';
            assertiveRegion.setAttribute('aria-live', 'assertive');
            assertiveRegion.setAttribute('aria-atomic', 'true');
            assertiveRegion.className = 'visually-hidden';
            document.body.appendChild(assertiveRegion);
        }
    }

    announceToScreenReader(message, urgent = false) {
        const regionId = urgent ? '#aria-live-assertive' : '#aria-live-region';
        const region = document.querySelector(regionId);
        
        if (region) {
            // Clear previous message
            region.textContent = '';
            
            // Add new message after a brief delay to ensure it's announced
            setTimeout(() => {
                region.textContent = message;
            }, 100);
            
            // Clear message after announcement
            setTimeout(() => {
                region.textContent = '';
            }, 3000);
        }
    }

    // ===== SCREEN READER ENHANCEMENTS =====
    setupScreenReaderAnnouncements() {
        // Announce page changes
        this.announcePageLoad();
        
        // Announce dynamic content changes
        this.observeContentChanges();
        
        // Announce form validation results
        this.setupFormValidationAnnouncements();
    }

    announcePageLoad() {
        const pageTitle = document.querySelector('#notifications-main-heading');
        const notificationCount = document.querySelectorAll('.notification-card').length;
        const unreadCount = document.querySelector('#unreadCountStat')?.textContent || '0';
        
        setTimeout(() => {
            this.announceToScreenReader(
                `Notifications page loaded. ${notificationCount} total notifications, ${unreadCount} unread.`
            );
        }, 1000);
    }

    observeContentChanges() {
        // Observe notification list changes
        const notificationList = document.querySelector('#notification-list');
        if (notificationList) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === 1 && node.classList.contains('notification-card')) {
                                this.announceToScreenReader('New notification added');
                            }
                        });
                        
                        mutation.removedNodes.forEach((node) => {
                            if (node.nodeType === 1 && node.classList.contains('notification-card')) {
                                this.announceToScreenReader('Notification removed');
                            }
                        });
                    }
                });
            });
            
            observer.observe(notificationList, { childList: true });
        }
    }

    setupFormValidationAnnouncements() {
        // Announce validation errors
        document.addEventListener('invalid', (e) => {
            const field = e.target;
            const label = field.labels?.[0]?.textContent || field.name || 'Field';
            this.announceToScreenReader(`${label} has an error: ${field.validationMessage}`, true);
        });
    }

    // ===== FORM ACCESSIBILITY ENHANCEMENTS =====
    enhanceFormAccessibility() {
        // Add proper labels and descriptions
        this.enhanceFormLabels();
        
        // Improve error handling
        this.enhanceErrorHandling();
        
        // Add helpful instructions
        this.addFormInstructions();
    }

    enhanceFormLabels() {
        // Ensure all form controls have proper labels
        const unlabeledInputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
        unlabeledInputs.forEach(input => {
            if (!input.labels || input.labels.length === 0) {
                const placeholder = input.placeholder;
                if (placeholder) {
                    input.setAttribute('aria-label', placeholder);
                }
            }
        });
    }

    enhanceErrorHandling() {
        // Improve error message accessibility
        const errorMessages = document.querySelectorAll('.invalid-feedback');
        errorMessages.forEach(error => {
            if (!error.id) {
                error.id = 'error-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            }
            
            const field = error.previousElementSibling;
            if (field && field.tagName === 'INPUT') {
                field.setAttribute('aria-describedby', error.id);
            }
        });
    }

    addFormInstructions() {
        // Add keyboard shortcut instructions
        const searchForm = document.querySelector('form[role="search"]');
        if (searchForm && !document.querySelector('#keyboard-instructions')) {
            const instructions = document.createElement('div');
            instructions.id = 'keyboard-instructions';
            instructions.className = 'visually-hidden';
            instructions.textContent = 'Keyboard shortcuts: Ctrl+/ to focus search, Ctrl+Shift+A to select all, Arrow keys to navigate notifications';
            searchForm.appendChild(instructions);
        }
    }
}

// Initialize accessibility enhancements when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new NotificationAccessibility();
});

// Export for testing
window.NotificationAccessibility = NotificationAccessibility;
