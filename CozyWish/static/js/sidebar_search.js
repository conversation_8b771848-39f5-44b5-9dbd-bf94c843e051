document.addEventListener('DOMContentLoaded', function () {
    const input = document.getElementById('sidebarSearch');
    if (!input) return;
    const items = document.querySelectorAll('#adminSidebar li.nav-item');
    input.addEventListener('input', function () {
        const query = this.value.toLowerCase();
        items.forEach(function (li) {
            const text = li.textContent.toLowerCase();
            li.style.display = text.includes(query) ? '' : 'none';
        });
    });
});
