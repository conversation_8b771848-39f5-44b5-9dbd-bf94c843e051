#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to set up AWS S3 bucket with proper configuration for CozyWish.
This script will:
1. Create the bucket if it doesn't exist
2. Set up proper CORS configuration
3. Set up bucket policy for public read access
4. Verify the configuration
"""

import boto3
import json
import os
from botocore.exceptions import ClientError


def get_s3_client():
    """Create and return S3 client."""
    return boto3.client(
        's3',
        aws_access_key_id=os.environ.get('AWS_ACCESS_KEY_ID'),
        aws_secret_access_key=os.environ.get('AWS_SECRET_ACCESS_KEY'),
        region_name=os.environ.get('AWS_S3_REGION_NAME', 'us-east-1')
    )


def create_bucket_if_not_exists(s3_client, bucket_name, region):
    """Create S3 bucket if it doesn't exist."""
    try:
        # Check if bucket exists
        s3_client.head_bucket(Bucket=bucket_name)
        print(f"✓ Bucket '{bucket_name}' already exists")
        return True
    except ClientError as e:
        error_code = int(e.response['Error']['Code'])
        if error_code == 404:
            # Bucket doesn't exist, create it
            try:
                if region == 'us-east-1':
                    # us-east-1 doesn't need LocationConstraint
                    s3_client.create_bucket(Bucket=bucket_name)
                else:
                    s3_client.create_bucket(
                        Bucket=bucket_name,
                        CreateBucketConfiguration={'LocationConstraint': region}
                    )
                print(f"✓ Created bucket '{bucket_name}' in region '{region}'")
                return True
            except ClientError as create_error:
                print(f"✗ Failed to create bucket: {create_error}")
                return False
        else:
            print(f"✗ Error checking bucket: {e}")
            return False


def setup_cors_configuration(s3_client, bucket_name):
    """Set up CORS configuration for the bucket."""
    cors_configuration = {
        'CORSRules': [
            {
                'AllowedHeaders': ['*'],
                'AllowedMethods': ['GET', 'PUT', 'POST', 'DELETE', 'HEAD'],
                'AllowedOrigins': [
                    'https://cozywish.onrender.com',
                    'https://www.cozywish.com',
                    'https://cozywish.com',
                    'http://localhost:8000',  # For local development
                    'http://127.0.0.1:8000'   # For local development
                ],
                'ExposeHeaders': ['ETag'],
                'MaxAgeSeconds': 3000
            }
        ]
    }
    
    try:
        s3_client.put_bucket_cors(
            Bucket=bucket_name,
            CORSConfiguration=cors_configuration
        )
        print("✓ CORS configuration set successfully")
        return True
    except ClientError as e:
        print(f"✗ Failed to set CORS configuration: {e}")
        return False


def setup_bucket_policy(s3_client, bucket_name):
    """Set up bucket policy for public read access to uploaded files."""
    # This policy allows public read access to all objects in the bucket
    # Adjust as needed for your security requirements
    bucket_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Sid": "PublicReadGetObject",
                "Effect": "Allow",
                "Principal": "*",
                "Action": "s3:GetObject",
                "Resource": f"arn:aws:s3:::{bucket_name}/*"
            }
        ]
    }
    
    try:
        s3_client.put_bucket_policy(
            Bucket=bucket_name,
            Policy=json.dumps(bucket_policy)
        )
        print("✓ Bucket policy set successfully (public read access)")
        return True
    except ClientError as e:
        print(f"✗ Failed to set bucket policy: {e}")
        return False


def verify_bucket_configuration(s3_client, bucket_name):
    """Verify the bucket configuration."""
    print("\n=== Verifying Bucket Configuration ===")
    
    # Check bucket location
    try:
        location = s3_client.get_bucket_location(Bucket=bucket_name)
        region = location.get('LocationConstraint') or 'us-east-1'
        print(f"✓ Bucket region: {region}")
    except ClientError as e:
        print(f"✗ Could not get bucket location: {e}")
    
    # Check CORS configuration
    try:
        cors = s3_client.get_bucket_cors(Bucket=bucket_name)
        print("✓ CORS configuration exists")
        if cors.get('CORSRules'):
            print(f"  - {len(cors['CORSRules'])} CORS rule(s) configured")
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchCORSConfiguration':
            print("⚠ No CORS configuration found")
        else:
            print(f"✗ Could not get CORS configuration: {e}")
    
    # Check bucket policy
    try:
        policy = s3_client.get_bucket_policy(Bucket=bucket_name)
        print("✓ Bucket policy exists")
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchBucketPolicy':
            print("⚠ No bucket policy found")
        else:
            print(f"✗ Could not get bucket policy: {e}")
    
    # Test upload permissions
    try:
        test_key = 'test-upload-permissions.txt'
        s3_client.put_object(
            Bucket=bucket_name,
            Key=test_key,
            Body=b'Test upload permissions',
            ContentType='text/plain'
        )
        print("✓ Upload permissions working")
        
        # Clean up test file
        s3_client.delete_object(Bucket=bucket_name, Key=test_key)
        print("✓ Delete permissions working")
        
    except ClientError as e:
        print(f"✗ Upload/delete test failed: {e}")


def main():
    """Main function to set up S3 bucket."""
    print("AWS S3 Bucket Setup for CozyWish")
    print("=" * 40)
    
    # Check environment variables
    required_vars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_STORAGE_BUCKET_NAME']
    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    
    if missing_vars:
        print(f"✗ Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these environment variables and try again.")
        return
    
    bucket_name = os.environ.get('AWS_STORAGE_BUCKET_NAME')
    region = os.environ.get('AWS_S3_REGION_NAME', 'us-east-1')
    
    print(f"Bucket name: {bucket_name}")
    print(f"Region: {region}")
    
    try:
        s3_client = get_s3_client()
        print("✓ Connected to AWS S3")
    except Exception as e:
        print(f"✗ Failed to connect to AWS S3: {e}")
        return
    
    # Create bucket if needed
    if not create_bucket_if_not_exists(s3_client, bucket_name, region):
        print("Failed to create or access bucket. Exiting.")
        return
    
    # Set up CORS configuration
    setup_cors_configuration(s3_client, bucket_name)
    
    # Set up bucket policy
    setup_bucket_policy(s3_client, bucket_name)
    
    # Verify configuration
    verify_bucket_configuration(s3_client, bucket_name)
    
    print("\n" + "=" * 40)
    print("S3 Bucket Setup Complete!")
    print("=" * 40)
    print("\nNext steps:")
    print("1. Test the configuration using: python manage.py test_s3_upload")
    print("2. Try uploading an image through your Django application")
    print("3. Check the Render logs for any error messages")
    
    print(f"\nYour bucket URL: https://{bucket_name}.s3.{region}.amazonaws.com/")
    if os.environ.get('AWS_S3_CUSTOM_DOMAIN'):
        print(f"Custom domain URL: https://{os.environ.get('AWS_S3_CUSTOM_DOMAIN')}/")


if __name__ == "__main__":
    main()
