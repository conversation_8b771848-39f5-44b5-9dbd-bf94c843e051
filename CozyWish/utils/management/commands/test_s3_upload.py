"""
Django management command to test AWS S3 upload functionality.
Usage: python manage.py test_s3_upload
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import SimpleUploadedFile
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
import os
import sys
from io import BytesIO


class Command(BaseCommand):
    help = 'Test AWS S3 upload functionality and diagnose issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )
        parser.add_argument(
            '--test-image',
            action='store_true',
            help='Test image upload processing',
        )

    def handle(self, *args, **options):
        self.verbose = options['verbose']
        self.test_image = options['test_image']
        
        self.stdout.write(
            self.style.SUCCESS('Starting AWS S3 Upload Test')
        )
        
        # Run all tests
        tests_passed = 0
        total_tests = 5
        
        if self.test_environment_variables():
            tests_passed += 1
        
        if self.test_django_settings():
            tests_passed += 1
            
        if self.test_boto3_connection():
            tests_passed += 1
            
        if self.test_django_storage():
            tests_passed += 1
            
        if self.test_image:
            if self.test_image_upload():
                tests_passed += 1
        else:
            total_tests -= 1
        
        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write('TEST SUMMARY')
        self.stdout.write('='*50)
        
        if tests_passed == total_tests:
            self.stdout.write(
                self.style.SUCCESS(f'✓ All {tests_passed}/{total_tests} tests passed!')
            )
            self.stdout.write(
                self.style.SUCCESS('S3 upload functionality should be working correctly.')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'✗ {tests_passed}/{total_tests} tests passed.')
            )
            self.stdout.write(
                self.style.ERROR('There are issues with S3 configuration.')
            )
            self.print_troubleshooting_guide()

    def test_environment_variables(self):
        """Test if all required AWS environment variables are set."""
        self.stdout.write('\n=== Testing Environment Variables ===')
        
        required_vars = [
            'AWS_ACCESS_KEY_ID',
            'AWS_SECRET_ACCESS_KEY', 
            'AWS_STORAGE_BUCKET_NAME',
            'AWS_S3_REGION_NAME'
        ]
        
        missing_vars = []
        for var in required_vars:
            value = os.environ.get(var)
            if value:
                # Mask sensitive values
                if 'SECRET' in var or 'KEY' in var:
                    display_value = f"{value[:4]}...{value[-4:]}" if len(value) > 8 else "***"
                else:
                    display_value = value
                self.stdout.write(f"✓ {var}: {display_value}")
            else:
                self.stdout.write(
                    self.style.ERROR(f"✗ {var}: NOT SET")
                )
                missing_vars.append(var)
        
        if missing_vars:
            self.stdout.write(
                self.style.ERROR(f"Missing environment variables: {', '.join(missing_vars)}")
            )
            return False
        
        self.stdout.write(self.style.SUCCESS("✓ All environment variables are set"))
        return True

    def test_django_settings(self):
        """Test Django settings configuration."""
        self.stdout.write('\n=== Testing Django Settings ===')
        
        self.stdout.write(f"DEBUG: {settings.DEBUG}")
        self.stdout.write(f"DEFAULT_FILE_STORAGE: {getattr(settings, 'DEFAULT_FILE_STORAGE', 'NOT SET')}")
        self.stdout.write(f"MEDIA_URL: {getattr(settings, 'MEDIA_URL', 'NOT SET')}")
        
        if self.verbose:
            self.stdout.write(f"AWS_STORAGE_BUCKET_NAME: {getattr(settings, 'AWS_STORAGE_BUCKET_NAME', 'NOT SET')}")
            self.stdout.write(f"AWS_S3_REGION_NAME: {getattr(settings, 'AWS_S3_REGION_NAME', 'NOT SET')}")
            self.stdout.write(f"AWS_S3_CUSTOM_DOMAIN: {getattr(settings, 'AWS_S3_CUSTOM_DOMAIN', 'NOT SET')}")
        
        # Check if we're using S3 storage
        if hasattr(settings, 'DEFAULT_FILE_STORAGE'):
            if 'S3Boto3Storage' in settings.DEFAULT_FILE_STORAGE:
                self.stdout.write(self.style.SUCCESS("✓ Using S3Boto3Storage"))
                return True
            else:
                self.stdout.write(
                    self.style.ERROR(f"✗ Not using S3 storage: {settings.DEFAULT_FILE_STORAGE}")
                )
                return False
        else:
            self.stdout.write(
                self.style.ERROR("✗ DEFAULT_FILE_STORAGE not configured")
            )
            return False

    def test_boto3_connection(self):
        """Test direct boto3 connection to S3."""
        self.stdout.write('\n=== Testing Boto3 Connection ===')
        
        try:
            # Create S3 client
            s3_client = boto3.client(
                's3',
                aws_access_key_id=os.environ.get('AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=os.environ.get('AWS_SECRET_ACCESS_KEY'),
                region_name=os.environ.get('AWS_S3_REGION_NAME', 'us-east-1')
            )
            
            # Test bucket access
            bucket_name = os.environ.get('AWS_STORAGE_BUCKET_NAME')
            self.stdout.write(f"Testing access to bucket: {bucket_name}")
            
            # List objects (this will fail if credentials are wrong)
            response = s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=1)
            self.stdout.write(self.style.SUCCESS("✓ Successfully connected to S3 bucket"))
            
            # Check bucket location
            location = s3_client.get_bucket_location(Bucket=bucket_name)
            bucket_region = location.get('LocationConstraint') or 'us-east-1'
            self.stdout.write(f"✓ Bucket location: {bucket_region}")
            
            # Verify region matches
            expected_region = os.environ.get('AWS_S3_REGION_NAME', 'us-east-1')
            if bucket_region != expected_region:
                self.stdout.write(
                    self.style.WARNING(
                        f"⚠ Region mismatch: bucket is in {bucket_region}, "
                        f"but AWS_S3_REGION_NAME is {expected_region}"
                    )
                )
            
            return True
            
        except NoCredentialsError:
            self.stdout.write(
                self.style.ERROR("✗ AWS credentials not found or invalid")
            )
            return False
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchBucket':
                self.stdout.write(
                    self.style.ERROR(f"✗ Bucket '{bucket_name}' does not exist")
                )
            elif error_code == 'AccessDenied':
                self.stdout.write(
                    self.style.ERROR("✗ Access denied - check IAM permissions")
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f"✗ AWS Error: {error_code} - {e.response['Error']['Message']}")
                )
            return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"✗ Unexpected error: {str(e)}")
            )
            return False

    def test_django_storage(self):
        """Test Django's default storage."""
        self.stdout.write('\n=== Testing Django Storage ===')

        try:
            # Test basic storage operations
            test_content = "This is a test file for S3 upload debugging."
            test_file = ContentFile(test_content.encode('utf-8'))
            test_path = "debug/test_file.txt"

            self.stdout.write(f"Attempting to save file: {test_path}")

            # Save file
            saved_path = default_storage.save(test_path, test_file)
            self.stdout.write(self.style.SUCCESS(f"✓ File saved successfully: {saved_path}"))

            # Check if file exists
            if default_storage.exists(saved_path):
                self.stdout.write(self.style.SUCCESS("✓ File exists in storage"))
            else:
                self.stdout.write(self.style.ERROR("✗ File does not exist in storage"))
                return False

            # Test URL generation
            try:
                file_url = default_storage.url(saved_path)
                self.stdout.write(self.style.SUCCESS(f"✓ File URL generated: {file_url}"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"✗ Failed to generate URL: {e}"))
                return False
            
            # Get file URL
            try:
                file_url = default_storage.url(saved_path)
                self.stdout.write(f"✓ File URL: {file_url}")
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"✗ Error getting file URL: {str(e)}")
                )
            
            # Clean up - delete test file
            try:
                default_storage.delete(saved_path)
                self.stdout.write(self.style.SUCCESS("✓ Test file cleaned up"))
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f"⚠ Warning: Could not delete test file: {str(e)}")
                )
            
            return True
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"✗ Django storage test failed: {str(e)}")
            )
            if self.verbose:
                import traceback
                self.stdout.write(f"Traceback: {traceback.format_exc()}")
            return False

    def test_image_upload(self):
        """Test image upload processing."""
        self.stdout.write('\n=== Testing Image Upload Processing ===')
        
        try:
            from PIL import Image
            
            # Create a simple test image
            img = Image.new('RGB', (100, 100), color='red')
            img_io = BytesIO()
            img.save(img_io, format='JPEG')
            img_io.seek(0)
            
            # Create uploaded file
            uploaded_file = SimpleUploadedFile(
                "test_image.jpg",
                img_io.getvalue(),
                content_type="image/jpeg"
            )
            
            self.stdout.write(self.style.SUCCESS("✓ Created test image"))
            
            # Test the image processing pipeline
            try:
                from utils.image_utils import process_image
                from utils.image_service import ImageService
                
                self.stdout.write("Testing image processing...")
                processed_data = process_image(
                    image_file=uploaded_file,
                    image_type='profile',
                    entity_type='debug',
                    entity_id='test'
                )
                self.stdout.write(self.style.SUCCESS("✓ Image processed successfully"))
                
                self.stdout.write("Testing image saving...")
                saved_path, metadata = ImageService.save_image(processed_data)
                self.stdout.write(
                    self.style.SUCCESS(f"✓ Image saved successfully: {saved_path}")
                )
                
                # Get image URL
                try:
                    image_url = ImageService.get_image_url(saved_path)
                    self.stdout.write(f"✓ Image URL: {image_url}")
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f"⚠ Could not get image URL: {str(e)}")
                    )
                
                # Clean up
                try:
                    ImageService.delete_image(metadata)
                    self.stdout.write(self.style.SUCCESS("✓ Test image cleaned up"))
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f"⚠ Warning: Could not delete test image: {str(e)}")
                    )
                
                return True
                
            except ImportError as e:
                self.stdout.write(
                    self.style.WARNING(f"⚠ Image processing utilities not available: {str(e)}")
                )
                return False
                
        except ImportError:
            self.stdout.write(
                self.style.WARNING("⚠ PIL (Pillow) not available for image testing")
            )
            return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"✗ Image upload test failed: {str(e)}")
            )
            if self.verbose:
                import traceback
                self.stdout.write(f"Traceback: {traceback.format_exc()}")
            return False

    def print_troubleshooting_guide(self):
        """Print troubleshooting guide for common issues."""
        self.stdout.write('\n' + '='*50)
        self.stdout.write('TROUBLESHOOTING GUIDE')
        self.stdout.write('='*50)
        
        self.stdout.write(
            self.style.WARNING("Common issues and solutions:")
        )
        
        issues = [
            "1. Missing environment variables - Set them in Render dashboard",
            "2. Invalid AWS credentials - Check IAM user permissions",
            "3. Bucket doesn't exist - Create the bucket in AWS S3",
            "4. Wrong region - Ensure bucket region matches AWS_S3_REGION_NAME",
            "5. IAM permissions - Ensure user has s3:GetObject, s3:PutObject, s3:DeleteObject",
            "6. CORS configuration - Check bucket CORS settings for web uploads",
            "7. Bucket policy - Ensure bucket policy allows your IAM user access"
        ]
        
        for issue in issues:
            self.stdout.write(f"   {issue}")
        
        self.stdout.write('\nFor more help, check the AWS S3 documentation or contact support.')
