"""
Management command to test URL resolution.

This command helps debug URL resolution issues during deployment.
"""

from django.core.management.base import BaseCommand
from django.urls import reverse, NoReverseMatch
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Test URL resolution to debug deployment issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--url-name',
            type=str,
            help='Specific URL name to test (e.g., "home")',
        )

    def handle(self, *args, **options):
        """Test URL resolution."""
        self.stdout.write(self.style.SUCCESS('🔍 Testing URL Resolution...'))
        
        # Test specific URL if provided
        if options['url_name']:
            self.test_single_url(options['url_name'])
            return

        # Test common URLs
        test_urls = [
            'home',
            'admin_app:admin_dashboard',
            'venues_app:venue_list',
            'accounts_app:customer_login',
            'dashboard_app:customer_dashboard',
        ]

        success_count = 0
        error_count = 0

        for url_name in test_urls:
            try:
                url = reverse(url_name)
                self.stdout.write(
                    self.style.SUCCESS(f'✅ {url_name}: {url}')
                )
                success_count += 1
            except NoReverseMatch as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ {url_name}: {e}')
                )
                error_count += 1
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ {url_name}: Unexpected error - {e}')
                )
                error_count += 1

        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(f'Total URLs tested: {len(test_urls)}')
        self.stdout.write(f'Successful: {success_count}')
        self.stdout.write(f'Failed: {error_count}')
        
        if error_count == 0:
            self.stdout.write(self.style.SUCCESS('🎉 All URLs resolved successfully!'))
        else:
            self.stdout.write(self.style.ERROR('⚠️  Some URLs failed to resolve.'))

    def test_single_url(self, url_name):
        """Test a single URL."""
        try:
            url = reverse(url_name)
            self.stdout.write(
                self.style.SUCCESS(f'✅ {url_name}: {url}')
            )
        except NoReverseMatch as e:
            self.stdout.write(
                self.style.ERROR(f'❌ {url_name}: {e}')
            )
            # Additional debugging info
            self.stdout.write(f'Settings.ROOT_URLCONF: {settings.ROOT_URLCONF}')
            self.stdout.write(f'Debug mode: {settings.DEBUG}')
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ {url_name}: Unexpected error - {e}')
            )
