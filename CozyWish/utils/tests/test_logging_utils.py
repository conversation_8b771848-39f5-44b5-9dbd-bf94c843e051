from django.test import TestCase, RequestFactory
from unittest.mock import patch

from utils.logging_utils import (
    get_client_info, log_error, log_security_event, log_user_activity
)


class LoggingUtilsEdgeCaseTests(TestCase):
    def setUp(self):
        self.factory = RequestFactory()

    def test_get_client_info_none(self):
        info = get_client_info(None)
        self.assertEqual(info['ip_address'], 'unknown')
        self.assertEqual(info['user_agent'], 'unknown')
        self.assertEqual(info['path'], 'unknown')
        self.assertEqual(info['method'], 'unknown')

    @patch('utils.logging_utils.logging.getLogger')
    def test_log_error_with_exception(self, mock_get_logger):
        mock_logger = mock_get_logger.return_value
        request = self.factory.get('/test')
        exc = ValueError('bad')
        log_error('test_app', 'validation', 'bad value', request=request, exception=exc)
        mock_logger.error.assert_called_once()
        extra = mock_logger.error.call_args[1]['extra']
        self.assertEqual(extra['exception_type'], 'ValueError')
        self.assertIn('bad', extra['exception_message'])
        self.assertIn('traceback', extra)

    @patch('utils.logging_utils.logging.getLogger')
    def test_log_security_event_warning_level(self, mock_get_logger):
        mock_logger = mock_get_logger.return_value
        request = self.factory.get('/')
        log_security_event('test_app', 'xss_attempt', request=request, severity='WARNING')
        mock_logger.warning.assert_called_once()
        extra = mock_logger.warning.call_args[1]['extra']
        self.assertEqual(extra['severity'], 'WARNING')

    @patch('utils.logging_utils.logging.getLogger')
    def test_log_user_activity_no_user(self, mock_get_logger):
        mock_logger = mock_get_logger.return_value
        request = self.factory.get('/')
        log_user_activity('test_app', 'visit', user=None, request=request)
        mock_logger.info.assert_called_once()
        extra = mock_logger.info.call_args[1]['extra']
        self.assertIsNone(extra['user_email'])
