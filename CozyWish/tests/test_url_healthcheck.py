"""
Comprehensive URL Health Check Test Module for CozyWish Django Project.

This module recursively discovers all URL patterns from the root URLConf and tests them
for basic functionality, including:
- URL resolution and reverse lookup
- View execution without unhandled exceptions
- Template rendering without syntax errors
- Authentication requirements handling
- Parameter substitution for dynamic URLs

Usage:
    python manage.py test tests.test_url_healthcheck
    pytest tests/test_url_healthcheck.py -v
"""

import json
import re
import uuid
from datetime import date, timedelta
from decimal import Decimal
from typing import Dict, List, Tuple, Any, Optional

from django.test import TestCase, Client
from django.urls import reverse, resolve, URLPattern, URLResolver
from django.urls.resolvers import RoutePattern, RegexPattern
from django.contrib.auth import get_user_model
from django.core.exceptions import ImproperlyConfigured
from django.template import TemplateDoesNotExist, TemplateSyntaxError
from django.http import Http404
from django.conf import settings
from django.db import transaction
from django.utils import timezone

# Import models for test data creation
from accounts_app.models import CustomerProfile, ServiceProviderProfile, TeamMember
from venues_app.models import Category, Venue, Service, USCity
from booking_cart_app.models import Booking, Cart, CartItem
from review_app.models import Review
from discount_app.models import ServiceDiscount, VenueDiscount, PlatformDiscount
from payments_app.models import Payment
from notifications_app.models import Notification
from admin_app.models import StaticPage, BlogPost, HomepageBlock, MediaFile

User = get_user_model()


class URLHealthCheckTest(TestCase):
    """
    Comprehensive URL health check test that discovers and tests all URL patterns.
    
    This test creates minimal test data, discovers all URLs recursively,
    and tests each URL for basic functionality including authentication,
    parameter handling, and template rendering.
    """
    
    @classmethod
    def setUpClass(cls):
        """Set up test data once for all URL tests."""
        super().setUpClass()
        cls.test_results = []
        cls.client = Client()
        
        # Create test users with different roles
        cls._create_test_users()
        
        # Create minimal test data for URL parameter substitution
        cls._create_test_data()
        
        # Discover all URL patterns
        cls.url_patterns = cls._discover_url_patterns()
    
    @classmethod
    def _create_test_users(cls):
        """Create test users for different authentication scenarios."""
        # Customer user
        cls.customer_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'password': 'testpass123',
                'role': User.CUSTOMER,
                'first_name': 'Test',
                'last_name': 'Customer'
            }
        )
        if created:
            cls.customer_user.set_password('testpass123')
            cls.customer_user.save()

        cls.customer_profile, _ = CustomerProfile.objects.get_or_create(
            user=cls.customer_user,
            defaults={
                'phone_number': '+**********',
                'birth_month': 6,
                'birth_year': 1990
            }
        )

        # Service provider user
        cls.provider_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'password': 'testpass123',
                'role': User.SERVICE_PROVIDER,
                'first_name': 'Test',
                'last_name': 'Provider'
            }
        )
        if created:
            cls.provider_user.set_password('testpass123')
            cls.provider_user.save()

        cls.provider_profile, _ = ServiceProviderProfile.objects.get_or_create(
            user=cls.provider_user,
            defaults={
                'legal_name': 'Test Spa LLC',
                'display_name': 'Test Spa',
                'description': 'Test spa description',
                'phone': '+**********',
                'contact_name': 'Test Provider',
                'address': '123 Test Street',
                'city': 'Test City',
                'state': 'CA',
                'zip_code': '12345'
            }
        )

        # Admin user
        cls.admin_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'password': 'testpass123',
                'role': User.ADMIN,
                'first_name': 'Test',
                'last_name': 'Admin',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            cls.admin_user.set_password('testpass123')
            cls.admin_user.save()
    
    @classmethod
    def _create_test_data(cls):
        """Create minimal test data for URL parameter substitution."""
        # Create US City
        cls.test_city, _ = USCity.objects.get_or_create(
            city_id='ca-test-city',
            defaults={
                'city': 'Test City',
                'state_id': 'CA',
                'state_name': 'California',
                'county_name': 'Test County',
                'latitude': Decimal('37.7749'),
                'longitude': Decimal('-122.4194'),
                'zip_codes': '12345 12346'
            }
        )

        # Create Category
        cls.test_category, _ = Category.objects.get_or_create(
            slug='test-category',
            defaults={
                'category_name': 'Test Category',
                'category_description': 'Test category description'
            }
        )

        # Create Venue
        cls.test_venue, _ = Venue.objects.get_or_create(
            slug='test-venue',
            defaults={
                'venue_name': 'Test Venue',
                'short_description': 'Test venue description',
                'service_provider': cls.provider_profile,
                'street_number': '123',
                'street_name': 'Test Street',
                'city': 'Test City',
                'state': 'CA',
                'county': 'Test County',
                'us_city': cls.test_city,
                'approval_status': Venue.APPROVED,
                'visibility': Venue.ACTIVE
            }
        )
        
        # Create Service
        cls.test_service, _ = Service.objects.get_or_create(
            slug='test-service',
            defaults={
                'service_title': 'Test Service',
                'venue': cls.test_venue,
                'short_description': 'Test service description',
                'price_min': Decimal('100.00'),
                'duration_minutes': 60
            }
        )

        # Create Booking
        cls.test_booking, _ = Booking.objects.get_or_create(
            customer=cls.customer_user,
            venue=cls.test_venue,
            defaults={
                'total_price': Decimal('100.00'),
                'booking_date': date.today() + timedelta(days=7),
                'status': Booking.CONFIRMED
            }
        )

        # Create Review
        cls.test_review, _ = Review.objects.get_or_create(
            customer=cls.customer_user,
            venue=cls.test_venue,
            defaults={
                'rating': 5,
                'written_review': 'Excellent experience at this venue',
                'slug': 'great-service'
            }
        )

        # Create Discount
        from discount_app.models import DiscountType
        cls.test_service_discount, _ = ServiceDiscount.objects.get_or_create(
            slug='test-service-discount',
            defaults={
                'service': cls.test_service,
                'name': 'Test Service Discount',
                'discount_type': DiscountType.PERCENTAGE,
                'discount_value': Decimal('20.00'),
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=30),
                'created_by': cls.provider_user
            }
        )

        # Create Payment
        cls.test_payment, _ = Payment.objects.get_or_create(
            booking=cls.test_booking,
            defaults={
                'customer': cls.customer_user,
                'provider': cls.provider_user,
                'amount_paid': Decimal('100.00'),
                'payment_status': Payment.SUCCEEDED,
                'payment_method': Payment.STRIPE
            }
        )

        # Create Notification
        cls.test_notification, _ = Notification.objects.get_or_create(
            user=cls.customer_user,
            title='Test Notification',
            defaults={
                'message': 'Test notification message',
                'notification_type': Notification.BOOKING
            }
        )
    
    @classmethod
    def _discover_url_patterns(cls) -> List[Dict[str, Any]]:
        """
        Recursively discover all URL patterns from the root URLConf.
        
        Returns:
            List of dictionaries containing URL pattern information
        """
        from django.urls import get_resolver
        
        patterns = []
        root_resolver = get_resolver()
        
        def extract_patterns(resolver, namespace='', prefix=''):
            """Recursively extract URL patterns from resolver."""
            for pattern in resolver.url_patterns:
                if isinstance(pattern, URLResolver):
                    # Handle included URL patterns
                    if pattern.namespace:
                        new_namespace = f"{namespace}:{pattern.namespace}" if namespace else pattern.namespace
                    else:
                        new_namespace = namespace
                    new_prefix = prefix + str(pattern.pattern)
                    extract_patterns(pattern, new_namespace, new_prefix)
                elif isinstance(pattern, URLPattern):
                    # Handle individual URL patterns
                    if pattern.name:  # Only include patterns with names
                        pattern_info = {
                            'pattern': prefix + str(pattern.pattern),
                            'name': pattern.name,
                            'namespace': namespace,
                            'full_name': f"{namespace}:{pattern.name}" if namespace else pattern.name,
                            'view_name': pattern.callback.__name__ if hasattr(pattern.callback, '__name__') else str(pattern.callback),
                            'view_module': pattern.callback.__module__ if hasattr(pattern.callback, '__module__') else 'unknown'
                        }
                        patterns.append(pattern_info)
        
        extract_patterns(root_resolver)
        return patterns

    def _get_url_parameters(self, pattern_str: str) -> Dict[str, Any]:
        """
        Generate test parameters for URL patterns with dynamic segments.

        Args:
            pattern_str: URL pattern string

        Returns:
            Dictionary of parameter names and test values
        """
        params = {}

        # Common parameter mappings
        param_mappings = {
            'pk': 1,
            'id': 1,
            'user_id': self.customer_user.id,
            'venue_id': self.test_venue.id,
            'service_id': self.test_service.id,
            'booking_id': str(self.test_booking.id),
            'payment_id': str(self.test_payment.id),
            'notification_id': self.test_notification.id,
            'review_id': self.test_review.id,
            'discount_id': self.test_service_discount.id,
            'category_id': self.test_category.id,

            # Slug parameters
            'slug': 'test-slug',
            'venue_slug': self.test_venue.slug,
            'service_slug': self.test_service.slug,
            'category_slug': self.test_category.slug,
            'discount_slug': self.test_service_discount.slug,
            'review_slug': self.test_review.slug,

            # Special parameters
            'uidb64': 'test-uid',
            'token': 'test-token',
            'discount_type': 'service',
            'image_type': 'profile',
            'entity_type': 'venue',
            'entity_id': 1,
            'item_id': 1,
            'refund_request_id': str(uuid.uuid4()),
            'refund_id': str(uuid.uuid4()),
            'job_id': 1,
            'event_id': 1,
            'release_id': 1,
            'commit_sha': 'abc123',
        }

        # Extract parameter names from pattern
        # Handle both Django path converters and regex patterns
        path_converters = re.findall(r'<(\w+):(\w+)>', pattern_str)
        simple_params = re.findall(r'<(\w+)>', pattern_str)

        # Add path converter parameters
        for converter_type, param_name in path_converters:
            if param_name in param_mappings:
                params[param_name] = param_mappings[param_name]
            elif converter_type == 'int':
                params[param_name] = 1
            elif converter_type == 'slug':
                params[param_name] = 'test-slug'
            elif converter_type == 'uuid':
                params[param_name] = str(uuid.uuid4())
            else:
                params[param_name] = 'test-value'

        # Add simple parameters
        for param_name in simple_params:
            if param_name in param_mappings:
                params[param_name] = param_mappings[param_name]
            else:
                params[param_name] = 'test-value'

        return params

    def _test_url_pattern(self, pattern_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Test a single URL pattern for basic functionality.

        Args:
            pattern_info: Dictionary containing URL pattern information

        Returns:
            Dictionary containing test results
        """
        result = {
            'url_name': pattern_info['full_name'],
            'pattern': pattern_info['pattern'],
            'view_name': pattern_info['view_name'],
            'status_code': None,
            'exception_type': None,
            'exception_message': None,
            'template_names': [],
            'requires_auth': False,
            'test_status': 'UNKNOWN'
        }

        try:
            # Get URL parameters
            params = self._get_url_parameters(pattern_info['pattern'])

            # Try to reverse the URL
            try:
                if params:
                    url = reverse(pattern_info['full_name'], kwargs=params)
                else:
                    url = reverse(pattern_info['full_name'])
            except Exception as e:
                result['exception_type'] = type(e).__name__
                result['exception_message'] = str(e)
                result['test_status'] = 'REVERSE_ERROR'
                return result

            # Test URL access scenarios
            test_scenarios = [
                ('anonymous', None),
                ('customer', self.customer_user),
                ('provider', self.provider_user),
                ('admin', self.admin_user)
            ]

            best_response = None
            best_status = 999

            for scenario_name, user in test_scenarios:
                try:
                    # Login user if provided
                    if user:
                        self.client.force_login(user)
                    else:
                        self.client.logout()

                    # Make request
                    response = self.client.get(url, follow=True)

                    # Track the best response (lowest non-error status code)
                    if response.status_code < best_status and response.status_code < 500:
                        best_response = response
                        best_status = response.status_code
                        result['auth_scenario'] = scenario_name

                    # If we get a 200, we're done
                    if response.status_code == 200:
                        break

                except Exception as e:
                    # Continue with other scenarios if one fails
                    if not best_response:
                        result['exception_type'] = type(e).__name__
                        result['exception_message'] = str(e)
                    continue

            # Use the best response we got
            if best_response:
                result['status_code'] = best_response.status_code

                # Extract template names
                if hasattr(best_response, 'templates'):
                    result['template_names'] = [t.name for t in best_response.templates if t.name]

                # Determine test status
                if best_response.status_code == 200:
                    result['test_status'] = 'SUCCESS'
                elif best_response.status_code in [301, 302, 303, 307, 308]:
                    result['test_status'] = 'REDIRECT'
                elif best_response.status_code == 403:
                    result['test_status'] = 'FORBIDDEN'
                    result['requires_auth'] = True
                elif best_response.status_code == 404:
                    result['test_status'] = 'NOT_FOUND'
                elif best_response.status_code >= 500:
                    result['test_status'] = 'SERVER_ERROR'
                else:
                    result['test_status'] = 'OTHER'
            else:
                result['test_status'] = 'NO_RESPONSE'

        except Exception as e:
            result['exception_type'] = type(e).__name__
            result['exception_message'] = str(e)
            result['test_status'] = 'EXCEPTION'

        finally:
            # Always logout after test
            self.client.logout()

        return result

    def test_all_url_patterns(self):
        """
        Main test method that tests all discovered URL patterns.

        This test will fail if any URL returns an unhandled exception or
        template error, but continues checking all URLs to provide a
        comprehensive report.
        """
        failed_urls = []
        error_urls = []
        success_count = 0

        print(f"\n{'='*80}")
        print(f"TESTING {len(self.url_patterns)} URL PATTERNS")
        print(f"{'='*80}")

        for i, pattern_info in enumerate(self.url_patterns, 1):
            print(f"[{i:3d}/{len(self.url_patterns)}] Testing: {pattern_info['full_name']}")

            result = self._test_url_pattern(pattern_info)
            self.test_results.append(result)

            # Categorize results
            if result['test_status'] == 'SUCCESS':
                success_count += 1
            elif result['test_status'] in ['EXCEPTION', 'SERVER_ERROR']:
                error_urls.append(result)
                print(f"    ❌ ERROR: {result['exception_type']} - {result['exception_message']}")
            elif result['test_status'] in ['REVERSE_ERROR', 'NO_RESPONSE']:
                failed_urls.append(result)
                print(f"    ⚠️  FAILED: {result['test_status']}")
            else:
                print(f"    ✅ {result['test_status']} ({result['status_code']})")

        # Generate summary report
        self._generate_summary_report()

        # Generate detailed JSON report
        self._generate_json_report()

        # Print summary
        print(f"\n{'='*80}")
        print(f"URL HEALTH CHECK SUMMARY")
        print(f"{'='*80}")
        print(f"Total URLs tested: {len(self.url_patterns)}")
        print(f"Successful: {success_count}")
        print(f"Failed: {len(failed_urls)}")
        print(f"Errors: {len(error_urls)}")
        print(f"Success rate: {(success_count/len(self.url_patterns)*100):.1f}%")

        # Fail the test if there are any unhandled exceptions or server errors
        if error_urls:
            error_messages = []
            for error in error_urls[:5]:  # Show first 5 errors
                error_messages.append(
                    f"URL: {error['url_name']} - {error['exception_type']}: {error['exception_message']}"
                )

            self.fail(
                f"Found {len(error_urls)} URLs with unhandled exceptions or server errors:\n" +
                "\n".join(error_messages) +
                (f"\n... and {len(error_urls) - 5} more errors" if len(error_urls) > 5 else "")
            )

    def _generate_summary_report(self):
        """Generate a summary table of test results."""
        print(f"\n{'='*120}")
        print(f"DETAILED URL TEST RESULTS")
        print(f"{'='*120}")
        print(f"{'URL Name':<40} {'Status':<15} {'Code':<6} {'Auth':<8} {'Templates':<30}")
        print(f"{'-'*120}")

        for result in self.test_results:
            templates = ', '.join(result['template_names'][:2])  # Show first 2 templates
            if len(result['template_names']) > 2:
                templates += '...'

            auth_req = 'Yes' if result['requires_auth'] else 'No'
            status_code = str(result['status_code']) if result['status_code'] else 'N/A'

            print(f"{result['url_name']:<40} {result['test_status']:<15} {status_code:<6} {auth_req:<8} {templates:<30}")

    def _generate_json_report(self):
        """Generate a detailed JSON report of all test results."""
        report_data = {
            'test_summary': {
                'total_urls': len(self.url_patterns),
                'success_count': len([r for r in self.test_results if r['test_status'] == 'SUCCESS']),
                'error_count': len([r for r in self.test_results if r['test_status'] in ['EXCEPTION', 'SERVER_ERROR']]),
                'failed_count': len([r for r in self.test_results if r['test_status'] in ['REVERSE_ERROR', 'NO_RESPONSE']]),
                'test_timestamp': str(timezone.now()) if 'timezone' in globals() else 'unknown'
            },
            'url_results': self.test_results,
            'error_details': [
                r for r in self.test_results
                if r['test_status'] in ['EXCEPTION', 'SERVER_ERROR', 'REVERSE_ERROR']
            ]
        }

        # Save to file
        try:
            import os
            os.makedirs('test_reports', exist_ok=True)
            with open('test_reports/url_healthcheck_report.json', 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
            print(f"\n📄 Detailed JSON report saved to: test_reports/url_healthcheck_report.json")
        except Exception as e:
            print(f"\n⚠️  Could not save JSON report: {e}")

    def test_critical_urls_individually(self):
        """
        Test critical URLs individually to ensure they work properly.

        This provides more detailed error information for important URLs.
        """
        critical_urls = [
            'home',
            'accounts_app:customer_login',
            'accounts_app:service_provider_login',
            'venues_app:venue_list',
            'admin:index',
        ]

        print(f"\n{'='*80}")
        print(f"TESTING CRITICAL URLS INDIVIDUALLY")
        print(f"{'='*80}")

        for url_name in critical_urls:
            try:
                url = reverse(url_name)
                response = self.client.get(url)
                print(f"✅ {url_name}: {response.status_code}")

                # These should not return server errors
                self.assertLess(response.status_code, 500,
                    f"Critical URL {url_name} returned server error: {response.status_code}")

            except Exception as e:
                print(f"❌ {url_name}: {type(e).__name__} - {e}")
                self.fail(f"Critical URL {url_name} failed: {e}")

    def test_admin_urls_require_authentication(self):
        """Test that admin URLs properly require authentication."""
        admin_patterns = [p for p in self.url_patterns if 'admin' in p['full_name'].lower()]

        print(f"\n{'='*80}")
        print(f"TESTING ADMIN URL AUTHENTICATION")
        print(f"{'='*80}")

        for pattern in admin_patterns[:10]:  # Test first 10 admin URLs
            try:
                params = self._get_url_parameters(pattern['pattern'])
                if params:
                    url = reverse(pattern['full_name'], kwargs=params)
                else:
                    url = reverse(pattern['full_name'])

                # Test without authentication
                self.client.logout()
                response = self.client.get(url)

                # Should redirect to login or return 403/401
                self.assertIn(response.status_code, [302, 403, 401],
                    f"Admin URL {pattern['full_name']} should require authentication")

            except Exception as e:
                # Skip URLs that can't be reversed or have other issues
                continue

    def test_url_parameter_handling(self):
        """Test that URLs with parameters handle edge cases properly."""
        parametrized_patterns = [
            p for p in self.url_patterns
            if '<' in p['pattern'] and '>' in p['pattern']
        ]

        print(f"\n{'='*80}")
        print(f"TESTING PARAMETRIZED URL HANDLING")
        print(f"{'='*80}")

        edge_case_params = {
            'pk': [0, -1, 999999],  # Invalid IDs
            'id': [0, -1, 999999],
            'slug': ['', 'invalid-slug', 'very-long-slug-' + 'x' * 100],
        }

        for pattern in parametrized_patterns[:5]:  # Test first 5 parametrized URLs
            try:
                base_params = self._get_url_parameters(pattern['pattern'])

                # Test with edge case parameters
                for param_name, edge_values in edge_case_params.items():
                    if param_name in base_params:
                        for edge_value in edge_values:
                            test_params = base_params.copy()
                            test_params[param_name] = edge_value

                            try:
                                url = reverse(pattern['full_name'], kwargs=test_params)
                                response = self.client.get(url)

                                # Should handle gracefully (not 500)
                                self.assertLess(response.status_code, 500,
                                    f"URL {pattern['full_name']} with {param_name}={edge_value} "
                                    f"returned server error: {response.status_code}")

                            except Exception:
                                # Expected for invalid parameters
                                continue

            except Exception:
                # Skip problematic patterns
                continue

    @classmethod
    def tearDownClass(cls):
        """Clean up after all tests and provide final summary."""
        super().tearDownClass()

        if hasattr(cls, 'test_results') and cls.test_results:
            print(f"\n{'='*80}")
            print(f"FINAL URL HEALTH CHECK SUMMARY")
            print(f"{'='*80}")

            # Count results by status
            status_counts = {}
            for result in cls.test_results:
                status = result['test_status']
                status_counts[status] = status_counts.get(status, 0) + 1

            print("Status breakdown:")
            for status, count in sorted(status_counts.items()):
                percentage = (count / len(cls.test_results)) * 100
                print(f"  {status}: {count} ({percentage:.1f}%)")

            # Show most problematic URLs
            error_results = [
                r for r in cls.test_results
                if r['test_status'] in ['EXCEPTION', 'SERVER_ERROR']
            ]

            if error_results:
                print(f"\nMost problematic URLs:")
                for result in error_results[:3]:
                    print(f"  - {result['url_name']}: {result['exception_type']}")

            print(f"\nTotal URLs discovered and tested: {len(cls.test_results)}")
            print(f"For detailed results, check: test_reports/url_healthcheck_report.json")


# Additional utility functions for URL testing

def get_url_patterns_by_app(app_name: str) -> List[Dict[str, Any]]:
    """
    Get URL patterns for a specific app.

    Args:
        app_name: Name of the Django app

    Returns:
        List of URL pattern dictionaries for the specified app
    """
    test_instance = URLHealthCheckTest()
    test_instance.setUpClass()

    return [
        pattern for pattern in test_instance.url_patterns
        if pattern['namespace'] == app_name or app_name in pattern['view_module']
    ]


def test_single_url(url_name: str, **kwargs) -> Dict[str, Any]:
    """
    Test a single URL pattern.

    Args:
        url_name: Full URL name (with namespace if applicable)
        **kwargs: URL parameters

    Returns:
        Dictionary containing test results
    """
    test_instance = URLHealthCheckTest()
    test_instance.setUpClass()

    # Find the pattern
    pattern_info = None
    for pattern in test_instance.url_patterns:
        if pattern['full_name'] == url_name:
            pattern_info = pattern
            break

    if not pattern_info:
        return {
            'url_name': url_name,
            'test_status': 'NOT_FOUND',
            'exception_message': f'URL pattern {url_name} not found'
        }

    return test_instance._test_url_pattern(pattern_info)


if __name__ == '__main__':
    """
    Allow running this module directly for quick URL health checks.

    Usage:
        python tests/test_url_healthcheck.py
    """
    import django
    from django.conf import settings
    from django.test.utils import get_runner

    if not settings.configured:
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
        django.setup()

    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(['tests.test_url_healthcheck'])

    if failures:
        exit(1)
