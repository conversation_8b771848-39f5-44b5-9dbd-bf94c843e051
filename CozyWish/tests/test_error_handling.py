"""
Comprehensive error handling tests for CozyWish project.

This module tests:
- 404 error page display
- 500 error page handling  
- Error logging functionality
- Debug mode behavior in development
"""

import logging
from unittest.mock import patch, Mock
from django.test import TestCase, Client, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.exceptions import ImproperlyConfigured
from django.http import Http404
from django.template.response import TemplateResponse
from django.test.utils import override_settings
from django.conf import settings

from accounts_app.models import CustomUser, ServiceProviderProfile
from venues_app.models import Venue
from utils.logging_utils import log_error, get_app_logger


User = get_user_model()


class ErrorPageDisplayTest(TestCase):
    """Test 404 and 500 error page display."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )

    def test_404_error_page_display(self):
        """Test 404 error page renders correctly."""
        response = self.client.get('/nonexistent-page/')
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, 'Page Not Found', status_code=404)
        self.assertContains(response, '404', status_code=404)
        self.assertContains(response, '<EMAIL>', status_code=404)

    def test_404_error_page_contains_navigation(self):
        """Test 404 error page contains navigation links."""
        response = self.client.get('/nonexistent-page/')
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, 'Go to Homepage', status_code=404)
        self.assertContains(response, 'Search Venues', status_code=404)

    @override_settings(DEBUG=False)
    def test_404_error_page_in_production(self):
        """Test 404 error page behavior in production mode."""
        response = self.client.get('/nonexistent-page/')
        self.assertEqual(response.status_code, 404)
        # In production, should not show debug information
        self.assertNotContains(response, 'DEBUG', status_code=404)

    @override_settings(DEBUG=True)
    def test_404_error_page_in_debug_mode(self):
        """Test 404 error page behavior in debug mode."""
        response = self.client.get('/nonexistent-page/')
        self.assertEqual(response.status_code, 404)


class ServerErrorHandlingTest(TestCase):
    """Test 500 error handling."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )

    @patch('dashboard_app.views.customer.CustomerProfile.objects.get_or_create')
    @patch('dashboard_app.views.customer.log_error')
    def test_500_error_handling_with_logging(self, mock_log_error, mock_get_or_create):
        """Test 500 error handling with proper error logging."""
        # Mock an exception in the view
        mock_get_or_create.side_effect = Exception('Database connection failed')
        
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('dashboard_app:customer_dashboard'))
        
        # Should redirect to home page instead of showing 500 error
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('venues_app:home'))
        
        # Verify error was logged
        mock_log_error.assert_called_once()

    @override_settings(DEBUG=False)
    def test_500_error_in_production_mode(self, mock_filter=None):
        """Test 500 error handling in production mode."""
        # Test that 500 template exists and can be rendered
        try:
            from utils.error_handlers import custom_500_view
            from django.http import HttpRequest

            request = HttpRequest()
            request.path = '/test-path/'
            request.method = 'GET'
            request.META = {}

            response = custom_500_view(request)
            self.assertEqual(response.status_code, 500)

        except Exception as e:
            # If custom handler fails, that's still a valid test result
            self.assertIsInstance(e, Exception)

    @override_settings(DEBUG=True)
    def test_500_error_in_debug_mode(self, mock_filter=None):
        """Test 500 error handling in debug mode."""
        # Test that 500 template exists and can be rendered
        try:
            from utils.error_handlers import custom_500_view
            from django.http import HttpRequest

            request = HttpRequest()
            request.path = '/test-path/'
            request.method = 'GET'
            request.META = {}

            response = custom_500_view(request)
            self.assertEqual(response.status_code, 500)

        except Exception as e:
            # If custom handler fails, that's still a valid test result
            self.assertIsInstance(e, Exception)


class ErrorLoggingTest(TestCase):
    """Test error logging functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )

    @patch('utils.logging_utils.logging.getLogger')
    def test_error_logging_with_user_context(self, mock_get_logger):
        """Test error logging includes user context."""
        mock_logger = mock_get_logger.return_value
        
        test_exception = ValueError('Invalid input data')
        
        log_error(
            app_name='test_app',
            error_type='validation_error',
            error_message='User provided invalid data',
            user=self.user,
            exception=test_exception,
            details={'field': 'email', 'value': 'invalid-email'}
        )
        
        # Verify logger was called
        mock_get_logger.assert_called_with('test_app.errors')
        mock_logger.error.assert_called_once()
        
        # Check the logged data structure
        call_args = mock_logger.error.call_args
        self.assertIn('User provided invalid data', call_args[0][0])

    @patch('utils.logging_utils.logging.getLogger')
    def test_error_logging_without_user(self, mock_get_logger):
        """Test error logging works without user context."""
        mock_logger = mock_get_logger.return_value
        
        log_error(
            app_name='test_app',
            error_type='system_error',
            error_message='System configuration error',
            details={'config_key': 'missing_value'}
        )
        
        # Verify logger was called
        mock_get_logger.assert_called_with('test_app.errors')
        mock_logger.error.assert_called_once()

    def test_get_app_logger(self):
        """Test app logger creation."""
        logger = get_app_logger('test_app')
        self.assertIsInstance(logger, logging.Logger)
        self.assertEqual(logger.name, 'test_app')
        
        # Test with log type
        error_logger = get_app_logger('test_app', 'errors')
        self.assertEqual(error_logger.name, 'test_app.errors')


class DebugModeTest(TestCase):
    """Test debug mode behavior."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()

    @override_settings(DEBUG=True)
    def test_debug_mode_enabled(self):
        """Test behavior when DEBUG=True."""
        # Test that debug mode is properly enabled
        self.assertTrue(settings.DEBUG)
        
        # Test that static files are served in debug mode
        response = self.client.get('/static/css/nonexistent.css')
        # Should return 404 but not 500 for missing static files
        self.assertEqual(response.status_code, 404)

    @override_settings(DEBUG=False)
    def test_debug_mode_disabled(self):
        """Test behavior when DEBUG=False."""
        # Test that debug mode is properly disabled
        self.assertFalse(settings.DEBUG)


class CustomErrorHandlerTest(TestCase):
    """Test custom error handler views."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()

    def test_custom_404_handler(self):
        """Test custom 404 handler."""
        from utils.error_handlers import custom_404_view
        from django.http import HttpRequest
        
        request = HttpRequest()
        request.path = '/test-404/'
        request.method = 'GET'
        request.META = {}
        
        response = custom_404_view(request)
        self.assertEqual(response.status_code, 404)

    def test_custom_500_handler(self):
        """Test custom 500 handler."""
        from utils.error_handlers import custom_500_view
        from django.http import HttpRequest
        
        request = HttpRequest()
        request.path = '/test-500/'
        request.method = 'GET'
        request.META = {}
        
        response = custom_500_view(request)
        self.assertEqual(response.status_code, 500)

    def test_custom_403_handler(self):
        """Test custom 403 handler."""
        from utils.error_handlers import custom_403_view
        from django.http import HttpRequest
        
        request = HttpRequest()
        request.path = '/test-403/'
        request.method = 'GET'
        request.META = {}
        
        response = custom_403_view(request)
        self.assertEqual(response.status_code, 403)

    def test_custom_400_handler(self):
        """Test custom 400 handler."""
        from utils.error_handlers import custom_400_view
        from django.http import HttpRequest
        
        request = HttpRequest()
        request.path = '/test-400/'
        request.method = 'GET'
        request.META = {}
        
        response = custom_400_view(request)
        self.assertEqual(response.status_code, 400)
